import {
  roundNumberBaseOnCurrency,
  separateThousandsBaseOnCurrency,
  separateThousandsWith2DecimalPlace,
} from '~/utilities/numberUtils.js';

const numberFormatter = {
  format: vi.fn(),
};

describe('separateThousandsWith2DecimalPlace', () => {
  test('should return the formatted result from numberFormatter', () => {
    const input = 9876.54;
    const formattedResult = '9,876.54';
    numberFormatter.format.mockReturnValueOnce(formattedResult);

    const result = separateThousandsWith2DecimalPlace(input);
    expect(result).toBe(formattedResult);
  });
});

describe('separateThousandsBaseOnCurrency', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should format input as per currency for JPY', () => {
    const input = 1_234_567.89;
    const currency = 'JPY';
    const expectedFormattedValue = '1,234,568';

    const result = separateThousandsBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should format input as per currency for KRW', () => {
    const input = 9_876_543.21;
    const currency = 'KRW';
    const expectedFormattedValue = '9,876,543';

    const result = separateThousandsBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should use separateThousandsWith2DecimalPlace for other currencies', () => {
    const input = 12_345.67;
    const currency = 'USD';
    const expectedFormattedValue = '12,345.67';

    const result = separateThousandsBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });
});

describe('roundNumberBaseOnCurrency', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should return 0 when input is not a number', () => {
    const input = 'aaa';
    const currency = 'JPY';
    const expectedFormattedValue = 0;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 50 for KRW', () => {
    const input = 9_876_543.001;
    const currency = 'KRW';
    const expectedFormattedValue = 9_876_544;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 1 for TWD', () => {
    const input = 9_876_543.001;
    const currency = 'TWD';
    const expectedFormattedValue = 9_876_543;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 1 for JPY', () => {
    const input = 9_876_543.001;
    const currency = 'JPY';
    const expectedFormattedValue = 9_876_543;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 0.01 for SGD', () => {
    const input = 9_876_543.001;
    const currency = 'SGD';
    const expectedFormattedValue = 9_876_543.01;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 0.01 for GBP', () => {
    const input = 9_876_543.001;
    const currency = 'GBP';
    const expectedFormattedValue = 9_876_543.01;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 0.01 for HKD', () => {
    const input = 9_876_543.011;
    const currency = 'HKD';
    const expectedFormattedValue = 9_876_543.01;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 0.01 for AUD', () => {
    const input = 9_876_543.011;
    const currency = 'AUD';
    const expectedFormattedValue = 9_876_543.02;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest 0.01 for MYR', () => {
    const input = 9_876_543.011;
    const currency = 'MYR';
    const expectedFormattedValue = 9_876_543.02;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });

  test('should return number round up to nearest .05 for other currencies', () => {
    const input = 9_876_543.001;
    const currency = 'USD';
    const expectedFormattedValue = 9_876_543.05;

    const result = roundNumberBaseOnCurrency(input, currency);

    expect(result).toBe(expectedFormattedValue);
  });
});
