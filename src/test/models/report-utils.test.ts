import axios from 'axios';
import { Mock } from 'vitest';

import { AesUtils } from '~/models/aesUtils.js';
import { ReportUtils } from '~/models/reportUtils.js';

import CountryISOService from '~/services/countryISO-service.js';

import { CustomManifestUtils } from '~/utilities/customManifestUtils.js';

import { CurrencyConversionService } from '../../main/services/currency-conversion-service.js';
import { OperationHubService } from '../../main/services/operation-hub.service.js';
import { DateUtils } from '../../main/utilities/dateUtils.js';
import { ExcelUtils } from '../../main/utilities/excelUtils.js';

vi.mock('~/utilities/excelUtils.js', () => ({
  ExcelUtils: {
    generateSheetData: vi.fn(() => [['mocked data']]),
    createBufferXLSX: vi.fn(() => Buffer.from('mocked buffer')),
    createBuffer: vi.fn(() => Buffer.from('mocked buffer')),
  },
}));
vi.mock('~/models/aesUtils.js');
vi.mock('~/services/currency-conversion-service.js');
vi.mock('~/utilities/customManifestUtils.js');
vi.mock('~/services/countryISO-service.js');

describe('generatePdInvoice function', () => {
  it('should send payload to pdf-func to generate pd', async () => {
    // Arrange
    const data = {
      merchant_type: 'Entity within SIA Group',
      invoiceNumber: '****************',
      invoice_generation_datetime: '2024-11-15T03:45:26.931Z',
      invoice_date: '31 Oct 2024',
      invoiceMonth: 'Oct 2024',
      invoiceRateCurrency: 'AUD',
      creditTerm: 'Immediate',
      routings: [],
      surcharges: [],
      taxZone: 'Singapore',
    };
    const merchant = {
      merchant_name: 'iherb',
      merchant_account_number: 'US00001',
      phone_number: '**********',
      merchant_address_line1: 'Random address',
      taxType: 'GST',
      taxCountry: 'Singapore',
      bankDetail: {
        account_type: 'SG SGD Account',
        title: 'For SGD Payment',
        bank_detail:
          'DBS Bank Limited\nFavouring Singapore Airlines Ltd\nBank Code: 7171\nAccount No: 072-********** \nSWIFT Code: DBSSSGSG',
      },
    };
    const taxInfoArr = [{ taxCountry: 'Singapore' }];
    axios.post = vi
      .fn()
      .mockResolvedValue({ data: { success: true, pdfBuffer: Buffer.from('PLS') } });

    // Act
    const res = await ReportUtils.generatePdInvoice(data, merchant, taxInfoArr);

    // Assert
    expect(axios.post).toHaveBeenCalled();
    expect(res).toHaveProperty('success', true);
  });
});

describe('exportCustomBrokerManifestLMGUSTemplate', () => {
  const mockParcels = [
    // Provide mock parcels data here
    {
      item: [],
    },
  ];
  const mockMawb = {
    _partitionKey: 'mockMawbNo',
    operation_hub: 'mockOperationHub',
  };
  const mockContainers = [
    // Provide mock containers data here
  ];
  const mockSta = '2023-10-10';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should generate the manifest correctly', async () => {
    const mockOperationHub = { name: 'mockOperationHub' };
    const mockExchangeRates = [
      // Provide mock exchange rates data here
    ];
    const mockDateOfArrival = '10102023';
    const mockBuffer = Buffer.from('mockBuffer');

    vi.spyOn(OperationHubService, 'getOperationHubByName').mockResolvedValue(mockOperationHub);
    vi.spyOn(CurrencyConversionService, 'getAllExchRates').mockResolvedValue(mockExchangeRates);
    vi.spyOn(DateUtils, 'format').mockReturnValue(mockDateOfArrival);
    vi.spyOn(ExcelUtils, 'createBuffer').mockReturnValue(mockBuffer);

    const result = await ReportUtils.exportCustomBrokerManifestLMGUSTemplate(
      mockParcels,
      mockMawb as any,
      mockContainers,
      mockSta,
    );

    expect(OperationHubService.getOperationHubByName).toHaveBeenCalledWith(mockMawb.operation_hub);
    expect(CurrencyConversionService.getAllExchRates).toHaveBeenCalled();
    expect(DateUtils.format).toHaveBeenCalledWith(
      'MMDDYYYY',
      new Date(mockSta).getFullYear(),
      new Date(mockSta).getMonth() + 1,
      new Date(mockSta).getDate(),
    );
    expect(ExcelUtils.createBuffer).toHaveBeenCalledWith(
      'Manifest',
      expect.anything(),
      expect.anything(),
    );
    expect(result).toBe(mockBuffer);
  });

  // Add more test cases as needed
});

describe('createDTExcelBuffer', () => {
  it('should generate an Excel buffer with the correct structure', () => {
    const invoiceData = {
      invoiceCurrency: 'USD',
      parcels: [
        {
          id: '123',
          tracking_id: 'TRK123',
          merchant_order_no: 'ORD123',
          rateZone: 'A',
          taxCode: 'TX1',
          totalActualTax: 10.5,
          ddp: { type: 'Customs', taxCode: 'TX2', rate: 5, tax: 1 },
        },
      ],
      totalTaxInMerchantRateCurrency: 10.5,
      totalSurcharge: 5,
      totalDue: 15.5,
      totalSurchargeTax: 1,
    };

    const merchantInvoiceData = {
      merchant_name: 'Merchant A',
      merchantAccountNo: 'ACC123',
      invoicingInfo: {},
    };

    const invoiceNumber = 'INV123';

    const result = ReportUtils.createDTExcelBuffer(invoiceData, merchantInvoiceData, invoiceNumber);

    expect(ExcelUtils.generateSheetData).toHaveBeenCalled();
    expect(ExcelUtils.createBufferXLSX).toHaveBeenCalledWith(
      `${invoiceNumber} detail`,
      expect.any(Array),
    );
    expect(result).toBeInstanceOf(Buffer);
  });
});

describe('ReportUtils.getFieldsForMY', () => {
  const flightNo = 'SQ123';
  const STA = '2024-06-23T10:00:00Z';
  const STD = '2024-06-23T12:00:00Z';
  const currency = 'USD';
  const allConversionRates = [{ from: 'USD', to: 'MYR', destination_currency: 4.5 }];
  const customBroker = { custom_broker_code: 'CB_NJV_KUL' };
  const mawbInfo = { mawb_date: '2024-06-23T09:00:00Z' };
  const cat1MerchantList = [{ merchant_id: 1 }];

  beforeEach(() => {
    vi.clearAllMocks();
    (AesUtils.CrtCounterDecrypt as Mock).mockImplementation((v) => (v ? `decrypted(${v})` : v));
    (CurrencyConversionService.convertCurrency as Mock).mockImplementation((rates, from, to) => {
      if (from === to) return { destination_currency: 1 };

      return rates.find((r) => r.from === from && r.to === to) || { destination_currency: 1 };
    });
    (CountryISOService.getCountryCode2 as Mock).mockImplementation((c) => (c ? `CC2(${c})` : ''));
    (CustomManifestUtils.getConsigneeEmail as Mock).mockReturnValue('<EMAIL>');
  });

  it('should return NJV_KUL fields when custom_broker_code is CB_NJV_KUL', () => {
    const fields = ReportUtils.getFieldsForMY(
      flightNo,
      STA,
      STD,
      currency,
      allConversionRates,
      customBroker,
      mawbInfo,
      cat1MerchantList,
    );
    expect(Array.isArray(fields)).toBe(true);
    expect(fields[0].label).toBe('Client Name');
    expect(fields.find((f) => f.label === 'MAWB No.')).toBeDefined();
    expect(fields.find((f) => f.label === 'HS Code')).toBeDefined();

    // Test value functions
    const row = {
      parcel: {
        mawb: { _partitionKey: 'MAWB123' },
        op_hub: { airport_code: 'KUL' },
        origin_country: 'SG',
        destination_group: 'DEST_GROUP_KUL',
        tracking_id: 'TRACK123',
        gaylord_no: 'BAG123',
        weight_unit: 'kg',
        weight: '10',
        merchant: {
          merchant_name: 'merchant1',
          street: 'street1',
          postal_code: '12345',
          city: 'city1',
          state: 'state1',
          country: 'SG',
        },
        country: 'MY',
        recipient_first_name: 'John',
        recipient_last_name: 'Doe',
        recipient_addressline1: 'Addr1',
        recipient_addressline2: 'Addr2',
        recipient_addressline3: 'Addr3',
        postcode: '54321',
        city_suburb: 'Kuala Lumpur',
        state: 'Selangor',
      },
      SKU: 'SKU123',
      description: 'desc',
      total_declared_value: '100',
      hs_code: 'HS123',
    };
    const mawbNoField = fields.find((f) => f.label === 'MAWB No.');
    expect(mawbNoField.value(row)).toBe('MAWB123');
    const consignorNameField = fields.find((f) => f.label === 'Consignor Name');
    expect(consignorNameField.value(row)).toBe('decrypted(merchant1)');
    const consigneeNameField = fields.find((f) => f.label === 'Consignee Name');
    expect(consigneeNameField.value(row)).toBe('John Doe');
    const goodsSkuField = fields.find((f) => f.label === 'Goods SKU');
    expect(goodsSkuField.value(row)).toBe('SKU123');
    const descriptionField = fields.find((f) => f.label === 'Description of Goods');
    expect(descriptionField.value(row)).toBe('desc');
    const hsCodeField = fields.find((f) => f.label === 'HS Code');
    expect(hsCodeField.value(row)).toBe('HS123');
  });

  it('should return default fields when custom_broker_code is not CB_NJV_KUL', () => {
    const customBroker2 = { custom_broker_code: 'OTHER' };
    const fields = ReportUtils.getFieldsForMY(
      flightNo,
      STA,
      STD,
      currency,
      allConversionRates,
      customBroker2,
      mawbInfo,
      cat1MerchantList,
    );
    expect(Array.isArray(fields)).toBe(true);
    expect(fields[0].label).toBe('LOADING PORT');
    expect(fields.find((f) => f.label === 'MAWB NO')).toBeDefined();
    expect(fields.find((f) => f.label === 'ITEM VALUE (Local)')).toBeDefined();

    // Test value functions
    const row = {
      parcel: {
        mawb: { mawb_no: 'MAWB456', tracking_status: [{}] },
        op_hub: { airport_code: 'KUL', country: 'SG' },
        merchant: {
          merchant_name: 'merchant2',
          street: 'street2',
          postal_code: '67890',
          city: 'city2',
          state: 'state2',
          country: 'SG',
          phone_number: '123456789',
        },
        country: 'MY',
        destination_group: 'DEST_GROUP_KUL',
        tracking_id: 'TRACK456',
        gaylord_no: 'BAG456',
        weight_unit: 'lb',
        weight: '22',
        recipient_first_name: 'Jane',
        recipient_last_name: 'Smith',
        recipient_addressline1: 'AddrA',
        recipient_addressline2: 'AddrB',
        recipient_addressline3: 'AddrC',
        postcode: '98765',
        city_suburb: 'Petaling Jaya',
        state: 'Selangor',
        merchant_declared_currency: 'USD',
        length: '10',
        width: '20',
        height: '30',
        dimensions_unit: 'cm',
        incoterm: 'FOB',
      },
      description: 'desc2',
      total_declared_value: 200,
      hs_code: 'HS456',
    };
    const mawbNoField = fields.find((f) => f.label === 'MAWB NO');
    expect(mawbNoField.value(row)).toBe('MAWB456');
    const shipperCountryCodeField = fields.find((f) => f.label === 'SHIPPER COUNTRY CODE');
    expect(shipperCountryCodeField.value(row)).toBe('decrypted(SG)');
    const consigneeNameField = fields.find((f) => f.label === 'CONSIGNEE NAME');
    expect(consigneeNameField.value(row)).toBe('Jane Smith');
    const consigneeEmailField = fields.find((f) => f.label === 'CONSIGNEE EMAIL');
    expect(consigneeEmailField.value(row)).toBe('<EMAIL>');
    const itemValueLocalField = fields.find((f) => f.label === 'ITEM VALUE (Local)');
    expect(
      itemValueLocalField.value({
        ...row,
        parcel: { ...row.parcel, merchant_declared_currency: 'USD' },
        total_declared_value: 100,
      }),
    ).toBe(450); // 100 * 4.5
    const productCodeField = fields.find((f) => f.label === 'Product Code');
    expect(productCodeField.value(row)).toBe('HS456');
  });

  it('should handle missing/undefined values gracefully', () => {
    const customBroker2 = { custom_broker_code: 'OTHER' };
    const fields = ReportUtils.getFieldsForMY(
      flightNo,
      STA,
      STD,
      currency,
      allConversionRates,
      customBroker2,
      mawbInfo,
      cat1MerchantList,
    );
    const row = { parcel: {} };
    fields.forEach((field) => {
      if (typeof field.value === 'function') {
        expect(() => field.value(row)).not.toThrow();
      }
    });
  });
});
