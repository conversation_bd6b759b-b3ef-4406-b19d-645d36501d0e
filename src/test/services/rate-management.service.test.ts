/* eslint-disable import/order */
import { AesUtils } from '~/models/aesUtils.js';

import XLSX, { WorkSheet } from 'xlsx';

import { RateManagementService } from '~/services/rate-management.service.js';

import CountryISOService from '~/services/countryISO-service.js';

import { Daos } from '~/daos/index.js';

// eslint-disable-next-line import/first
import { checkCellsEmpty, getCellValue } from '~/utilities/rate-sheet.util.js';
import { MerchantService } from '~/services/merchant-service.js';
import { RateServices } from '~/services/rate-services.js';
import { CommonUtils } from '~/utilities/commonUtils.js';

vi.mock('~/utilities/rate-sheet.util.js', () => ({
  getCellValue: vi.fn(),
  checkCellsEmpty: vi.fn(),
}));

describe('test validateRateSheetDataV1', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  const rateSheet = {};

  it('should return false for not correct number of columns', async () => {
    // Arrange
    const range = {
      e: {
        r: 3,
        c: 3,
      },
    };
    XLSX.utils.decode_range = vi.fn().mockReturnValueOnce(range);
    RateManagementService.validateHeaders = vi.fn().mockResolvedValueOnce(undefined);

    // Act
    const result = await RateManagementService.validateRateSheetDataV1(rateSheet);

    // Assert
    expect(result).toHaveProperty('success', false);
    expect(RateManagementService.validateHeaders).not.toHaveBeenCalled();
  });

  it('should return false for invalid headers', async () => {
    // Arrange
    const range = {
      e: {
        r: 3,
        c: 7,
      },
    };
    XLSX.utils.decode_range = vi.fn().mockReturnValueOnce(range);
    RateManagementService.validateHeaders = vi.fn().mockReturnValueOnce({
      success: false,
    });

    // Act
    const result = await RateManagementService.validateRateSheetDataV1(rateSheet);

    // Assert
    expect(RateManagementService.validateHeaders).toHaveBeenCalledOnce();
    expect(result).toHaveProperty('success', false);
  });

  it('should return false if there is empty cell', async () => {
    // Arrange
    const range = {
      e: {
        r: 3,
        c: 7,
      },
    };
    XLSX.utils.decode_range = vi.fn().mockReturnValueOnce(range);
    RateManagementService.validateHeaders = vi.fn().mockReturnValueOnce({
      success: true,
    });
    vi.mocked(checkCellsEmpty).mockReturnValueOnce({
      success: false,
    });

    // Act
    const result = await RateManagementService.validateRateSheetDataV1(rateSheet);

    // Assert
    expect(RateManagementService.validateHeaders).toHaveBeenCalledOnce();
    expect(checkCellsEmpty).toHaveBeenCalledOnce();
    expect(result).toHaveProperty('success', false);
  });
});

describe('test validateDataV2', () => {
  const code2List = new Set(['vn', 'sg']);

  it('should throw error for invalid country', () => {
    // Arrange
    const rateSheetArr = [
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['Parcel Weight', 'fr', 'vn', undefined, undefined, undefined],
      ['Service Option', 'data2', 'data3', 'data4', 'data5', 'data6'],
    ];

    // Act
    let result;

    try {
      RateManagementService.validateDataV2(rateSheetArr, code2List);
    } catch (error) {
      result = error.message;
    }

    // Assert
    expect(result).toEqual('Error: Invalid country: fr');
  });

  it('should throw error for invalid servie option', () => {
    // Arrange
    const rateSheetArr = [
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['Parcel Weight', 'sg', 'vn', undefined, undefined, undefined],
      ['Service Option', 'invalid', 'invalid', undefined, undefined, undefined],
    ];

    // Act
    let result;

    try {
      RateManagementService.validateDataV2(rateSheetArr, code2List);
    } catch (error) {
      result = error.message;
    }

    // Assert
    expect(result).toEqual('Error: Invalid Service Option applied for sg');
  });

  it('should throw error for invalid rates', () => {
    // Arrange
    const rateSheetArr = [
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['data1', 'data2', 'data3', 'data4', 'data5', 'data6'],
      ['Parcel Weight', 'sg', 'vn', undefined, undefined, undefined],
      ['Service Option', 'postal', 'standard', undefined, undefined, undefined],
      ['Incremental', 'invalid', 'invalid', undefined, undefined, undefined],
    ];

    // Act
    let result;

    try {
      RateManagementService.validateDataV2(rateSheetArr, code2List);
    } catch (error) {
      result = error.message;
    }

    // Assert
    expect(result).toEqual('Error: Invalid rates');
  });
});

describe('test validateRateSheetDataV2', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  const rateSheet = {};
  const serviceOption = 'B2C';

  it('should pass validations', async () => {
    // Arrange
    const range = {
      e: {
        r: 1,
        c: 1,
      },
    };
    XLSX.utils.decode_range = vi.fn().mockReturnValueOnce(range);
    vi.mocked(getCellValue).mockImplementation(() => 'mock value');
    CountryISOService.getCountriesList = vi.fn().mockResolvedValueOnce([
      {
        currency: 'VND',
        codeAlpha2: 'VN',
      },
      {
        currency: 'SGD',
        codeAlpha2: 'SG',
      },
    ]);
    RateManagementService.validateDataV2 = vi.fn();

    // Act
    const result = await RateManagementService.validateRateSheetDataV2(rateSheet, serviceOption);

    // Assert
    expect(RateManagementService.validateDataV2).toHaveBeenCalledOnce();
    expect(result).toHaveProperty('success', true);
  });

  it('should return false for invalid weight unit', async () => {
    // Arrange
    const range = {
      e: {
        r: 6,
        c: 2,
      },
    };
    XLSX.utils.decode_range = vi.fn().mockReturnValueOnce(range);
    vi.mocked(getCellValue).mockImplementation(() => 'mock value');
    CountryISOService.getCountriesList = vi.fn().mockResolvedValueOnce([
      {
        currency: 'VND',
        codeAlpha2: 'VN',
      },
      {
        currency: 'SGD',
        codeAlpha2: 'SG',
      },
    ]);

    // Act
    const result = await RateManagementService.validateRateSheetDataV2(rateSheet, serviceOption);

    // Assert
    expect(result).toHaveProperty('success', false);
  });
});

describe('test validateB2CRateSheet', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  const rateSheet = {};
  const data = {
    merchantAccNoFromRequest: 'MERCHANTACCNO0001',
  };

  it('should fail if there is missing metadata', async () => {
    // Arrange
    vi.mocked(getCellValue).mockReturnValueOnce(undefined);
    vi.mocked(getCellValue).mockReturnValue('mock value');

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(result).toHaveProperty('success', false);
  });

  it('should fail for invalid chargeable weight', async () => {
    // Arrange
    vi.mocked(getCellValue).mockReturnValue('mock value');

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(result).toHaveProperty('success', false);
  });

  it('should fail for mismatching merchant account numbers', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 6) {
        return 'Gross weight';
      }

      return 'mock value';
    });

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(result).toHaveProperty('success', false);
  });

  it('should fail for mismatching merchant account numbers', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 4) {
        return data.merchantAccNoFromRequest;
      }

      if (callCount === 6) {
        return 'Gross weight';
      }

      return 'mock value';
    });

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(result).toHaveProperty('success', false);
  });

  it('should fail if no merchant exists', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 4) {
        return data.merchantAccNoFromRequest;
      }

      if (callCount === 6) {
        return 'Gross weight';
      }

      return 'mock value';
    });
    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(false);

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(MerchantService.checkMerchantExist).toHaveBeenCalledOnce();
    expect(result).toHaveProperty('success', false);
  });

  it('should fail if validity dates are invalid', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 4) {
        return data.merchantAccNoFromRequest;
      }

      if (callCount === 6) {
        return 'Gross weight';
      }

      return 'mock value';
    });
    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(MerchantService.checkMerchantExist).toHaveBeenCalledOnce();
    expect(result).toHaveProperty('success', false);
  });

  it('should fail if valid from date greater than valid to date', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 4) {
        return data.merchantAccNoFromRequest;
      }

      if (callCount === 5) {
        return '2024-08-12T15:30:00Z';
      }

      if (callCount === 7) {
        return '2024-08-10T15:30:00Z';
      }

      if (callCount === 6) {
        return 'Gross weight';
      }

      return 'mock value';
    });
    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(MerchantService.checkMerchantExist).toHaveBeenCalledOnce();
    expect(result).toHaveProperty('success', false);
  });

  it('should fail data validation', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 4) {
        return data.merchantAccNoFromRequest;
      }

      if (callCount === 5 || callCount === 7) {
        return '2024-08-12T15:30:00Z';
      }

      if (callCount === 6) {
        return 'Gross weight';
      }

      return 'mock value';
    });
    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);
    RateManagementService.validateRateSheetDataV1 = vi.fn().mockResolvedValueOnce({
      success: false,
      message: 'Fail',
    });
    RateManagementService.validateRateSheetDataV2 = vi.fn();

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(MerchantService.checkMerchantExist).toHaveBeenCalledOnce();
    expect(RateManagementService.validateRateSheetDataV1).toHaveBeenCalledOnce();
    expect(RateManagementService.validateRateSheetDataV2).not.toHaveBeenCalled();
    expect(result).toHaveProperty('success', false);
  });

  it('should pass validation for version 1', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 4) {
        return data.merchantAccNoFromRequest;
      }

      if (callCount === 5 || callCount === 7) {
        return '2024-08-12T15:30:00Z';
      }

      if (callCount === 6) {
        return 'Gross weight';
      }

      return 'mock value';
    });
    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);
    RateManagementService.validateRateSheetDataV1 = vi.fn().mockResolvedValueOnce({
      success: true,
      message: 'Pass',
    });
    RateManagementService.validateRateSheetDataV2 = vi.fn();

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(MerchantService.checkMerchantExist).toHaveBeenCalledOnce();
    expect(RateManagementService.validateRateSheetDataV1).toHaveBeenCalledOnce();
    expect(RateManagementService.validateRateSheetDataV2).not.toHaveBeenCalled();
    expect(result).toHaveProperty('success', true);
  });

  it('should pass validation for version 2', async () => {
    // Arrange
    let callCount = 0;
    vi.mocked(getCellValue).mockImplementation(() => {
      callCount++;

      if (callCount === 2) {
        return 'Currency';
      }

      if (callCount === 4) {
        return data.merchantAccNoFromRequest;
      }

      if (callCount === 5 || callCount === 7) {
        return '2024-08-12T15:30:00Z';
      }

      if (callCount === 6) {
        return 'Gross weight';
      }

      if (callCount === 9) {
        return;
      }

      return 'mock value';
    });
    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);
    RateManagementService.validateRateSheetDataV1 = vi.fn();
    RateManagementService.validateRateSheetDataV2 = vi.fn().mockResolvedValueOnce({
      success: true,
      message: 'Pass',
    });

    // Act
    const result = await RateManagementService.validateB2CRateSheet(rateSheet, data);

    // Assert
    expect(getCellValue).toHaveBeenCalledTimes(9);
    expect(MerchantService.checkMerchantExist).toHaveBeenCalledOnce();
    expect(RateManagementService.validateRateSheetDataV1).not.toHaveBeenCalled();
    expect(RateManagementService.validateRateSheetDataV2).toHaveBeenCalledOnce();
    expect(result).toHaveProperty('success', true);
    expect(result).toHaveProperty('data', expect.objectContaining({ version: 2 }));
  });
});

describe('test deactivateMerchantRate', () => {
  it('should return patched rate merchant', async () => {
    // Arrange
    const rateId = 'merchant-rate-id';
    const partitionKey = 'merchant-account-number-0001';
    const expected = { id: rateId };
    Daos.rateMerchant.patch = vi.fn().mockResolvedValueOnce(expected);

    // Act
    const result = await RateManagementService.deactivateMerchantRate(rateId, partitionKey);

    // Assert
    expect(result).toBe(expected);
  });
});

describe('test deactivateMerchantRateItems', () => {
  const rateId = 'merchant-rate-id';
  const encryptedNumber = 'encrypted-merchant-account-number';

  it('should return undefined if no merchant rate items found', async () => {
    // Arrange
    Daos.rateMerchant.find = vi.fn().mockResolvedValueOnce([]);

    // Act
    const result = await RateManagementService.deactivateMerchantRateItems(rateId, encryptedNumber);

    // Assert
    expect(Daos.rateMerchant.find).toHaveBeenCalledOnce();
    expect(result).toBe(undefined);
  });

  it('should return undefined for unexpected error', async () => {
    // Arrange
    Daos.rateMerchant.find = vi.fn().mockRejectedValueOnce(undefined);

    // Act
    const result = await RateManagementService.deactivateMerchantRateItems(rateId, encryptedNumber);

    // Assert
    expect(Daos.rateMerchant.find).toHaveBeenCalledOnce();
    expect(result).toBe(undefined);
  });
});

describe('test createNewActiveMerchantRate', () => {
  const rateSheetArr = [
    [undefined],
    undefined,
    undefined,
    [undefined, 'asdf', 'MERCHANTACCNO001'],
    [undefined, undefined, '2024-08-12T15:30:00Z', undefined, '2024-08-12T15:30:00Z'],
    [undefined, undefined, 'Gross Weight'],
    [undefined, undefined, 'Currency'],
    [undefined, undefined, '12'],
    [undefined, undefined, 'kg'],
    undefined,
    [undefined, 'destinationCode'],
    [undefined, 'serviceOption'],
    [undefined, '0.5'],
  ];

  it('should return new merchant rate id after creating version 1', async () => {
    // Arrange
    const expected = { id: 'final-id' };
    AesUtils.CrtCounterEncrypt = vi.fn().mockReturnValueOnce('encryped-merchant-number');
    Daos.rateMerchant.addItem = vi.fn().mockResolvedValueOnce(expected);

    // Act
    const result = await RateManagementService.createNewActiveMerchantRate(
      rateSheetArr,
      1,
      'B2C',
      false,
    );

    // Assert
    expect(result).toBe(expected);
  });

  it('should return new merchant rate id after creating version 2', async () => {
    // Arrange
    const expected = { id: 'final-id' };
    AesUtils.CrtCounterEncrypt = vi.fn().mockReturnValueOnce('encryped-merchant-number');
    Daos.rateMerchant.addItem = vi.fn().mockResolvedValueOnce(expected);

    // Act
    const result = await RateManagementService.createNewActiveMerchantRate(
      rateSheetArr,
      2,
      'B2C',
      false,
    );

    // Assert
    expect(result).toBe(expected);
  });
});

describe('test updateRateAndRateItems', () => {
  const rateSheetArr = [
    [undefined],
    undefined,
    undefined,
    [undefined, 'asdf', 'MERCHANTACCNO001'],
    [undefined, undefined, '2024-08-12T15:30:00Z', undefined, '2024-08-12T15:30:00Z'],
  ];
  const version = 2; // does not matter in this test case
  const serviceOption = 'B2C';

  it('should update rate items if merchant rate found', async () => {
    // Arrange
    const expectedRes = 'new-merchant-rate-id';
    AesUtils.CrtCounterEncrypt = vi.fn().mockReturnValueOnce('encrypted-merchant-account-number');
    RateServices.getRateByValidityDates = vi.fn().mockResolvedValueOnce({ id: 'merchant-rate-id' });
    RateManagementService.deactivateMerchantRateItems = vi.fn();
    RateManagementService.deactivateMerchantRate = vi.fn().mockResolvedValueOnce(undefined);
    RateManagementService.createNewActiveMerchantRate = vi.fn().mockResolvedValueOnce(expectedRes);

    // Act
    const result = await RateManagementService.updateRateAndRateItems(
      'file-name.xlsx',
      rateSheetArr,
      version,
      serviceOption,
      false,
    );

    // Assert
    expect(RateManagementService.deactivateMerchantRateItems).toHaveBeenCalledOnce();
    expect(RateManagementService.deactivateMerchantRate).toHaveBeenCalledOnce();
    expect(RateManagementService.createNewActiveMerchantRate).toHaveBeenCalledOnce();
    expect(result).toBe(expectedRes);
  });

  it('should not update rate items if merchant rate not found', async () => {
    // Arrange
    const expectedRes = 'new-merchant-rate-id';
    AesUtils.CrtCounterEncrypt = vi.fn().mockReturnValueOnce('encrypted-merchant-account-number');
    RateServices.getRateByValidityDates = vi.fn().mockResolvedValueOnce(null);
    RateManagementService.deactivateMerchantRateItems = vi.fn();
    RateManagementService.deactivateMerchantRate = vi.fn().mockResolvedValueOnce(undefined);
    RateManagementService.createNewActiveMerchantRate = vi.fn().mockResolvedValueOnce(expectedRes);

    // Act
    const result = await RateManagementService.updateRateAndRateItems(
      'file-name.xlsx',
      rateSheetArr,
      version,
      serviceOption,
      false,
    );

    // Assert
    expect(RateManagementService.deactivateMerchantRateItems).not.toHaveBeenCalled();
    expect(RateManagementService.deactivateMerchantRate).not.toHaveBeenCalled();
    expect(RateManagementService.createNewActiveMerchantRate).toHaveBeenCalledOnce();
    expect(result).toBe(expectedRes);
  });
});

describe('test createMerchantRateItemsV2', () => {
  const rateSheetArr = [
    [undefined, undefined],
    undefined,
    undefined,
    [undefined, '', 'MERCHANTACCNO001'],
    [undefined, undefined, '2024-08-12T15:30:00Z', undefined, '2024-08-12T15:30:00Z'],
    undefined,
    undefined,
    undefined,
    undefined,
    undefined,
    ['Parcel Weight', 'countryCode'],
    [undefined, 'serviceOption'],
    undefined,
    ['10.0', '14.2'],
  ];
  const data = {
    fileName: 'file-name.xlsx',
    rateId: 'merchant-rate-id',
  };
  const expectedObj = {
    active: true,
    country_code: 'countryCode',
    merchant_account_number: 'MERCHANTACCNO001',
    rate: 14.2,
    rate_table_id: 'merchant-rate-id',
    service_option: 'serviceoption',
    type: 'merchant_rate_item',
    weight: 10,
  };
  CommonUtils.sleep = vi.fn().mockResolvedValue(undefined);

  it('should return merchant rate items version 2', async () => {
    // Arrange
    RateManagementService.getBatch = vi.fn().mockReturnValueOnce([[{}]]);
    RateManagementService.createMerchantRateItem = vi.fn().mockResolvedValue(undefined);

    // Act
    const result = await RateManagementService.createMerchantRateItemsV2(rateSheetArr, data);

    // Assert
    expect(result).toEqual([expectedObj]);
  });
});

describe('test createMerchantRateItems', () => {
  const expectedRes = {
    origin: 'origin',
    destination: 'destination',
    merchant_account_number: 'MERCHANTACCNO001',
    active: true,
    rate: 14.2,
    rate_currency: 'currency',
    rate_table_id: 'merchant-rate-id',
    rate_zone: 'rate-zone',
    service_option: 'serviceOption',
    unit: 'kg',
    weight: 10,
  };
  const rateSheet: WorkSheet = {};
  const rateSheetArr = [
    undefined,
    undefined,
    undefined,
    [undefined, undefined, 'MERCHANTACCNO001'],
    undefined,
    undefined,
    undefined,
    undefined,
    ['origin', 'destination', 'serviceOption', '10.0', 'kg', 'currency', '14.2', 'zone'],
  ];
  const data = {
    fileName: 'file-name.xlsx',
    rateId: 'merchant-rate-id',
  };
  const range = {
    e: {
      r: 8,
      c: 2,
    },
  };

  it('should return merchant rate items version 1', async () => {
    // Arrange
    RateManagementService.mapLMDZone = vi.fn().mockResolvedValueOnce({ zone: 'rate-zone' });
    XLSX.utils.decode_range = vi.fn().mockReturnValueOnce(range);
    RateManagementService.getBatch = vi.fn().mockReturnValueOnce([[{}]]);
    RateManagementService.createMerchantRateItem = vi.fn().mockResolvedValue(undefined);

    // Act
    const result = await RateManagementService.createMerchantRateItems(
      rateSheet,
      rateSheetArr,
      data,
    );

    // Assert
    expect(result).toEqual([expectedRes]);
  });
});

describe('updateMerchantRateFinishUpload', () => {
  it('should update the merchant rate successfully', async () => {
    // Arrange
    const id = 'test-id';
    const encryptedMerchantAccNo = 'encrypted-merchant-acc-no';
    const expectedMerchantRate = { id, partitionKey: encryptedMerchantAccNo, is_uploading: false };
    Daos.rateMerchant.patch = vi.fn().mockResolvedValue(expectedMerchantRate);

    // Act
    const result = await RateManagementService.updateMerchantRateFinishUpload(
      id,
      encryptedMerchantAccNo,
    );

    // Assert
    expect(Daos.rateMerchant.patch).toHaveBeenCalledWith(expectedMerchantRate);
    expect(result).toEqual(expectedMerchantRate);
  });

  it('should log an error if updating the merchant rate fails', async () => {
    // Arrange
    const id = 'test-id';
    const encryptedMerchantAccNo = 'encrypted-merchant-acc-no';
    const mockError = new Error('Failed to update merchant rate');
    Daos.rateMerchant.patch = vi.fn().mockRejectedValueOnce(mockError);

    try {
      // Act
      await RateManagementService.updateMerchantRateFinishUpload(id, encryptedMerchantAccNo);
    } catch (error) {
      expect(error.message).toEqual('Failed to update merchant rate');
    }

    // Assert
    expect(Daos.rateMerchant.patch).toHaveBeenCalledWith({
      id,
      partitionKey: encryptedMerchantAccNo,
      is_uploading: false,
    });
  });
});

describe('validateB2BRateSheet', () => {
  const mockRateSheet = {};
  const mockData = { merchantAccNoFromRequest: '123456' };
  const mockServiceOption = 'B2B';

  it('should return error if metadata is not fully populated', async () => {
    vi.mocked(getCellValue).mockReturnValueOnce(null);

    const result = await RateManagementService.validateB2BRateSheet(
      mockRateSheet,
      mockData,
      mockServiceOption,
    );

    expect(result).toEqual({
      success: false,
      message:
        'Error Encountered. File format has issues. Please contact your administrator for assistance',
    });
  });

  it('should return error if merchant account number does not match', async () => {
    vi.mocked(getCellValue)
      .mockReturnValueOnce('A1')
      .mockReturnValueOnce('Merchant Name')
      .mockReturnValueOnce('654321'); // Mock cellC4 as '654321'

    const result = await RateManagementService.validateB2BRateSheet(
      mockRateSheet,
      mockData,
      mockServiceOption,
    );

    expect(result).toEqual({
      success: false,
      message:
        'Merchant account 654321 does not match with the merchant which the file is uploaded to',
    });
  });

  it('should return error if merchant does not exist', async () => {
    vi.mocked(getCellValue)
      .mockReturnValueOnce('A1')
      .mockReturnValueOnce('Merchant Name')
      .mockReturnValueOnce('123456'); // Mock cellC4 as '123456'

    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(false);

    const result = await RateManagementService.validateB2BRateSheet(
      mockRateSheet,
      mockData,
      mockServiceOption,
    );

    expect(result).toEqual({
      success: false,
      message: 'Error: Merchant Name and/or Account No. does not exist. Please check your file.',
    });
  });

  it('should return error if dates are invalid', async () => {
    vi.mocked(getCellValue)
      .mockReturnValueOnce('A1')
      .mockReturnValueOnce('Merchant Name')
      .mockReturnValueOnce('123456')
      .mockReturnValueOnce('2021-01-01') // Mock cellC5 as '2021-01-01'
      .mockReturnValueOnce('charge weight')
      .mockReturnValueOnce('2020-01-01') // Mock cellE5 as '2020-01-01'
      .mockReturnValueOnce('USD')
      .mockReturnValueOnce('weight unit');

    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);

    const result = await RateManagementService.validateB2BRateSheet(
      mockRateSheet,
      mockData,
      mockServiceOption,
    );

    expect(result).toEqual({
      success: false,
      message: 'Error: Invalid dates',
    });
  });

  it('should return error if chargeable weight is invalid', async () => {
    vi.mocked(getCellValue)
      .mockReturnValueOnce('A1')
      .mockReturnValueOnce('Merchant Name')
      .mockReturnValueOnce('123456')
      .mockReturnValueOnce('2021-01-01')
      .mockReturnValueOnce('charge weight')
      .mockReturnValueOnce('2022-03-02')
      .mockReturnValueOnce('USD')
      .mockReturnValueOnce('weight unit');

    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);

    const result = await RateManagementService.validateB2BRateSheet(
      mockRateSheet,
      mockData,
      mockServiceOption,
    );

    expect(result).toEqual({
      success: false,
      message: 'Error: Invalid Chargeable Weight',
    });
  });

  it('should return success with flat rate if all validations pass', async () => {
    vi.mocked(getCellValue)
      .mockReturnValueOnce('A1')
      .mockReturnValueOnce('Merchant Name')
      .mockReturnValueOnce('123456')
      .mockReturnValueOnce('2021-01-01')
      .mockReturnValueOnce('gross weight')
      .mockReturnValueOnce('2022-01-01')
      .mockReturnValueOnce('USD')
      .mockReturnValueOnce('kg')
      .mockReturnValueOnce('Flat Rate');

    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);

    const mockDataValidationResult = {
      success: true,
      data: [],
      emptyRows: 1,
    };

    vi.spyOn(RateManagementService, 'validateRateSheetDataV2').mockResolvedValueOnce(
      mockDataValidationResult,
    );

    const result = await RateManagementService.validateB2BRateSheet(
      mockRateSheet,
      mockData,
      mockServiceOption,
    );

    expect(result).toEqual({
      success: true,
      data: {
        rates: [],
        version: 2,
        rateChargeType: 'Flat Rate',
        emptyRows: 1,
      },
    });
  });

  it('should return success with weight break if all validations pass', async () => {
    vi.mocked(getCellValue)
      .mockReturnValueOnce('A1')
      .mockReturnValueOnce('Merchant Name')
      .mockReturnValueOnce('123456')
      .mockReturnValueOnce('2021-01-01')
      .mockReturnValueOnce('gross weight')
      .mockReturnValueOnce('2022-01-01')
      .mockReturnValueOnce('USD')
      .mockReturnValueOnce('kg')
      .mockReturnValueOnce('Weight Break');

    MerchantService.checkMerchantExist = vi.fn().mockResolvedValueOnce(true);

    const mockDataValidationResult = {
      success: true,
      data: [],
      emptyRows: 3,
    };

    vi.spyOn(RateManagementService, 'validateRateSheetDataV2').mockResolvedValueOnce(
      mockDataValidationResult,
    );

    const result = await RateManagementService.validateB2BRateSheet(
      mockRateSheet,
      mockData,
      mockServiceOption,
    );

    expect(result).toEqual({
      success: true,
      data: {
        rates: [],
        version: 2,
        rateChargeType: 'Weight Break',
        emptyRows: 3,
      },
    });
  });
});
