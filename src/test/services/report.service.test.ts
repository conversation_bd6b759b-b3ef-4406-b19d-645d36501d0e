import * as XLSX from 'xlsx';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { DestinationService } from '~/services/destination-services.js';
import { ReportService } from '~/services/report.service.js';
import StatusMappingService from '~/services/statusMappingService.js';

import { PlsReportUtils } from '~/utilities/plsReportUtils.js';

import { Daos } from '~/daos/index.js';

console.info = vi.fn();
console.error = vi.fn();

describe('ReportService', () => {
  beforeEach(() => {
    AesUtils.CrtCounterDecrypt = vi.fn();

    Daos.merchant.find = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '6688',
        invoicing_info: {
          insurance: {
            maximumValue: '100',
            valueCurrency: 'VND',
            chargedToMerchant: '99%',
          },
          tax: {
            constParty: 'constParty',
            country: 'VN',
          },
        },
      },
    ]);

    Daos.gaylord.find = vi.fn().mockResolvedValue([
      {
        id: 'GL-23-123456',
        mawb_no: '**********',
      },
    ]);

    Daos.mawb.find = vi.fn().mockResolvedValue([
      {
        _partitionKey: '**********',
      },
    ]);

    Daos.invoice.find = vi.fn().mockResolvedValue([
      {
        invoiceNumber: 'invoiceNumber',
        AR_Posted: 'AR_Posted',
      },
    ]);

    DestinationService.getDestinationAirport = vi
      .fn()
      .mockResolvedValue({ destination_group: 'SIN' });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getMawbs', () => {
    const mawbNo = 'testMawbNo';
    let mockFind;

    beforeEach(() => {
      mockFind = vi.spyOn(Daos.mawb, 'find').mockResolvedValue([]);
    });

    afterEach(() => {
      vi.restoreAllMocks();
    });

    it('should return the correct record count', async () => {
      // Arrange
      const mockResponse = [42];
      mockFind.mockResolvedValueOnce(mockResponse);

      // Act
      const result = await ReportService.getMawbs(mawbNo);

      // Assert
      expect(result).toStrictEqual([42]);
    });
  });

  describe('getGaylordsInLateMawb', () => {
    it('should return the correct gaylords records', async () => {
      // Arrange
      const mawbNo = 'testMawbNo';
      const mockResponse = [
        { id: '1', overpack_id: 'OP1' },
        { id: '2', overpack_id: 'OP2' },
      ]; // Mocked response from the find method
      Daos.gaylord.find = vi.fn().mockResolvedValue(mockResponse);

      // Act
      const result = await ReportService.getGaylordsInLateMawb(mawbNo);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(Daos.gaylord.find).toHaveBeenCalledWith({
        query: 'SELECT c.id, c.overpack_id FROM c WHERE c.mawb_no = @mawbNo',
        parameters: [{ name: '@mawbNo', value: mawbNo }],
      });
    });
  });
  describe('getRawParcels', () => {
    it('should return the correct parcels records', async () => {
      // Arrange
      const gaylordIds = ['gaylord1', 'gaylord2'];
      const mockResponse = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: 'shipped',
          origin: 'USA',
          merchant_name: 'Merchant1',
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord1',
        },
        {
          tracking_no: '456',
          tracking_id: 'def',
          tracking_status: 'in transit',
          origin: 'Canada',
          merchant_name: 'Merchant2',
          destination_group: 'Group2',
          lmd: '2024-06-02',
          gaylord_no: 'gaylord2',
        },
      ]; // Mocked response from the find method
      Daos.manifest_items.find = vi.fn().mockResolvedValueOnce(mockResponse);

      // Act
      const result = await ReportService.getRawParcels(gaylordIds);

      // Assert
      expect(result).toEqual(mockResponse);
      expect(Daos.manifest_items.find).toHaveBeenCalledWith({
        query:
          'SELECT c.tracking_no, c.tracking_id, c.tracking_status, c.origin, c.merchant_name, c.destination_group, c.lmd, c.gaylord_no ' +
          'FROM c WHERE ARRAY_CONTAINS(@gaylords, c.gaylord_no)',
        parameters: [{ name: '@gaylords', value: ['gaylord1', 'gaylord2'] }],
      });
    });
  });
  describe('getSanitizedParcels', () => {
    it('should return the correct sanitized parcels for B2C', () => {
      // Arrange
      const rawParcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'shipped' }],
          origin: 'USA',
          merchant_name: 'encryptedName1',
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord1',
        },
      ];
      const gaylordsInLateMawb = [{ id: 'gaylord1', overpack_id: 'OP1' }];
      const mawbNo = 'testMawbNo';
      const decryptedMerchantName = 'Merchant1';
      const parcelAge = 5;
      const isB2B = false;
      const mawbs = [];

      AesUtils.CrtCounterDecrypt = vi.fn().mockReturnValue(decryptedMerchantName);
      PlsReportUtils.getParcelAge = vi.fn().mockReturnValue(parcelAge);

      // Act
      const result = ReportService.getSanitizedParcels(
        rawParcels,
        gaylordsInLateMawb,
        mawbNo,
        isB2B,
        mawbs,
      );

      // Assert
      expect(result).toEqual([
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'shipped' }],
          origin: 'USA',
          merchant_name: decryptedMerchantName,
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord1',
          parcel_age: parcelAge,
          mawb_no: mawbNo,
          latest_tracking_status: 'shipped',
          overpack_no: 'OP1',
        },
      ]);
      expect(AesUtils.CrtCounterDecrypt).toHaveBeenCalledWith('encryptedName1');
      expect(PlsReportUtils.getParcelAge).toHaveBeenCalledWith(rawParcels[0]);
    });

    it('should return the correct sanitized parcels for B2B', () => {
      // Arrange
      const rawParcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'shipped' }],
          origin: 'USA',
          merchant_name: 'encryptedName1',
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord1',
        },
      ];
      const gaylordsInLateMawb = [{ id: 'gaylord1', overpack_id: 'OP1' }];
      const mawbNo = 'testMawbNo';
      const decryptedMerchantName = 'Merchant1';
      const parcelAge = 5;
      const isB2B = true;
      const mawbs = [{ id: 'mock-id', status: ENUM.mawbStatus.stastd, destination: 'LHR' }];

      AesUtils.CrtCounterDecrypt = vi.fn().mockReturnValue(decryptedMerchantName);
      PlsReportUtils.getParcelAge = vi.fn().mockReturnValue(parcelAge);

      // Act
      const result = ReportService.getSanitizedParcels(
        rawParcels,
        gaylordsInLateMawb,
        mawbNo,
        isB2B,
        mawbs,
      );

      // Assert
      expect(result).toEqual([
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'shipped' }],
          origin: 'USA',
          merchant_name: decryptedMerchantName,
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord1',
          parcel_age: parcelAge,
          mawb_no: mawbNo,
          latest_tracking_status: 'shipped',
          overpack_no: 'OP1',
          destination_country: 'LHR',
        },
      ]);
      expect(AesUtils.CrtCounterDecrypt).toHaveBeenCalledWith('encryptedName1');
      expect(PlsReportUtils.getParcelAge).toHaveBeenCalledWith(rawParcels[0]);
    });

    it('should handle parcels with no matching gaylord in late MAWB', () => {
      // Arrange
      const rawParcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'shipped' }],
          origin: 'USA',
          merchant_name: 'encryptedName1',
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord2',
        },
      ];
      const gaylordsInLateMawb = [{ id: 'gaylord1', overpack_id: 'OP1' }];
      const mawbNo = 'testMawbNo';
      const decryptedMerchantName = 'Merchant1';
      const parcelAge = 5;
      const isB2B = false;
      const mawbs = [];

      AesUtils.CrtCounterDecrypt = vi.fn().mockReturnValue(decryptedMerchantName);
      PlsReportUtils.getParcelAge = vi.fn().mockReturnValue(parcelAge);

      // Act
      const result = ReportService.getSanitizedParcels(
        rawParcels,
        gaylordsInLateMawb,
        mawbNo,
        isB2B,
        mawbs,
      );

      // Assert
      expect(result).toEqual([
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'shipped' }],
          origin: 'USA',
          merchant_name: decryptedMerchantName,
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord2',
          parcel_age: parcelAge,
          mawb_no: mawbNo,
          latest_tracking_status: 'shipped',
          overpack_no: '',
        },
      ]);
      expect(AesUtils.CrtCounterDecrypt).toHaveBeenCalledWith('encryptedName1');
      expect(PlsReportUtils.getParcelAge).toHaveBeenCalledWith(rawParcels[0]);
    });

    it('should handle parcels with multiple tracking statuses', () => {
      // Arrange
      const rawParcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'in transit' }, { status: 'shipped' }],
          origin: 'USA',
          merchant_name: 'encryptedName1',
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord1',
        },
      ];
      const gaylordsInLateMawb = [{ id: 'gaylord1', overpack_id: 'OP1' }];
      const mawbNo = 'testMawbNo';
      const decryptedMerchantName = 'Merchant1';
      const parcelAge = 5;
      const isB2B = false;
      const mawbs = [];

      AesUtils.CrtCounterDecrypt = vi.fn().mockReturnValue(decryptedMerchantName);
      PlsReportUtils.getParcelAge = vi.fn().mockReturnValue(parcelAge);

      // Act
      const result = ReportService.getSanitizedParcels(
        rawParcels,
        gaylordsInLateMawb,
        mawbNo,
        isB2B,
        mawbs,
      );

      // Assert
      expect(result).toEqual([
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: [{ status: 'in transit' }, { status: 'shipped' }],
          origin: 'USA',
          merchant_name: decryptedMerchantName,
          destination_group: 'Group1',
          lmd: '2024-06-01',
          gaylord_no: 'gaylord1',
          parcel_age: parcelAge,
          mawb_no: mawbNo,
          latest_tracking_status: 'shipped',
          overpack_no: 'OP1',
        },
      ]);
      expect(AesUtils.CrtCounterDecrypt).toHaveBeenCalledWith('encryptedName1');
      expect(PlsReportUtils.getParcelAge).toHaveBeenCalledWith(rawParcels[0]);
    });

    it('should handle empty rawParcels array', () => {
      // Arrange
      const rawParcels = [];
      const gaylordsInLateMawb = [{ id: 'gaylord1', overpack_id: 'OP1' }];
      const mawbNo = 'testMawbNo';
      const isB2B = false;
      const mawbs = [];

      // Act
      const result = ReportService.getSanitizedParcels(
        rawParcels,
        gaylordsInLateMawb,
        mawbNo,
        isB2B,
        mawbs,
      );

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('getXLSXContentForLateMawb', () => {
    it('should correctly map parcel fields to XLSX headers', () => {
      // Arrange
      const parcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: 'shipped',
          origin: 'USA',
          merchant_name: 'Merchant1',
          destination_group: 'Group1',
          lmd: '2024-06-01',
        },
        {
          tracking_no: '456',
          tracking_id: 'def',
          tracking_status: 'in transit',
          origin: 'Canada',
          merchant_name: 'Merchant2',
          destination_group: 'Group2',
          lmd: '2024-06-02',
        },
      ];

      const mapXlsxHeadersToParcelField = [
        { xlsxHeader: 'Tracking Number', parcelField: 'tracking_no' },
        { xlsxHeader: 'Tracking ID', parcelField: 'tracking_id' },
        { xlsxHeader: 'Status', parcelField: 'tracking_status' },
        { xlsxHeader: 'Origin', parcelField: 'origin' },
        { xlsxHeader: 'Merchant', parcelField: 'merchant_name' },
        { xlsxHeader: 'Destination Group', parcelField: 'destination_group' },
        { xlsxHeader: 'LMD', parcelField: 'lmd' },
      ];

      // Act
      const result = ReportService.getXLSXContentForLateMawb(parcels, mapXlsxHeadersToParcelField);

      // Assert
      expect(result).toEqual([
        {
          'Tracking Number': '123',
          'Tracking ID': 'abc',
          Status: 'shipped',
          Origin: 'USA',
          Merchant: 'Merchant1',
          'Destination Group': 'Group1',
          LMD: '2024-06-01',
        },
        {
          'Tracking Number': '456',
          'Tracking ID': 'def',
          Status: 'in transit',
          Origin: 'Canada',
          Merchant: 'Merchant2',
          'Destination Group': 'Group2',
          LMD: '2024-06-02',
        },
      ]);
    });

    it('should handle parcels with missing fields', () => {
      // Arrange
      const parcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
        },
      ];

      const mapXlsxHeadersToParcelField = [
        { xlsxHeader: 'Tracking Number', parcelField: 'tracking_no' },
        { xlsxHeader: 'Tracking ID', parcelField: 'tracking_id' },
        { xlsxHeader: 'Status', parcelField: 'tracking_status' },
        { xlsxHeader: 'Origin', parcelField: 'origin' },
        { xlsxHeader: 'Merchant', parcelField: 'merchant_name' },
        { xlsxHeader: 'Destination Group', parcelField: 'destination_group' },
        { xlsxHeader: 'LMD', parcelField: 'lmd' },
      ];

      // Act
      const result = ReportService.getXLSXContentForLateMawb(parcels, mapXlsxHeadersToParcelField);

      // Assert
      expect(result).toEqual([
        {
          'Tracking Number': '123',
          'Tracking ID': 'abc',
          Status: undefined,
          Origin: undefined,
          Merchant: undefined,
          'Destination Group': undefined,
          LMD: undefined,
        },
      ]);
    });

    it('should handle an empty parcels array', () => {
      // Arrange
      const parcels = [];

      const mapXlsxHeadersToParcelField = [
        { xlsxHeader: 'Tracking Number', parcelField: 'tracking_no' },
        { xlsxHeader: 'Tracking ID', parcelField: 'tracking_id' },
        { xlsxHeader: 'Status', parcelField: 'tracking_status' },
        { xlsxHeader: 'Origin', parcelField: 'origin' },
        { xlsxHeader: 'Merchant', parcelField: 'merchant_name' },
        { xlsxHeader: 'Destination Group', parcelField: 'destination_group' },
        { xlsxHeader: 'LMD', parcelField: 'lmd' },
      ];

      // Act
      const result = ReportService.getXLSXContentForLateMawb(parcels, mapXlsxHeadersToParcelField);

      // Assert
      expect(result).toEqual([]);
    });

    it('should handle an empty mapXlsxHeadersToParcelField array', () => {
      // Arrange
      const parcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
          tracking_status: 'shipped',
          origin: 'USA',
          merchant_name: 'Merchant1',
          destination_group: 'Group1',
          lmd: '2024-06-01',
        },
      ];

      const mapXlsxHeadersToParcelField = [];

      // Act
      const result = ReportService.getXLSXContentForLateMawb(parcels, mapXlsxHeadersToParcelField);

      // Assert
      expect(result).toEqual([{}]);
    });

    it('should handle non-existent parcel fields gracefully', () => {
      // Arrange
      const parcels = [
        {
          tracking_no: '123',
          tracking_id: 'abc',
        },
      ];

      const mapXlsxHeadersToParcelField = [
        { xlsxHeader: 'Tracking Number', parcelField: 'tracking_no' },
        { xlsxHeader: 'Non-existent Field', parcelField: 'non_existent_field' },
      ];

      // Act
      const result = ReportService.getXLSXContentForLateMawb(parcels, mapXlsxHeadersToParcelField);

      // Assert
      expect(result).toEqual([
        {
          'Tracking Number': '123',
          'Non-existent Field': undefined,
        },
      ]);
    });
  });
  describe('getMawbReport', () => {
    beforeEach(() => {
      vi.resetAllMocks();
    });

    it('should return workbook buffer when get report for B2C', async () => {
      // Mock implementations
      const getMawbsMock = vi.fn().mockResolvedValue([{ id: 'mawb1' }]);
      const getGaylordsInLateMawbMock = vi
        .fn()
        .mockResolvedValue([{ id: 'gaylord1' }, { id: 'gaylord2' }]);
      const getRawParcelsMock = vi.fn().mockResolvedValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1' },
        { parcel_id: 'parcel2', tracking_no: 'tracking2' },
      ]);
      const combineMawbSttsMock = vi.fn().mockReturnValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1', mawb: {} },
        { parcel_id: 'parcel2', tracking_no: 'tracking2', mawb: {} },
      ]);
      const getSanitizedParcelsMock = vi.fn().mockReturnValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1', sanitized: true },
        { parcel_id: 'parcel2', tracking_no: 'tracking2', sanitized: true },
      ]);
      const getXLSXContentForLateMawbMock = vi.fn().mockReturnValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1' },
        { parcel_id: 'parcel2', tracking_no: 'tracking2' },
      ]);
      const generateMawbReportMock = vi.fn().mockReturnValue(Buffer.from('mock xlsx content'));

      // Replace actual implementations with mocks
      ReportService.getMawbs = getMawbsMock;
      ReportService.getGaylordsInLateMawb = getGaylordsInLateMawbMock;
      ReportService.getRawParcels = getRawParcelsMock;
      ReportService.combineMawbStts = combineMawbSttsMock;
      ReportService.getSanitizedParcels = getSanitizedParcelsMock;
      ReportService.getXLSXContentForLateMawb = getXLSXContentForLateMawbMock;
      ReportService.generateMawbReport = generateMawbReportMock;

      const mawbNo = 'testMawbNo';
      const result = await ReportService.getMawbReport(mawbNo);

      // Check if the mocks were called with correct arguments
      expect(getMawbsMock).toHaveBeenCalledWith(mawbNo);
      expect(getGaylordsInLateMawbMock).toHaveBeenCalledWith(mawbNo);
      expect(getRawParcelsMock).toHaveBeenCalledWith(['gaylord1', 'gaylord2']);
      expect(combineMawbSttsMock).toHaveBeenCalledWith(expect.any(Array), [{ id: 'mawb1' }]);
      expect(getSanitizedParcelsMock).toHaveBeenCalledWith(
        expect.any(Array),
        [{ id: 'gaylord1' }, { id: 'gaylord2' }],
        mawbNo,
        false,
        expect.any(Array),
      );
      expect(getXLSXContentForLateMawbMock).toHaveBeenCalledWith(
        expect.any(Array),
        expect.any(Array),
      );
      expect(generateMawbReportMock).toHaveBeenCalledWith(expect.any(Array), mawbNo);

      // Check if the result is a buffer
      expect(result).toBeInstanceOf(Buffer);
    });

    it('should return workbook buffer when get report for B2B', async () => {
      // Mock implementations
      const getMawbsMock = vi.fn().mockResolvedValue([{ id: 'mawb1', service_option: 'B2B' }]);
      const getGaylordsInLateMawbMock = vi
        .fn()
        .mockResolvedValue([{ id: 'gaylord1' }, { id: 'gaylord2' }]);
      const getRawParcelsMock = vi.fn().mockResolvedValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1' },
        { parcel_id: 'parcel2', tracking_no: 'tracking2' },
      ]);
      const combineMawbSttsMock = vi.fn().mockReturnValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1', mawb: {} },
        { parcel_id: 'parcel2', tracking_no: 'tracking2', mawb: {} },
      ]);
      const getSanitizedParcelsMock = vi.fn().mockReturnValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1', sanitized: true },
        { parcel_id: 'parcel2', tracking_no: 'tracking2', sanitized: true },
      ]);
      const getXLSXContentForLateMawbMock = vi.fn().mockReturnValue([
        { parcel_id: 'parcel1', tracking_no: 'tracking1' },
        { parcel_id: 'parcel2', tracking_no: 'tracking2' },
      ]);
      const generateMawbReportMock = vi.fn().mockReturnValue(Buffer.from('mock xlsx content'));

      // Replace actual implementations with mocks
      ReportService.getMawbs = getMawbsMock;
      ReportService.getGaylordsInLateMawb = getGaylordsInLateMawbMock;
      ReportService.getRawParcels = getRawParcelsMock;
      ReportService.combineMawbStts = combineMawbSttsMock;
      ReportService.getSanitizedParcels = getSanitizedParcelsMock;
      ReportService.getXLSXContentForLateMawb = getXLSXContentForLateMawbMock;
      ReportService.generateMawbReport = generateMawbReportMock;

      const mawbNo = 'testMawbNo';
      const result = await ReportService.getMawbReport(mawbNo);

      // Check if the mocks were called with correct arguments
      expect(getMawbsMock).toHaveBeenCalledWith(mawbNo);
      expect(getGaylordsInLateMawbMock).toHaveBeenCalledWith(mawbNo);
      expect(getRawParcelsMock).toHaveBeenCalledWith(['gaylord1', 'gaylord2']);
      expect(getXLSXContentForLateMawbMock).toHaveBeenCalledWith(
        expect.any(Array),
        expect.any(Array),
      );
      expect(generateMawbReportMock).toHaveBeenCalledWith(expect.any(Array), mawbNo);

      // Check if the result is a buffer
      expect(result).toBeInstanceOf(Buffer);
    });

    it('should throw BadRequestError if no MAWB record found', async () => {
      // Mock implementation to return an empty array
      const getMawbsMock = vi.fn().mockResolvedValue([]);
      ReportService.getMawbs = getMawbsMock;

      const mawbNo = 'invalidMawbNo';
      await expect(ReportService.getMawbReport(mawbNo)).rejects.toThrow(
        'No Mawb with mawbNo provided found',
      );
    });

    it('should handle case when no gaylords are found', async () => {
      const getMawbsMock = vi.fn().mockResolvedValue([{ id: 'mawb1' }]);
      const getGaylordsInLateMawbMock = vi.fn().mockResolvedValue([]);
      const generateMawbReportMock = vi.fn().mockReturnValue(Buffer.from('empty report'));

      ReportService.getMawbs = getMawbsMock;
      ReportService.getGaylordsInLateMawb = getGaylordsInLateMawbMock;
      ReportService.generateMawbReport = generateMawbReportMock;

      const mawbNo = 'mawbWithNoGaylords';
      await ReportService.getMawbReport(mawbNo);

      expect(getMawbsMock).toHaveBeenCalledWith(mawbNo);
      expect(getGaylordsInLateMawbMock).toHaveBeenCalledWith(mawbNo);
    });
  });
  describe('convertMawbToObject', () => {
    test('should return undefined for an empty string', () => {
      expect(ReportService.convertMawbToObject('')).toBeUndefined();
    });

    test('should return an object with booleans set to true for each MAWB', () => {
      const input = 'MAWB123,MAWB456,MAWB789';
      const expectedOutput = {
        MAWB123: true,
        MAWB456: true,
        MAWB789: true,
      };

      expect(ReportService.convertMawbToObject(input)).toEqual(expectedOutput);
    });

    test('should handle single MAWB', () => {
      const input = 'MAWB123';
      const expectedOutput = {
        MAWB123: true,
      };

      expect(ReportService.convertMawbToObject(input)).toEqual(expectedOutput);
    });

    test('should handle multiple MAWBs with extra spaces', () => {
      const input = 'MAWB123, MAWB456 ,MAWB789';
      const expectedOutput = {
        MAWB123: true,
        MAWB456: true,
        MAWB789: true,
      };

      expect(ReportService.convertMawbToObject(input)).toEqual(expectedOutput);
    });

    test('should handle null input', () => {
      expect(ReportService.convertMawbToObject(null)).toBeUndefined();
    });
  });

  describe('test getGaylordListForMonitoring', () => {
    it('should called with correct query spec', async () => {
      // Arrange
      const mawbList = ['618-1431244134', '618-3424124324'];
      const expectParam = {
        parameters: [
          {
            name: '@mawbList',
            value: ['618-1431244134', '618-3424124324'],
          },
        ],
        query: `SELECT c.mawb_no, c.id, c.tracking_status
              FROM c
              WHERE ARRAY_CONTAINS(@mawbList, c.mawb_no)`,
      };

      // Act
      await ReportService.getGaylordListForMonitoring(mawbList);

      // Assert
      expect(Daos.gaylord.find).toHaveBeenCalledWith(expectParam);
    });
  });

  describe('test parcelQueryForMonitoring', () => {
    it('should return correct query spec', () => {
      // Arrange
      const gaylordList = ['GL-1234', 'GL-1235'];
      const merchant = 'iherb';
      const expectParam = [
        {
          name: '@gaylordIdList',
          value: ['GL-1234', 'GL-1235'],
        },
        {
          name: '@merchant',
          value: 'iherb',
        },
      ];

      // Act
      const result = ReportService.parcelQueryForMonitoring(gaylordList, merchant);

      // Assert
      expect(result.parameters).toEqual(expectParam);
      expect(result.query).contain(
        'SELECT c.id, c.tracking_id, c.tracking_status, c.merchant_account_number, c.gaylord_no',
      );
      expect(result.query).contain('FROM c');
      expect(result.query).contain(
        'WHERE ARRAY_CONTAINS(@gaylordIdList, c.gaylord_no) AND c.merchant_account_number = @merchant',
      );
    });
  });

  describe('getParcelListForMonitoring', () => {
    const mockGaylordList = [
      {
        mawb_no: '618-********',
        id: 'CTN-24-********',
        tracking_status: [
          {
            status: 'New',
            date: '2024-11-04T08:49:41.395Z',
          },
        ],
        parcels: [],
      },
      {
        mawb_no: '618-********',
        id: 'CTN-24-********',
        tracking_status: [
          {
            status: 'New',
            date: '2024-11-13T08:54:58.617Z',
          },
        ],
        parcels: [],
      },
    ];

    const mockMawbList = [
      { _partitionKey: 'MAWB1234', otherField: 'value1' },
      { _partitionKey: 'MAWB5678', otherField: 'value2' },
    ];

    const mockMerchant = 'ACME Corp';
    const mockDateRangeBooking = '2024-01-01,2024-01-31';
    const mockDateRangeWarehouse = '2024-02-01,2024-02-28';

    const mockFoundParcelList = [
      {
        id: 'PXLMY00124I0004W',
        tracking_status: [
          {
            status: 'Booked',
            date: '2024-09-06T10:52:27.335Z',
          },
          {
            status: 'LMD receive booking',
            date: '2024-09-06T10:52:29.190Z',
          },
          {
            status: 'Received at Warehouse',
            date: '2024-11-04T08:50:03.906Z',
          },
          {
            status: 'Packed to Gaylord',
            date: '2024-11-04T08:50:03.906Z',
            timestamp: '2024-11-04T08:50:03.906Z',
            gaylord_no: 'CTN-24-********',
          },
        ],
        merchant_account_number: '77fa0e5d7c',
        gaylord_no: 'CTN-24-********',
      },
    ];

    beforeEach(() => {
      vi.resetAllMocks();
    });

    it('should return an empty array when no parcels are found', async () => {
      ReportService.getRawParcelsForMonitorView = vi.fn().mockResolvedValue([]);

      const result = await ReportService.getParcelListForMonitoring(
        mockGaylordList,
        mockMawbList,
        mockMerchant,
        mockDateRangeBooking,
        mockDateRangeWarehouse,
      );

      expect(result).toEqual([]);
      expect(ReportService.getRawParcelsForMonitorView).toHaveBeenCalledWith(
        mockGaylordList,
        mockMerchant,
        mockDateRangeBooking,
        mockDateRangeWarehouse,
      );
    });

    it('should return the modified parcel list with additional information', async () => {
      ReportService.getRawParcelsForMonitorView = vi.fn().mockResolvedValue(mockFoundParcelList);

      const result = await ReportService.getParcelListForMonitoring(
        mockGaylordList,
        mockMawbList,
        mockMerchant,
        mockDateRangeBooking,
        mockDateRangeWarehouse,
      );

      expect(result).toEqual([
        {
          gaylord_no: 'CTN-24-********',
          id: 'PXLMY00124I0004W',
          latest_tracking_status: 'Packed to Gaylord',
          mawb_no: '618-********',
          merchant_account_number: '77fa0e5d7c',
          timestamp: '2024-11-04T08:50:03.906Z',
          tracking_status: [
            {
              date: '2024-09-06T10:52:27.335Z',
              status: 'Booked',
            },
            {
              date: '2024-09-06T10:52:29.190Z',
              status: 'LMD receive booking',
            },
            {
              date: '2024-11-04T08:50:03.906Z',
              status: 'Received at Warehouse',
            },
            {
              date: '2024-11-04T08:50:03.906Z',
              gaylord_no: 'CTN-24-********',
              status: 'Packed to Gaylord',
              timestamp: '2024-11-04T08:50:03.906Z',
            },
          ],
        },
      ]);

      expect(ReportService.getRawParcelsForMonitorView).toHaveBeenCalledWith(
        mockGaylordList,
        mockMerchant,
        mockDateRangeBooking,
        mockDateRangeWarehouse,
      );
    });
  });

  describe('test getCombinedHeaderForNewReport', () => {
    test('should return correct', () => {
      // Arrange
      const mockData = {
        default: {
          headers: ['PLS_shipment_booking_reference', 'shipment_tracking_id'],
          checked: true,
          extractedByEnding: false,
        },
        shipmentInfo: {
          headers: ['received_at_warehouse_date', 'successful_delivery_date'],
          checked: true,
          extractedByEnding: false,
        },
      };
      const expectedData = [
        'PLS_shipment_booking_reference',
        'shipment_tracking_id',
        'received_at_warehouse_date',
        'successful_delivery_date',
      ];

      // Act
      const result = ReportService.getCombinedHeaderForNewReport(mockData);

      // Assert
      expect(result).toEqual(expectedData);
    });
  });

  describe('test getCombinedDataForNewReport', () => {
    it('should return combined data for new report with valid data', () => {
      const extractedContent = [
        { field1: 'value1', field2: 'value2' },
        { field1: 'value3', field2: 'value4' },
      ];
      const newReportConfig = {
        config1: { checked: true, headers: ['field1'] },
        config2: { checked: true, headers: ['field2'] },
      };
      const reportContent = { config1: 'value' };

      const result = ReportService.getCombinedDataForNewReport(
        extractedContent,
        newReportConfig,
        reportContent,
      );

      expect(result).toEqual([{ '0': 'v' }, { '0': 'a' }]);
    });

    it('should return empty array when extractedContent is empty', () => {
      const extractedContent = [];
      const newReportConfig = {
        config1: { checked: true, headers: ['field1'] },
        config2: { checked: true, headers: ['field2'] },
      };
      const reportContent = {};

      const result = ReportService.getCombinedDataForNewReport(
        extractedContent,
        newReportConfig,
        reportContent,
      );

      expect(result).toEqual([]);
    });

    it('should process configurations correctly', () => {
      const extractedContent = [
        { field1: 'value1', field2: 'value2', field3: 'value3' },
        { field1: 'value4', field2: 'value5', field3: 'value6' },
      ];
      const newReportConfig = {
        config1: { checked: true, headers: ['field1', 'field3'] },
        config2: { checked: false, headers: ['field2'] },
      };
      const reportContent = { config1: 'value' };

      const result = ReportService.getCombinedDataForNewReport(
        extractedContent,
        newReportConfig,
        reportContent,
      );

      expect(result).toEqual([{ '0': 'v' }, { '0': 'a' }]);
    });
  });

  describe('test generateExcel', () => {
    test('should return a buffer when called with valid data', () => {
      // Arrange
      vi.spyOn(XLSX.utils, 'book_new').mockReturnValue({
        Sheets: {},
        SheetNames: ['default'],
      } as XLSX.WorkBook);
      vi.spyOn(XLSX.utils, 'json_to_sheet').mockReturnValue({});
      vi.spyOn(XLSX.utils, 'book_append_sheet').mockImplementation(() => '');
      const combinedData = [
        { field1: 'value1', field2: 'value2' },
        { field1: 'value3', field2: 'value4' },
      ];
      const combinedHeaders = ['field1', 'field2'];

      // Act
      ReportService.generateExcel(combinedData, combinedHeaders);

      // Assert
      expect(XLSX.utils.book_new).toHaveBeenCalled();
    });
  });

  describe('isDateWithinRange', () => {
    it('should return true if the date is within the range', async () => {
      const dateString = '2024-11-15T00:00:00.000Z';
      const startDate = '2024-11-10T00:00:00.000Z';
      const endDate = '2024-11-20T00:00:00.000Z';

      const result = ReportService.isDateWithinRange(dateString, startDate, endDate);

      expect(result).toBe(true);
    });

    it('should return false if the date is before the start date', async () => {
      const dateString = '2024-11-05T00:00:00.000Z';
      const startDate = '2024-11-10T00:00:00.000Z';
      const endDate = '2024-11-20T00:00:00.000Z';

      const result = ReportService.isDateWithinRange(dateString, startDate, endDate);

      expect(result).toBe(false);
    });

    it('should return false if the date is after the end date', async () => {
      const dateString = '2024-11-25T00:00:00.000Z';
      const startDate = '2024-11-10T00:00:00.000Z';
      const endDate = '2024-11-20T00:00:00.000Z';

      const result = ReportService.isDateWithinRange(dateString, startDate, endDate);

      expect(result).toBe(false);
    });

    it('should return true if the date is exactly on the start date', async () => {
      const dateString = '2024-11-10T00:00:00.000Z';
      const startDate = '2024-11-10T00:00:00.000Z';
      const endDate = '2024-11-20T00:00:00.000Z';

      const result = ReportService.isDateWithinRange(dateString, startDate, endDate);

      expect(result).toBe(true);
    });

    it('should return true if the date is exactly on the end date', async () => {
      const dateString = '2024-11-20T00:00:00.000Z';
      const startDate = '2024-11-10T00:00:00.000Z';
      const endDate = '2024-11-20T00:00:00.000Z';

      const result = ReportService.isDateWithinRange(dateString, startDate, endDate);

      expect(result).toBe(true);
    });

    it('should handle invalid ISO date strings and return false', async () => {
      const dateString = 'invalid-iso-string';
      const startDate = '2024-11-10T00:00:00.000Z';
      const endDate = '2024-11-20T00:00:00.000Z';

      const result = ReportService.isDateWithinRange(dateString, startDate, endDate);

      expect(result).toBe(false);
    });

    it('should handle empty ISO date strings and return false', async () => {
      const dateString = '';
      const startDate = '2024-11-10T00:00:00.000Z';
      const endDate = '2024-11-20T00:00:00.000Z';

      const result = ReportService.isDateWithinRange(dateString, startDate, endDate);

      expect(result).toBe(false);
    });
  });

  describe('isDateWithinRange', () => {
    it('should return true if the date is within the range', () => {
      const result = ReportService.isDateWithinRange('2024-11-15', '2024-11-01', '2024-11-30');
      expect(result).toBe(true);
    });

    it('should return false if the date is before the range', () => {
      const result = ReportService.isDateWithinRange('2024-10-31', '2024-11-01', '2024-11-30');
      expect(result).toBe(false);
    });

    it('should return false if the date is after the range', () => {
      const result = ReportService.isDateWithinRange('2024-12-01', '2024-11-01', '2024-11-30');
      expect(result).toBe(false);
    });
  });

  describe('parseDateRange', () => {
    it('should return null if no date range is provided', () => {
      const result = ReportService.parseDateRange(undefined);
      expect(result).toBeNull();
    });

    it('should return a tuple with start and end dates if valid date range is provided', () => {
      const result = ReportService.parseDateRange('2024-11-01,2024-11-30');
      expect(result).toEqual(['2024-11-01', '2024-11-30']);
    });
  });

  describe('filterParcelsByStatusAndDateRange', () => {
    const mockParcels = [
      {
        id: '1',
        merchant_account_number: 'M123',
        gaylord_no: 'G001',
        tracking_status: [
          { status: 'DELIVERED', date: '2024-11-15' },
          { status: 'IN_TRANSIT', date: '2024-11-10' },
        ],
      },
      {
        id: '2',
        merchant_account_number: 'M456',
        gaylord_no: 'G002',
        tracking_status: [
          { status: 'DELIVERED', date: '2024-11-01' },
          { status: 'IN_TRANSIT', date: '2024-11-05' },
        ],
      },
    ];

    beforeEach(() => {
      vi.resetAllMocks();
      StatusMappingService.getManifestStatus = vi.fn().mockReturnValue('DELIVERED');
    });

    it('should filter parcels by status and date range', () => {
      const result = ReportService.filterParcelsByStatusAndDateRange(mockParcels, 'DELIVERED', [
        '2024-11-01',
        '2024-11-30',
      ]);

      expect(result).toEqual([
        {
          gaylord_no: 'G001',
          id: '1',
          merchant_account_number: 'M123',
          tracking_status: [
            { status: 'DELIVERED', date: '2024-11-15' },
            { status: 'IN_TRANSIT', date: '2024-11-10' },
          ],
        },
        {
          id: '2',
          merchant_account_number: 'M456',
          gaylord_no: 'G002',
          tracking_status: [
            { status: 'DELIVERED', date: '2024-11-01' },
            { status: 'IN_TRANSIT', date: '2024-11-05' },
          ],
        },
      ]);
    });

    it('should return an empty array if no parcels match the criteria', () => {
      const result = ReportService.filterParcelsByStatusAndDateRange(mockParcels, 'DELIVERED', [
        '2024-12-01',
        '2024-12-31',
      ]);

      expect(result).toEqual([]);
    });

    it('should handle parcels with missing or invalid dates', () => {
      const parcelsWithMissingDates = [
        {
          id: '1',
          merchant_account_number: 'M123',
          gaylord_no: 'G001',
          tracking_status: [
            { status: 'DELIVERED', date: null },
            { status: 'IN_TRANSIT', date: 'InvalidDate' },
          ],
        },
      ];

      const result = ReportService.filterParcelsByStatusAndDateRange(
        parcelsWithMissingDates,
        'DELIVERED',
        ['2024-11-01', '2024-11-30'],
      );

      expect(result).toEqual([]);
    });
  });
});
