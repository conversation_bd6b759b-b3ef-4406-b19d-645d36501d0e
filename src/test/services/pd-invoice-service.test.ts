import { AzureStorageQueue } from '~/common/azure/azure-storage-queue.js';

import { ENUM } from '~/models/enum.js';

import CountryISOService from '~/services/countryISO-service.js';
import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { HawbService } from '~/services/hawb.service.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';
import { OperationHubService } from '~/services/operation-hub.service.js';
import PdInvoiceService from '~/services/pd-invoice-service.js';
import { RateServices } from '~/services/rate-services.js';
import { TaxService } from '~/services/tax-service.js';
import { TimezoneServices } from '~/services/timezone-services.js';

import { IMerchant, MerchantType } from '~/types/merchant.type.js';

beforeEach(() => {
  vi.clearAllMocks();
});

vi.mock('~/services/rate-services.js', () => ({
  RateServices: { getActiveRates: vi.fn() },
}));

vi.mock('~/services/hawb.service.js', () => ({
  HawbService: { updateHawbChargeableWeightAndPdAmount: vi.fn() },
}));

vi.mock(
  '~/services/countryISO-service.js',
  async (importOriginal: () => Promise<{ default: any }>) => {
    const actual = await importOriginal();

    return {
      default: {
        ...actual.default,
        getCountryCode2: vi.fn(),
      },
    };
  },
);

vi.mock('~/services/tax-service.js', () => ({
  TaxService: { calculateTax: vi.fn() },
}));
vi.mock('~/models/aesUtils.js', () => ({
  AesUtils: {
    CrtCounterEncrypt: vi.fn((v) => v),
    CrtCounterDecrypt: vi.fn((v) => v),
  },
}));

describe('prepareInvoiceMetadata', () => {
  test('should get all metadata', async () => {
    CurrencyConversionService.getAllExchRates = vi.fn().mockResolvedValue('exchangeRates');
    OperationHubService.getAll = vi.fn().mockResolvedValue('operationHubs');

    await PdInvoiceService.prepareInvoiceMetadata();

    expect(PdInvoiceService.exchangeRates).toEqual('exchangeRates');
    expect(PdInvoiceService.operationHubs).toEqual('operationHubs');
  });
});

describe('prepareInvoiceMetadata', () => {
  test('should query parcels', async () => {
    TimezoneServices.getUTCAtAddress = vi
      .fn()
      .mockResolvedValueOnce('monthStart')
      .mockResolvedValueOnce('monthEnd');
    const parcels = [1, 2, 3];
    ManifestItemService.getPDReadyToInvoiceParcels = vi.fn().mockReturnValue(parcels);

    const start = new Date('2023-01-01T00:00:00.000Z');
    const result = await PdInvoiceService.getReadyToInvoiceParcels(
      { merchant_name: 'merchant' },
      2023,
      3,
      31,
      start,
    );
    const end = new Date('2023-03-30T23:59:59.999Z');

    expect(ManifestItemService.getPDReadyToInvoiceParcels).toBeCalledWith('merchant', start, end);
    expect(result).toEqual(parcels);
  });
});

describe('getReadyToInvoiceParcels', () => {
  test('should query parcels', async () => {
    TimezoneServices.getUTCAtAddress = vi
      .fn()
      .mockResolvedValueOnce('monthStart')
      .mockResolvedValueOnce('monthEnd');
    const parcels = [1, 2, 3];
    ManifestItemService.getPDReadyToInvoiceParcels = vi.fn().mockResolvedValue(parcels);

    const start = new Date('2023-01-01T00:00:00.000Z');
    const result = await PdInvoiceService.getReadyToInvoiceParcels(
      { merchant_name: 'merchant' },
      2023,
      3,
      31,
      start,
    );
    const end = new Date('2023-03-30T23:59:59.999Z');

    expect(ManifestItemService.getPDReadyToInvoiceParcels).toBeCalledWith('merchant', start, end);
    expect(result).toEqual(parcels);
  });
});

describe('calculateParcelWeights', () => {
  const chargeType = 'Gross Weight';
  const parcel = {
    weight_unit: 'kg',
    weight: 10,
    dimensions_unit: 'cm',
    length: 10,
    width: 10,
    height: 10,
  };

  test('should calculate weight in kg', () => {
    const mockParcel = { ...parcel };
    PdInvoiceService.calculateParcelWeights(mockParcel, chargeType, 'kg');

    expect(mockParcel).toEqual({
      ...parcel,
      chargeWeightUnit: 'kg',
      chargeableWeight: 10,
      grossWeight: 10,
      VolumetricWeigh: 0.2,
    });
  });

  test('should calculate weight in lb', () => {
    const mockParcel = { ...parcel };
    PdInvoiceService.calculateParcelWeights(mockParcel, chargeType, 'lb');

    expect(mockParcel).toEqual({
      ...parcel,
      chargeWeightUnit: 'lb',
      chargeableWeight: 22.05,
      grossWeight: 22.05,
      VolumetricWeigh: 0.44,
    });
  });
});

describe('getRateV1', () => {
  let parcel;
  beforeEach(() => {
    parcel = {
      origin_country: 'SG',
      country_ISO2: 'JP',
      service_option: 'standard',
      lmd_zone: 'NO_RATEZONE_REQUIRED',
      weight_unit: 'kg',
      weight: 10,
    };
  });

  const rateSheet = {
    rates: [
      {
        origin: 'SG',
        destination: 'JP',
        service_option: 'Standard',
        rate_zone: 'JP',
        weight: 5,
        unit: 'kg',
      },
      {
        origin: 'SG',
        destination: 'JP',
        service_option: 'Standard',
        rate_zone: 'JP',
        weight: 10,
        unit: 'kg',
      },
    ],
    chargeable_weight: 'Gross Weight',
  };

  test('should not return rate if origin not match', () => {
    const result = PdInvoiceService.getRateV1(rateSheet, { ...parcel, origin_country: 'JP' });

    expect(result).toBe(undefined);
  });

  test('should not return rate if destination not match', () => {
    const result = PdInvoiceService.getRateV1(rateSheet, { ...parcel, country_ISO2: 'SG' });

    expect(result).toBe(undefined);
  });

  test('should not return rate if service option not match', () => {
    const result = PdInvoiceService.getRateV1(rateSheet, { ...parcel, service_option: 'plus' });

    expect(result).toBe(undefined);
  });

  test('should not return rate if lmd zone not match', () => {
    const result = PdInvoiceService.getRateV1(rateSheet, { ...parcel, lmd_zone: 'Japan' });

    expect(result).toBe(undefined);
  });

  test('should return rate that has matched weight', () => {
    const result = PdInvoiceService.getRateV1(rateSheet, { ...parcel });

    expect(result).toEqual(rateSheet.rates[1]);
  });

  test('should return rate that has biggest weight if no match', () => {
    const result = PdInvoiceService.getRateV1(rateSheet, { ...parcel, weight: 15 });

    expect(result).toEqual(rateSheet.rates[1]);
  });
});

describe('calculateParcelDeliveryRate', () => {
  test('should set parcel rate for rateSheet version 2', () => {
    const merchant = {};
    const parcel: any = { shipment_type: 'domestic', origin_country: 'US', country_ISO2: 'CA' };
    const rateSheets = [{ version: 2, currency: 'USD', id: 1 }];
    const lmdZone = { name: 'Zone1' };

    vi.spyOn(PdInvoiceService, 'getRateV2').mockReturnValueOnce({
      country_code: 'US',
      weight: 10,
      rate: 100,
    });

    PdInvoiceService.calculateParcelDeliveryRate(merchant, parcel, rateSheets, lmdZone);

    expect(parcel.invoiceRateCurrency).toBe('USD');
    expect(parcel.rate_zone).toBe('US');
    expect(parcel.rate_sheet_chg_wt).toBe(10);
    expect(parcel.rate).toBe(100);
  });
  test('should set parcel rate for rateSheet version not 2', () => {
    const merchant = {};
    const parcel: any = {
      shipment_type: 'domestic',
      origin_country: 'US',
      country_ISO2: 'CA',
      lmd_zone: 'Zone1',
    };
    const rateSheets = [{ version: 1, id: 1 }];
    const lmdZone = { name: 'Zone1' };

    PdInvoiceService.getRateV1 = vi.fn().mockReturnValue({
      rate_currency: 'USD',
      rate_zone: 'Zone1',
      weight: 10,
      rate: 100,
    });

    PdInvoiceService.calculateParcelDeliveryRate(merchant, parcel, rateSheets, lmdZone);

    expect(parcel.invoiceRateCurrency).toBe('USD');
    expect(parcel.rate_zone).toBe('Zone1');
    expect(parcel.rate_sheet_chg_wt).toBe(10);
    expect(parcel.rate).toBe(100);
  });
  test('should set invoiceError when no rate is found', () => {
    const merchant = {};
    const parcel: any = {
      shipment_type: 'domestic',
      origin_country: 'US',
      country_ISO2: 'CA',
      lmd_zone: 'Zone1',
    };
    const rateSheets = [{ version: 1, id: 1 }];
    const lmdZone = { name: 'Zone1' };

    PdInvoiceService.getRateV1 = vi.fn().mockReturnValue(undefined);

    PdInvoiceService.calculateParcelDeliveryRate(merchant, parcel, rateSheets, lmdZone);

    expect(parcel.invoiceError).toBe(
      'Rate not found for route US-CA (lmd_zone: Zone1) in rate sheets 1',
    );
  });
  test('should calculate tax information for delivery rate', () => {
    const merchant = {};
    const parcel: any = {
      shipment_type: 'domestic',
      origin_country: 'US',
      country_ISO2: 'CA',
      lmd_zone: 'Zone1',
    };
    const rateSheets = [{ version: 2, currency: 'USD', id: 1 }];
    const lmdZone = { name: 'Zone1' };

    vi.spyOn(PdInvoiceService, 'getRateV2').mockReturnValueOnce({
      country_code: 'US',
      weight: 10,
      rate: 100,
    });

    TaxService.calculateTax = vi.fn().mockReturnValue({
      taxAmount: 10,
      taxCurrency: 'USD',
    });

    PdInvoiceService.calculateParcelDeliveryRate(merchant, parcel, rateSheets, lmdZone);

    expect(TaxService.calculateTax).toHaveBeenCalledWith(
      merchant,
      true,
      ENUM.invoiceChargeType.delivery,
      100,
      'USD',
      'N/A',
    );
    expect(parcel.taxAmount).toBe(10);
    expect(parcel.taxCurrency).toBe('USD');
  });
});

describe('getRateV2', () => {
  test('should return a valid rate when found', () => {
    const rateSheets1 = {
      version: 2,
      chargeable_weight: 'gross weight',
      weight_unit: 'kg',
      rates: [{ country_code: 'US', weight: 10, rate: 100, service_option: 'standard' }],
    };
    const parcel1 = {
      country_ISO2: 'US',
      service_option: 'standard',
      weight: 10,
      weight_unit: 'kg',
    };

    const result = PdInvoiceService.getRateV2(rateSheets1, parcel1);

    expect(result).toStrictEqual({
      country_code: 'US',
      weight: 10,
      service_option: 'standard',
      rate: 100,
    });
  });

  test('should return undefined when no rate is found', () => {
    const rateSheets2 = {
      version: 2,
      rates: [{ country_code: 'US', weight: 10, rate: 100, service_option: 'standard' }],
    };

    const parcel2 = {
      country_ISO2: 'TW',
      service_option: 'postal',
      weight: 10,
    };

    const result1 = PdInvoiceService.getRateV2(rateSheets2, parcel2);

    expect(result1).toBeUndefined();
  });

  test('should calculate rate if parcel weight is higher the hightest weight in rate sheet', () => {
    const rateSheets3 = {
      version: 2,
      chargeable_weight: 'gross weight',
      weight_unit: 'kg',
      incremental_weight_break: 0.5,
      incremental: { TW: { standard: 3 } },
      rates: [{ country_code: 'TW', weight: 10, rate: 100, service_option: 'standard' }],
    };
    const parcel3 = {
      weight_unit: 'kg',
      country_ISO2: 'TW',
      service_option: 'standard',
      weight: 10.6,
    };

    const result2 = PdInvoiceService.getRateV2(rateSheets3, parcel3);

    expect(result2).toStrictEqual({
      country_code: 'TW',
      weight: 11,
      rate: 106,
    });
  });
});
describe('calculateParcelFuelSurcharge', () => {
  const parcel = {
    chargeWeightUnit: 'kg',
    chargeableWeight: 10,
    fuel_surcharge: {
      currency: 'SGD',
      rates_per_kg: 0.5,
    },
    invoiceRateCurrency: 'SGD',
  };

  test('should return 0 if no fuel surcharge', async () => {
    const mockParcel = { ...parcel };
    delete mockParcel.fuel_surcharge;

    const result3 = PdInvoiceService.calculateParcelFuelSurcharge(mockParcel);

    expect(result3).toBe(0);
  });

  test('should return fuel surcharge without convert currency', async () => {
    const mockParcel = { ...parcel, chargeWeightUnit: 'lb' };

    const result = PdInvoiceService.calculateParcelFuelSurcharge(mockParcel);

    expect(result).toBe(2.27);
  });

  test('should return fuel surcharge with convert currency', async () => {
    const mockParcel = { ...parcel, invoiceRateCurrency: 'USD' };
    CurrencyConversionService.convertCurrency = vi
      .fn()
      .mockReturnValue({ destination_currency: 0.5 });

    const result = PdInvoiceService.calculateParcelFuelSurcharge(mockParcel);

    expect(result).toBe(2.5);
  });
});

describe('calculateChargesByRoutes', () => {
  test('should calculate charges by route and service options', async () => {
    const parcels = [
      {
        destination_group: 'DG_SGW_NRT',
        service_option: 'standard',
        country: 'Taiwan',
        country_ISO2: 'TW',
      },
      {
        destination_group: 'DG_SGW_NRT',
        service_option: 'standard',
        country: 'Japan',
        country_ISO2: 'JP',
      },
      {
        destination_group: 'DG_SGW_NRT',
        service_option: 'plus',
        country: 'Japan',
        country_ISO2: 'JP',
      },
      {
        destination_group: 'DG_SGW2_NRT',
        service_option: 'standard',
        country: 'Taiwan',
        country_ISO2: 'TW',
      },
    ].map((item) => ({
      ...item,
      operation_hub: 'SIN01',
      rate: 10,
      tax: 0.1,
      insurance_amount: 1,
      fuelSurcharge: 2,
      taxCode: 'SR',
      taxRate: 1,
      origin_country: 'SG',
    }));

    const result = await PdInvoiceService.calculateChargesByRoutes(
      parcels,
      'TWD',
      new Date(2024, 1),
    );

    expect(result).toEqual([
      {
        destination: 'TW',
        origin_country: 'SG',
        rate: 20,
        numberOfParcel: 2,
        route: 'SG-TW',
        service_options: [
          {
            service_option: 'standard',
            tax: 0.2,
            total: 20,
            total_fuel_surcharge: 4,
            total_insurance_charge: 2,
          },
        ],
        tax: 0.2,
        taxCode: 'SR',
        totalRateInSGD: 0,
        totalTaxInSGD: 0,
        taxRate: 1,
        total_fuel_surcharge: 4,
        total_insurance_charge: 2,
      },
      {
        destination: 'JP',
        origin_country: 'SG',
        rate: 20,
        route: 'SG-JP',
        numberOfParcel: 2,
        service_options: [
          {
            service_option: 'standard',
            tax: 0.1,
            total: 10,
            total_fuel_surcharge: 2,
            total_insurance_charge: 1,
          },
          {
            service_option: 'plus',
            tax: 0.1,
            total: 10,
            total_fuel_surcharge: 2,
            total_insurance_charge: 1,
          },
        ],
        tax: 0.2,
        taxCode: 'SR',
        taxRate: 1,
        totalRateInSGD: 0,
        totalTaxInSGD: 0,
        total_fuel_surcharge: 4,
        total_insurance_charge: 2,
      },
    ]);
  });
});

describe('updateParcelInvoiceStatus', () => {
  it('should queue update parcel invoiced status', async () => {
    const parcels = [
      {
        id: 1,
      },
      {
        id: 2,
      },
    ];
    AzureStorageQueue.sendBase64Message = vi.fn();

    await PdInvoiceService.updateParcelInvoiceStatus(parcels, {});

    expect(AzureStorageQueue.sendBase64Message).toHaveBeenCalledTimes(parcels.length);
  });
});

const invoiceInfo = {
  invoiceRateCurrency: 'SGD',
  taxZone: 'Singapore',
};

describe('generateSectionCsv', () => {
  test('should return subtotal excel content', () => {
    const result = PdInvoiceService.generateSubtotalSection(invoiceInfo, 10, 1);
    expect(result).toEqual([
      [],
      [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SUBTOTAL',
        'SGD',
        {
          v: 10,
          z: '#,##0.00',
          t: 'n',
        },
        'SGD',
        {
          v: 1,
          z: '#,##0.00',
          t: 'n',
        },
      ],
    ]);
  });
});

describe('generateParcelDelivery', () => {
  test('should return excel content', () => {
    const parcels = [
      {
        id: 'id1',
        tracking_id: 'trackId1',
        merchant_order_no: 'order1',
        order_date: '2022-11-03',
        rate_zone: 'KR',
        service_option: 'standard',
        chargeType: ENUM.invoiceChargeType.delivery,
        chargeWeightUnit: 'kg',
        rate_sheet_chg_wt: '10',
        chargeableWeight: '5',
        grossWeight: '6',
        VolumetricWeigh: '8',
        taxCode: 'SR',
        rate: 10,
        tax: 1,
        tracking_status: [
          {
            status: 'Receive at Warehouse',
            date: '2022-12-20T15:59:01.781Z',
          },
        ],
        surcharges: [
          {
            chargeType: 'Surcharge - Relabel',
            rate: 1,
            tax: 0.1,
            taxCode: 'SR',
          },
        ],
      },
    ];
    const merchantRti = {
      standard: { deliveryStatus: ENUM.rti_info.standardOrPlus.deliveredOrUndeliverd1st },
      plus: { deliveryStatus: ENUM.rti_info.standardOrPlus.deliveredOrUndeliverd3rd },
    };

    const invoiceDataForExcel = PdInvoiceService.generateParcelDelivery(
      invoiceInfo,
      parcels,
      merchantRti,
    );

    expect(invoiceDataForExcel).toEqual([
      ['PARCEL DELIVERY INVOICE DETAILS'],
      [
        'PLS Shipment Booking Reference',
        'Shipment Tracking ID',
        'Merchant Order No',
        'Shipment Booking Date/UTC',
        'Rate Zone',
        'Service Option',
        'Status',
        'Status (Date and Time/UTC)',
        'Rate Sheet Chargeable Weight (kg)',
        'Chargeable Weight (kg)',
        'Gross Weight (kg)',
        'Volumetric Weight (kg)',
        'Tax code',
        'Charge Type',
        'Rate Currency',
        'Rate',
        'Tax Currency',
        'Tax',
      ],
      [
        'id1',
        {
          t: 's',
          v: 'trackId1',
        },
        'order1',
        '2022-11-03',
        'KR',
        'standard',
        '',
        '',
        '10',
        '5',
        '6',
        '8',
        'SR',
        'Delivery Fee',
        'SGD',
        { v: 10, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 1, z: '#,##0.00', t: 'n' },
      ],
      [],
      [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SUBTOTAL',
        'SGD',
        { v: 10, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 1, z: '#,##0.00', t: 'n' },
      ],
    ]);
  });
});

describe('generateFuelSurchargeCsv', () => {
  test('should return data to generate excel file', () => {
    const parcels = [
      {
        id: '1',
        fuelSurcharge: 1,
        chargeableWeight: '1',
        fuel_surcharge: {
          currency: 'USD',
          rates_per_kg: '1',
        },
        origin_country: 'SG',
        country_ISO2: 'AU',
      },
      {
        id: '2',
        fuelSurcharge: 4,
        chargeableWeight: '2',
        fuel_surcharge: {
          currency: 'USD',
          rates_per_kg: '2',
        },
        origin_country: 'SG',
        country_ISO2: 'NZ',
      },
    ];

    const result = PdInvoiceService.generateFuelSurcharge(invoiceInfo, parcels);

    expect(result).toEqual([
      ['FUEL SURCHARGES'],
      [
        'PLS Shipment Booking Reference',
        'Routing',
        'Currency',
        'Rate per KG',
        'Chargeable Weight (KG)',
        'Tax Code',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Surcharge Currency',
        'Surcharge',
        'Tax Currency',
        'Tax',
      ],
      [
        '1',
        'SG - AU',
        'USD',
        'USD1',
        '1',
        'ZR',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'USD',
        { v: 1, z: '#,##0.00', t: 'n' },
        'USD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [
        '2',
        'SG - NZ',
        'USD',
        'USD2',
        '2',
        'ZR',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'USD',
        { v: 4, z: '#,##0.00', t: 'n' },
        'USD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [],
      [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SUBTOTAL',
        'SGD',
        { v: 5, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
    ]);
  });
});

describe('generateInsuranceSurcharge', () => {
  test('should return excel content', () => {
    const parcels = [
      {
        id: '1',
        insurance_amount: '1',
        item: [{ total_declared_value: '1' }, { total_declared_value: '2' }],
      },
      {
        id: '2',
        insurance_amount: '2',
        item: [{ total_declared_value: '3' }, { total_declared_value: '4' }],
      },
    ];
    const merchantInsurance = {
      maximumValue: 5,
      valueCurrency: 'USD',
      chargedToMerchant: 10,
    };

    const result = PdInvoiceService.generateInsuranceSurcharge(
      invoiceInfo,
      parcels,
      merchantInsurance,
    );

    expect(result).toEqual([
      ['INSURANCE CHARGES'],
      [
        'PLS Shipment Booking Reference',
        'Shipment Value',
        'Max Coverage',
        'Currency',
        'Percentage',
        'Tax Code',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Surcharge Currency',
        'Surcharge',
        'Tax Currency',
        'Tax',
      ],
      [
        '1',
        '3',
        5,
        'USD',
        10,
        'ZR',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SGD',
        { v: 1, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [
        '2',
        '7',
        5,
        'USD',
        10,
        'ZR',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SGD',
        { v: 2, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [],
      [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SUBTOTAL',
        'SGD',
        { v: 3, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
    ]);
  });
});

describe('generateSurcharge', () => {
  test('should return data to gen xlsx', () => {
    const surcharges = [
      {
        parxl_id: '1',
        rate: 1,
        type: 'Redelivery',
        tracking_id: '1',
        tax: 0,
        taxCode: 'ZR',
      },
      {
        parxl_id: '2',
        rate: 2,
        type: 'Return to Sender',
        tracking_id: '2',
        tax: 0,
        taxCode: 'ZR',
      },
      {
        parxl_id: '3',
        rate: 3,
        type: 'Redelivery',
        tracking_id: '3',
        tax: 0,
        taxCode: 'ZR',
      },
      {
        rate: 2,
        type: 'Disposal SR',
        reference_type: 'merchant',
        tax: 0,
        taxCode: '',
      },
    ];

    const result = PdInvoiceService.generateSurcharge(invoiceInfo, [], surcharges);

    expect(result).toEqual([
      ['SURCHARGES INVOICE DETAILS'],
      [
        'PLS Shipment Booking Reference',
        'Shipment Tracking ID',
        'Fee Type',
        'Tax code',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'Rate Currency',
        'Rate',
        'Tax Currency',
        'Tax',
      ],
      [
        {
          t: 's',
          v: '1',
        },
        {
          t: 's',
          v: '1',
        },
        'Redelivery',
        'ZR',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SGD',
        { v: 1, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [
        {
          t: 's',
          v: '2',
        },
        {
          t: 's',
          v: '2',
        },
        'Return to Sender',
        'ZR',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SGD',
        { v: 2, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [
        {
          t: 's',
          v: '3',
        },
        {
          t: 's',
          v: '3',
        },
        'Redelivery',
        'ZR',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SGD',
        { v: 3, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [
        {
          t: 's',
          v: 'N/A',
        },
        {
          t: 's',
          v: 'N/A',
        },
        'Disposal SR Flat Fee',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SGD',
        { v: 2, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
      [],
      [
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        '',
        'SUBTOTAL',
        'SGD',
        { v: 8, z: '#,##0.00', t: 'n' },
        'SGD',
        { v: 0, z: '#,##0.00', t: 'n' },
      ],
    ]);
  });
});

describe('calculateHawbChargesByRoutes', () => {
  test('should group hawbs by route and calculate totals', async () => {
    const hawbs = [
      {
        pd_amount: 100,
        tax: 10,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination_country: 'JP',
      },
      {
        pd_amount: 200,
        tax: 20,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination_country: 'JP',
      },
      {
        pd_amount: 300,
        tax: 30,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination_country: 'TW',
      },
    ];
    const merchant = { country: 'SG' };
    const invoiceCurrency = 'SGD';
    const invoiceMonth = new Date(2024, 1);

    CurrencyConversionService.exchangeMoney = vi.fn().mockResolvedValue(0);

    const result = await PdInvoiceService.calculateHawbChargesByRoutes(
      hawbs,
      merchant,
      invoiceCurrency,
      invoiceMonth,
    );

    expect(result).toEqual([
      {
        route: 'SG-JP',
        service_options: [
          {
            service_option: 'B2B',
            total: 300,
            tax: 30,
          },
        ],
        rate: 300,
        tax: 30,
        total_insurance_charge: 0,
        total_fuel_surcharge: 0,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination: 'JP',
        numberOfHawbs: 2,
        totalRateInSGD: 0,
        totalTaxInSGD: 0,
      },
      {
        route: 'SG-TW',
        service_options: [
          {
            service_option: 'B2B',
            total: 300,
            tax: 30,
          },
        ],
        rate: 300,
        tax: 30,
        total_insurance_charge: 0,
        total_fuel_surcharge: 0,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination: 'TW',
        numberOfHawbs: 1,
        totalRateInSGD: 0,
        totalTaxInSGD: 0,
      },
    ]);
  });

  test('should return empty array if hawbs is empty', async () => {
    const merchant = { country: 'SG' };
    const invoiceCurrency = 'SGD';
    const invoiceMonth = new Date(2024, 1);

    const result = await PdInvoiceService.calculateHawbChargesByRoutes(
      [],
      merchant,
      invoiceCurrency,
      invoiceMonth,
    );

    expect(result).toEqual([]);
  });

  test('should handle hawbs with different tax codes and rates', async () => {
    const hawbs = [
      {
        pd_amount: 50,
        tax: 5,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination_country: 'JP',
      },
      {
        pd_amount: 75,
        tax: 7.5,
        taxCode: 'ZR',
        taxRate: 0,
        origin_country: 'SG',
        destination_country: 'JP',
      },
      {
        pd_amount: 100,
        tax: 10,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination_country: 'TW',
      },
    ];
    const merchant = { country: 'SG' };
    const invoiceCurrency = 'SGD';
    const invoiceMonth = new Date(2024, 1);

    CurrencyConversionService.exchangeMoney = vi.fn().mockResolvedValue(0);

    const result = await PdInvoiceService.calculateHawbChargesByRoutes(
      hawbs,
      merchant,
      invoiceCurrency,
      invoiceMonth,
    );

    expect(result).toEqual([
      {
        route: 'SG-JP',
        service_options: [
          {
            service_option: 'B2B',
            total: 125,
            tax: 12.5,
          },
        ],
        rate: 125,
        tax: 12.5,
        total_insurance_charge: 0,
        total_fuel_surcharge: 0,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination: 'JP',
        numberOfHawbs: 2,
        totalRateInSGD: 0,
        totalTaxInSGD: 0,
      },
      {
        route: 'SG-TW',
        service_options: [
          {
            service_option: 'B2B',
            total: 100,
            tax: 10,
          },
        ],
        rate: 100,
        tax: 10,
        total_insurance_charge: 0,
        total_fuel_surcharge: 0,
        taxCode: 'SR',
        taxRate: 7,
        origin_country: 'SG',
        destination: 'TW',
        numberOfHawbs: 1,
        totalRateInSGD: 0,
        totalTaxInSGD: 0,
      },
    ]);
  });
});

describe('RateManagementService.calculateChargesByHawbs', () => {
  const merchant: IMerchant = {
    id: 'id',
    merchant_name: 'merchant_name',
    merchant_type: MerchantType.NORMAL,
    merchant_account_number: 'merchant_account_number',
    merchant_invoice_name: 'merchant_invoice_name',
    allow_parxl_collect: false,
    street: 'street',
    city: 'city',
    state: 'state',
    postal_code: 'postal_code',
    vat_no: 'vat_no',
    eori: 'eori',
    phone_number: 'phone_number',
    country: 'country',
    api_url: 'api_url',
    list_merchant_account: [
      {
        id: 'id',
        name: 'name',
        email: 'email',
        phone_number: 'phone_number',
        role: 'role',
        accountEnabled: false,
        last_activity: 'last_activity',
      },
    ],
    autoInvoice: true,
    invoicing_info: {
      tax: {
        constParty: '68d25d0d21e974ecd68aef1bb1d6',
        country: '77d4500b2cb956fbc1',
        state: '77d4500b2cb956fbc1',
        sapARCode: '',
        creditTerm: '45 days from Invoice Date',
        accountType: 'JP JPY Account',
        updateOn: '2023-12-21T07:02:34.304Z',
        updateBy: '52d4500423aa0f',
      },
      rti_info: {
        standard: { deliveryStatus: 'deliveryStatus' },
        plus: { deliveryStatus: 'deliveryStatus' },
        'self-collect': { deliveryStatus: 'deliveryStatus' },
        freight: { deliveryStatus: 'deliveryStatus' },
        postal: { deliveryStatus: 'deliveryStatus' },
        valid_from: '2022-12-30T00:00:00.000Z',
        updateOn: '2024-06-13T06:52:25.393Z',
        updateBy: '74d54b0f039d0cbd',
      },
      surcharges: {
        hasAdminCharge: false,
        adminCharge: 0,
        hasFuelSurchargeApply: false,
        updateOn: '2023-06-22T09:11:53.979Z',
        updateBy: '6ada510f1d9d08bd',
      },
      insurance: {
        maximumValue: '16',
        valueCurrency: '65fb70',
        chargedToMerchant: '0',
        updateOn: '2023-07-27T08:59:02.130Z',
        updateBy: '4cc8500b23a10cbbe48ff70ef1c1c499',
      },
    },
    createdAt: '2023-07-27T08:59:02.130Z',
    updatedAt: '2023-07-27T08:59:02.130Z',
    use_warehouse_address_for_invoice: false,
    invoice_address: {
      street: '52d45b1823a854',
      city: '6cf3',
      state: '77d8511921',
      postal_code: '158d0e5c7d',
      country: '72f3',
    },
    return_address_list: [
      {
        street: '158f0d',
        city: '158f0d',
        state: '158f0d',
        postal_code: '158f0d',
        country: '65e5',
        building: '158f0d5d',
      },
    ],
    ops_hub_international: 'ops_hub_international',
    ops_hub_domestic: 'ops_hub_domestic',
    fmd: 'fmd',
    lmd: {
      KR: 'Hanjin',
      AU: 'Australia Post',
    },
    invoice_emails: ['a@email'],
  };

  const hawbs = [
    {
      item: {
        id: 1,
        destination_country: 'MY',
      },
      pd_amount: 100,
      pd_currency: 'SGD',
      chargeType: 'Delivery',
      rate: 10,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return empty array and 0 if hawbs is empty', async () => {
    const result = await PdInvoiceService.calculateChargesByHawbs(merchant, []);
    expect(result).toEqual([[], 0]);
  });

  it('should throw error if no active rates', async () => {
    vi.mocked(RateServices.getActiveRates).mockResolvedValueOnce([]);
    await expect(PdInvoiceService.calculateChargesByHawbs(merchant, hawbs)).rejects.toThrow(
      'No active rate for hawb',
    );
  });

  it('should process hawbs and return successHawbs and error count', async () => {
    const rates = [
      {
        service_option: ENUM.RATE_SHEET_SERVICE_OPTION.B2B,
        rates: [{ id: 1 }],
        rateItems: [],
      },
    ];
    vi.mocked(RateServices.getActiveRates).mockResolvedValueOnce(rates);

    const successItems = [
      {
        item: { id: 1, destination_country: 'MY' },
        pd_amount: 100,
        pd_currency: 'SGD',
        chargeType: 'Delivery',
        rate: 10,
      },
    ];
    const errorItems = [];

    HawbService.updateHawbChargeableWeightAndPdAmount = vi.fn().mockResolvedValueOnce({
      successItems,
      errorItems,
    });

    CountryISOService.getCountryCode2 = vi.fn().mockReturnValue('MY');
    TaxService.calculateTax = vi.fn().mockReturnValue({
      tax: 7,
      taxCode: 'GST',
      taxRate: 0.07,
    });

    const result = await PdInvoiceService.calculateChargesByHawbs(merchant, hawbs);

    expect(result[0]).toHaveLength(1);
    expect(result[1]).toBe(0);
  });

  it('should handle errorItems returned from HawbService', async () => {
    const rates = [
      {
        service_option: ENUM.RATE_SHEET_SERVICE_OPTION.B2B,
        rates: [{ id: 1 }],
        rateItems: [],
      },
    ];
    vi.mocked(RateServices.getActiveRates).mockResolvedValueOnce(rates);

    const successItems = [];
    const errorItems = [{ id: 2 }];

    vi.mocked(HawbService.updateHawbChargeableWeightAndPdAmount).mockResolvedValueOnce({
      successItems,
      errorItems,
    });

    const result = await PdInvoiceService.calculateChargesByHawbs(merchant, hawbs);

    expect(result[0]).toHaveLength(0);
    expect(result[1]).toBe(1);
  });
});
