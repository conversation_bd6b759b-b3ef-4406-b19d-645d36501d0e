import axios from 'axios';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { EmailService } from '~/services/email.services.js';
import { UserServices } from '~/services/user.services.js';

import { EmailUtils } from '~/utilities/emailUtils.js';

import { config } from '~/configs/index.js';

axios.post = vi.fn();
axios.get = vi.fn();

console.error = vi.fn();

beforeEach(() => {
  console.log = vi.fn();
});

describe('test file email.services', () => {
  describe('test function sendMailRequestApproveDeminimis', () => {
    const objectDeminimis = {
      country: 'Singapore',
      state: 'N/A',
      tax_type: 'import',
      tax_percentage: '5',
      currency: 'SGD',
      threshold: 100,
      valid_from: '27/11/2019',
      created_by: 'test_user',
      approver: 'john_doe',
    };
    test('called to function sendmail with correct params and return data', async () => {
      const encrypted = AesUtils.encryptTaxRate(objectDeminimis);
      UserServices.getUserByNameOrEmail = vi
        .fn()
        .mockResolvedValue({
          success: true,
          data: [
            {
              name: 'test_approver',
              email: '<EMAIL>',
            },
          ],
        })
        .mockResolvedValueOnce({
          success: true,
          data: [
            {
              name: 'test_requestor',
              email: '<EMAIL>',
            },
          ],
        });
      await EmailService.sendMailRequestApproveDeminimis(encrypted);
      expect(axios.post).toHaveBeenCalledWith(
        expect.any(String),
        {
          attachments: [],
          from: '',
          html: `<p>Dear test_approver,</p>
      <p>
      test_requestor has requested you to approve the De minimis Update for Singapore. Please login into PLS (#/distribution/deminimis) to process the request.
      </p>
      <p>Regards,</p>
      <p>PLS</p>`,
          subject: 'PLS notification - Request for De minimis Update Approval',
          to: '<EMAIL>',
        },
        expect.any(Object),
      );
    });
    test('case exception', async () => {
      const encrypted = AesUtils.encryptTaxRate(objectDeminimis);
      const error = new Error('test');
      UserServices.getUserByNameOrEmail = vi.fn().mockResolvedValue({
        success: false,
        error,
      });
      console.error = vi.fn();
      await EmailService.sendMailRequestApproveDeminimis(encrypted);
      expect(console.error).toHaveBeenCalledWith(
        'type=ERROR, name=EmailService, function=sendMailRequestApproveDeminimis, error=test',
      );
    });
  });

  describe('test function sendMailApproveOrRejectDeminimis', () => {
    const objectDeminimis = {
      country: 'Singapore',
      state: 'N/A',
      tax_type: 'import',
      tax_percentage: '5',
      currency: 'SGD',
      threshold: 100,
      valid_from: '27/11/2019',
      created_by: 'test_user',
      approver: 'john_doe',
    };
    test('called to function sendmail with correct params and return data for approved case', async () => {
      const encrypted = AesUtils.encryptTaxRate(objectDeminimis);
      UserServices.getUserByNameOrEmail = vi
        .fn()
        .mockResolvedValueOnce({
          success: true,
          data: [
            {
              name: 'test_approver',
              email: '<EMAIL>',
            },
          ],
        })
        .mockResolvedValueOnce({
          success: true,
          data: [
            {
              name: 'test_requestor',
              email: '<EMAIL>',
            },
          ],
        });

      await EmailService.sendMailApproveOrRejectDeminimis(encrypted, true);

      expect(axios.post).toHaveBeenCalledWith(
        expect.any(String),
        {
          attachments: [],
          from: '',
          subject: 'PLS notification - Request for De minimis Update Approval',
          to: '<EMAIL>',
          html: expect.stringContaining(
            'test_requestor has approved the De minimis Update for Singapore',
          ),
        },
        expect.any(Object),
      );
    });

    test('called to function sendmail with correct params and return data for rejected case', async () => {
      const encrypted = AesUtils.encryptTaxRate(objectDeminimis);
      UserServices.getUserByNameOrEmail = vi
        .fn()
        .mockResolvedValueOnce({
          success: true,
          data: [
            {
              name: 'test_rejected',
              email: '<EMAIL>',
            },
          ],
        })
        .mockResolvedValueOnce({
          success: true,
          data: [
            {
              name: 'test_requestor',
              email: '<EMAIL>',
            },
          ],
        });
      await EmailService.sendMailApproveOrRejectDeminimis(encrypted, false);

      expect(axios.post).toHaveBeenCalledWith(
        expect.any(String),
        {
          attachments: [],
          from: '',
          subject: 'PLS notification - Request for De minimis Update Rejected',
          to: '<EMAIL>',
          html: expect.stringContaining(
            'test_requestor has rejected the De minimis Update for Singapore.',
          ),
        },
        expect.any(Object),
      );
    });

    test('case exception', async () => {
      const encrypted = AesUtils.encryptTaxRate(objectDeminimis);
      const error = new Error('test');
      UserServices.getUserByNameOrEmail = vi.fn().mockResolvedValue({
        success: false,
        error,
      });
      console.error = vi.fn();
      await EmailService.sendMailApproveOrRejectDeminimis(encrypted, false);
      expect(console.error).toHaveBeenCalledWith(
        'type=ERROR, name=EmailService, function=sendMailApproveOrRejectDeminimis, error=test',
      );
    });
  });

  describe('Test sendRateSheetExpiryNotifyEmail', () => {
    it('should send an email with the correct template', async () => {
      const merchantRateMap = new Map([
        [
          'merchant1',
          {
            merchant: { merchant_name: 'Merchant 1' },
            rates: [
              {
                validity_from: new Date('2022-01-01'),
                validity_to: new Date('2022-12-31'),
              },
            ],
          },
        ],
        [
          'merchant2',
          {
            merchant: { merchant_name: 'Merchant 2' },
            rates: [
              {
                validity_from: new Date('2022-02-01'),
                validity_to: new Date('2022-12-31'),
              },
            ],
          },
        ],
      ]);
      const emailType = ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.MONTH;
      process.env.VALIDITY_EXPIRE_EMAIL_TO = '<EMAIL>';
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue(null);
      // act
      await EmailService.sendRateSheetExpiryNotifyEmail(emailType, merchantRateMap);

      // assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith({
        to: '<EMAIL>',
        from: config.sendgrid.api_email,
        cc: expect.any(String),
        subject: 'Reminder - Rate sheet(s) about to expire 30 days',
        html: expect.any(String),
      });
    });

    it('should send an email with the correct template', async () => {
      const merchantRateMap = new Map([
        [
          'merchant1',
          {
            merchant: { merchant_name: 'Merchant 1' },
            rates: [
              {
                validity_from: new Date('2022-01-01'),
                validity_to: new Date('2022-12-31'),
              },
            ],
          },
        ],
        [
          'merchant2',
          {
            merchant: { merchant_name: 'Merchant 2' },
            rates: [
              {
                validity_from: new Date('2022-02-01'),
                validity_to: new Date('2022-12-31'),
              },
            ],
          },
        ],
      ]);
      const emailType = ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.WEEK;
      process.env.VALIDITY_EXPIRE_EMAIL_TO = '<EMAIL>';
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue(null);
      // act
      await EmailService.sendRateSheetExpiryNotifyEmail(emailType, merchantRateMap);

      // assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith({
        to: '<EMAIL>',
        from: config.sendgrid.api_email,
        cc: expect.any(String),
        subject: 'Reminder - Rate sheet(s) about to expire 7 days',
        html: expect.any(String),
      });
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith(
        expect.objectContaining({
          html: expect.stringContaining('7 days'),
        }),
      );
    });
  });

  describe('notifyUpdateParcelDimensionSuccessfully()', () => {
    it('should return successful response', async () => {
      // Arrange
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue({ success: true });
      const fromEmail = '<EMAIL>';
      const toEmail = '<EMAIL>';
      const numberOfParcels = 10;
      const fileName = 'bulk-parcels-update.csv';
      const timeUpload = new Date().toISOString();

      // Act
      await EmailService.notifyUpdateParcelDimensionSuccessfully(
        fromEmail,
        toEmail,
        numberOfParcels,
        fileName,
        timeUpload,
      );

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalled();
    });

    it('should return error response', async () => {
      // Arrange
      const err = new Error('Send email unsuccessfully');
      EmailUtils.sendEmailSendgrid = vi.fn().mockRejectedValue(err);
      const fromEmail = '<EMAIL>';
      const toEmail = '<EMAIL>';
      const numberOfParcels = 10;
      const fileName = 'bulk-parcels-update.csv';
      const timeUpload = new Date().toISOString();

      // Act
      await EmailService.notifyUpdateParcelDimensionSuccessfully(
        fromEmail,
        toEmail,
        numberOfParcels,
        fileName,
        timeUpload,
      );

      // Assert
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('notifyUpdateParcelDimensionUnsuccessfully()', () => {
    it('should return successful response', async () => {
      // Arrange
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue({ success: true });
      const toEmail = '<EMAIL>';
      const attachment = Buffer.from('bulk-parcels-update.csv');
      const fileName = 'bulk-parcels-update.csv';
      const timeUpload = new Date().toISOString();

      // Act
      await EmailService.notifyUpdateParcelDimensionUnsuccessfully(
        '',
        toEmail,
        attachment,
        fileName,
        timeUpload,
      );

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalled();
    });

    it('should return error response', async () => {
      // Arrange
      const err = new Error('Send email unsuccessfully');
      EmailUtils.sendEmailSendgrid = vi.fn().mockRejectedValue(err);
      const toEmail = '<EMAIL>';
      const attachment = Buffer.from('bulk-parcels-update.csv');
      const fileName = 'bulk-parcels-update.csv';
      const timeUpload = new Date().toISOString();

      // Act
      await EmailService.notifyUpdateParcelDimensionUnsuccessfully(
        '',
        toEmail,
        attachment,
        fileName,
        timeUpload,
      );

      // Assert
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('Test function getInvoiceReportContent', () => {
    const tableDatas = [
      { merchant_name: 'Merchant 1', noOfParcelsForPD: 10, noOfParcelsForDT: 20 },
      { merchant_name: 'Merchant 2', noOfParcelsForPD: 20, noOfParcelsForDT: 10 },
    ];

    it('should generate the correct email template', () => {
      const time = { date: 1, month: 12, year: 2022 };
      const errorListCsv = 'Parcel ID, Error Type\r\n1, Error 1\r\n2, Error 2';

      const mailOp = EmailService.getInvoiceReportContent(tableDatas, time, errorListCsv);

      expect(mailOp.from).toEqual(config.sendgrid.tech_email);
      expect(mailOp.to).toEqual(
        process.env.APP_ENV === 'UAT'
          ? config.sendgrid.parxl_customs_test_email
          : `${config.sendgrid.tech_email}; ${config.sendgrid.api_email}`,
      );
      expect(mailOp.subject).toEqual(
        'Invoice Summary - Expected volumes for invoicing on [01-12-2022]',
      );
      expect(mailOp.attachments).toEqual([
        {
          filename: 'error list.csv',
          content: Buffer.from(errorListCsv),
        },
      ]);
    });

    it('should generate the correct email template without error list', () => {
      const time = { date: 1, month: 12, year: 2022 };

      const mailOp = EmailService.getInvoiceReportContent(tableDatas, time, '');

      expect(mailOp.from).toEqual(config.sendgrid.tech_email);
      expect(mailOp.to).toEqual(
        process.env.APP_ENV === 'UAT'
          ? config.sendgrid.parxl_customs_test_email
          : `${config.sendgrid.tech_email}; ${config.sendgrid.api_email}`,
      );
      expect(mailOp.subject).toEqual(
        'Invoice Summary - Expected volumes for invoicing on [01-12-2022]',
      );
    });
  });

  describe('sendParcelsDimsWeightUpdateResult', () => {
    it('should send email upload fail', async () => {
      const spy = vi.spyOn(EmailUtils, 'sendEmailSendgrid').mockResolvedValue({ success: true });

      await EmailService.sendParcelsDimsWeightUpdateResult(
        '<EMAIL>',
        'test',
        Buffer.from('test'),
      );

      expect(spy.mock.calls[0][0].html).toContain('Some of your updates have errors.');
    });

    it('should send email upload success', async () => {
      const spy = vi.spyOn(EmailUtils, 'sendEmailSendgrid').mockResolvedValue({ success: true });

      await EmailService.sendParcelsDimsWeightUpdateResult('<EMAIL>', 'test');

      expect(spy.mock.calls[0][0].html).not.toContain('Some of your updates have errors.');
    });
  });

  describe('sendMailInformSurchargeRejected function', () => {
    it('should send email informing rejected surcharge successfully', async () => {
      // Arrange
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValueOnce(null);
      AesUtils.CrtCounterDecrypt = vi.fn().mockReturnValue('test');

      // Act
      await EmailService.sendMailInformSurchargeRejected(
        'submitterEmail',
        'submitterName',
        'approverName',
      );

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalled();
    });
  });

  describe('sendMailInformSurchargeApproved function', () => {
    it('should send email informing approved surcharge successfully', async () => {
      // Arrange
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValueOnce(null);
      AesUtils.CrtCounterDecrypt = vi.fn().mockReturnValue('test');

      // Act
      await EmailService.sendMailInformSurchargeApproved(
        'submitterEmail',
        'submitterName',
        'approverName',
      );

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalled();
    });
  });

  describe('notifyMerchantB2CDisabled', () => {
    it('should return successful response', async () => {
      // Arrange
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue({ success: true });
      const username = 'username';
      const mockEmail = '<EMAIL>';
      const merchantName = 'merchantName';

      // Act
      await EmailService.notifyMerchantB2CDisabled({ email: mockEmail, username, merchantName });

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalled();
    });
  });

  describe('sentCommercialInvoiceEmail', () => {
    const mockItem = {
      zipFile: {
        toBuffer: vi.fn().mockResolvedValue(Buffer.from('mock-zip-content')),
      },
      destination: 'US',
      merchantName: 'TestMerchant',
      sequence: 1,
      merchantAcc: 'TM001',
      originCountry: 'GB',
      mawbId: 'TEST-MAWB-123',
    };

    const mockMerchant = {
      commercial_invoice_emails: ['<EMAIL>', '<EMAIL>'],
    };

    beforeEach(() => {
      vi.clearAllMocks();
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue({ success: true });
      AesUtils.CrtCounterEncrypt = vi.fn().mockReturnValue('encrypted-merchant-name');
    });

    it('should send email to opsHub recipients without adding commercial_invoice_emails when recipientType is ops_hub', async () => {
      // Arrange
      const opsHubEmails = ['<EMAIL>', '<EMAIL>'];
      const MerchantService = await import('~/services/merchant-service.js');
      MerchantService.MerchantService.getMerchantByName = vi.fn().mockResolvedValue([mockMerchant]);

      // Act
      await EmailService.sentCommercialInvoiceEmail(opsHubEmails, mockItem, 'ops_hub');

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>;<EMAIL>',
          subject: expect.stringContaining('PARXL - Commercial Invoices for TestMerchant'),
        }),
        expect.objectContaining({
          fromFunction: 'sentCheckMailToMerchant',
        }),
      );

      // Verify commercial_invoice_emails were not added for ops_hub recipients
      expect(MerchantService.MerchantService.getMerchantByName).not.toHaveBeenCalled();
    });

    it('should send email to commercial_invoice recipients and add merchant commercial_invoice_emails when recipientType is commercial_invoice', async () => {
      // Arrange
      const initialEmails = ['<EMAIL>'];
      const MerchantService = await import('~/services/merchant-service.js');
      MerchantService.MerchantService.getMerchantByName = vi.fn().mockResolvedValue([mockMerchant]);

      // Act
      await EmailService.sentCommercialInvoiceEmail(initialEmails, mockItem, 'commercial_invoice');

      // Assert
      expect(MerchantService.MerchantService.getMerchantByName).toHaveBeenCalledWith(
        'encrypted-merchant-name',
        false,
      );
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>;<EMAIL>;<EMAIL>',
          subject: expect.stringContaining('PARXL - Commercial Invoices for TestMerchant'),
        }),
        expect.objectContaining({
          fromFunction: 'sentCheckMailToMerchant',
        }),
      );
    });

    it('should handle merchant without commercial_invoice_emails gracefully', async () => {
      // Arrange
      const initialEmails = ['<EMAIL>'];
      const merchantWithoutCommercialEmails = {};
      const MerchantService = await import('~/services/merchant-service.js');
      MerchantService.MerchantService.getMerchantByName = vi
        .fn()
        .mockResolvedValue([merchantWithoutCommercialEmails]);

      // Act
      await EmailService.sentCommercialInvoiceEmail(initialEmails, mockItem, 'commercial_invoice');

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
        expect.any(Object),
      );
    });

    it('should handle merchant service errors gracefully', async () => {
      // Arrange
      const initialEmails = ['<EMAIL>'];
      const MerchantService = await import('~/services/merchant-service.js');
      MerchantService.MerchantService.getMerchantByName = vi
        .fn()
        .mockRejectedValue(new Error('Service error'));

      // Act
      await EmailService.sentCommercialInvoiceEmail(initialEmails, mockItem, 'commercial_invoice');

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
        expect.any(Object),
      );
    });

    it('should use default recipientType ops_hub when not specified', async () => {
      // Arrange
      const opsHubEmails = ['<EMAIL>'];
      const MerchantService = await import('~/services/merchant-service.js');
      MerchantService.MerchantService.getMerchantByName = vi.fn().mockResolvedValue([mockMerchant]);

      // Act
      await EmailService.sentCommercialInvoiceEmail(opsHubEmails, mockItem);

      // Assert
      // Should not call merchant service for ops_hub type (default)
      expect(MerchantService.MerchantService.getMerchantByName).not.toHaveBeenCalled();
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
        }),
        expect.any(Object),
      );
    });

    it('should return error response', async () => {
      // Arrange
      const err = new Error('Send email unsuccessfully');
      EmailUtils.sendEmailSendgrid = vi.fn().mockRejectedValue(err);
      const username = 'username';
      const mockEmail = '<EMAIL>';
      const merchantName = 'merchantName';

      // Act
      await EmailService.notifyMerchantB2CDisabled({ email: mockEmail, username, merchantName });

      // Assert
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('notifyLspUserB2CDisabled', () => {
    it('should return successful response', async () => {
      // Arrange
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue({ success: true });
      const username = 'username';
      const mockEmail = '<EMAIL>';
      const opHubName = 'opHubName';

      // Act
      await EmailService.notifyLspUserB2CDisabled({ email: mockEmail, username, opHubName });

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalled();
    });

    it('should return error response', async () => {
      // Arrange
      const err = new Error('Send email unsuccessfully');
      EmailUtils.sendEmailSendgrid = vi.fn().mockRejectedValue(err);
      const username = 'username';
      const mockEmail = '<EMAIL>';
      const opHubName = 'opHubName';

      // Act
      await EmailService.notifyLspUserB2CDisabled({
        email: mockEmail,
        username,
        opHubName,
        isSuperuser: true,
      });

      // Assert
      expect(console.error).toHaveBeenCalled();
    });
  });

  describe('getErrorListCsv', () => {
    it('should return successful response', async () => {
      // Arrange
      const mockParcels = [
        {
          id: 'id',
          message: 'message',
        },
      ];

      // Act
      const result = EmailService.getErrorListCsv(mockParcels);

      // Assert
      expect(result).toEqual('Parcel ID, Error Type\r\nid, message');
    });
  });

  describe('sendDriverEmail', () => {
    it('should return successful response', async () => {
      // Arrange
      EmailUtils.sendEmailSendgrid = vi.fn().mockResolvedValue({ success: true });
      const password = 'password';
      const mockEmail = '<EMAIL>';

      // Act
      EmailService.sendDriverEmail(mockEmail, password);

      // Assert
      expect(EmailUtils.sendEmailSendgrid).toHaveBeenCalled();
    });
  });

  describe('sendSAPMail', () => {
    it('should send SAP email successfully with both AR and CTL files', async () => {
      // Arrange
      const spy = vi.spyOn(EmailUtils, 'sendEmailSendgrid').mockResolvedValue({ success: true });
      const bufferData = Buffer.from('AR file content');
      const arFileName = 'AR_File.txt';
      const errString = '';
      const ctlFileBuffer = Buffer.from('CTL file content');
      const ctlFileName = 'CTL_File.txt';

      // Act
      await EmailService.sendSAPMail(bufferData, arFileName, errString, ctlFileBuffer, ctlFileName);

      // Assert
      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          from: config.sendgrid.tech_email,
          to: config.sendgrid.tech_email,
          subject: expect.stringContaining('AR File Transfer Success'),
          html: expect.stringContaining('AR file transfer has been completed successfully'),
          attachments: [
            {
              filename: arFileName,
              content: bufferData.toString('base64'),
            },
            {
              filename: ctlFileName,
              content: ctlFileBuffer.toString('base64'),
            },
          ],
        }),
      );
    });

    it('should send SAP email successfully with only AR file', async () => {
      // Arrange
      const spy = vi.spyOn(EmailUtils, 'sendEmailSendgrid').mockResolvedValue({ success: true });
      const bufferData = Buffer.from('AR file content');
      const arFileName = 'AR_File.txt';
      const errString = '';
      const ctlFileBuffer = null;
      const ctlFileName = '';

      // Act
      await EmailService.sendSAPMail(bufferData, arFileName, errString, ctlFileBuffer, ctlFileName);

      // Assert
      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          from: config.sendgrid.tech_email,
          to: config.sendgrid.tech_email,
          subject: expect.stringContaining('AR File Transfer Success'),
          html: expect.stringContaining('AR file transfer has been completed successfully'),
          attachments: [
            {
              filename: arFileName,
              content: bufferData.toString('base64'),
            },
          ],
        }),
      );
    });

    it('should include error string in the email body if provided', async () => {
      // Arrange
      const spy = vi.spyOn(EmailUtils, 'sendEmailSendgrid').mockResolvedValue({ success: true });
      const bufferData = Buffer.from('AR file content');
      const arFileName = 'AR_File.txt';
      const errString = 'Sample error occurred';
      const ctlFileBuffer = null;
      const ctlFileName = '';

      // Act
      await EmailService.sendSAPMail(bufferData, arFileName, errString, ctlFileBuffer, ctlFileName);

      // Assert
      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          html: expect.stringContaining(
            'The following error occurred when generating the AR file:',
          ),
        }),
      );
      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          html: expect.stringContaining(errString),
        }),
      );
    });
  });
});
