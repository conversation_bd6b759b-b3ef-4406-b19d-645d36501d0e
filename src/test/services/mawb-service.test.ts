import axios from 'axios';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import CountryISOService from '~/services/countryISO-service.js';
import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { DestinationService } from '~/services/destination-services.js';
import { GaylordServices } from '~/services/gaylord.service.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';
import { MawbService } from '~/services/mawb-service.js';
import { MerchantService } from '~/services/merchant-service.js';
import { OperationHubService } from '~/services/operation-hub.service.js';
import { SystemConfigurationsService } from '~/services/systemConfigurationsService.js';

import { ConsoleManifestUtils } from '~/utilities/consoleManifestUtils.js';
import { ExcelUtils } from '~/utilities/excelUtils.js';

import { Daos } from '~/daos/index.js';
import { mawbHelper } from '~/helpers/mawb_helper.js';

vi.useFakeTimers();

Daos.mawb.addItem = vi.fn();
Daos.gaylord.updateItemResolveConflict = vi.fn();

const { createBoxCManifest } = MawbService;
const mockDate = new Date('2021-09-28T10:09:15.990Z');

beforeAll(() => {
  console.error = vi.fn();
  console.log = vi.fn();
  vi.setSystemTime(mockDate);
});

afterAll(() => {
  vi.clearAllTimers();
});

afterEach(() => {
  vi.clearAllMocks();
  MawbService.createBoxCManifest = createBoxCManifest;
});

const mawb_tranx = [
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
    tranx_type: 'created',
    status: 'NEW',
    _ts: '1574664568',
    _partitionKey: '618-73195426',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884db7',
    tranx_type: 'add-gaylord',
    gaylord_no: 'GL-19-00000153',
    _ts: '1574674568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884db8',
    tranx_type: 'remove-gaylord',
    gaylord_no: 'GL-19-00000153',
    _ts: '1574684568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884db9',
    tranx_type: 'add-gaylord',
    gaylord_no: 'GL-19-00000154',
    _ts: '1574694568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884dba',
    tranx_type: 'finish',
    status: 'FINISH',
    finish_date: '2019-12-06T06:58:25.401Z',
    date: '2019-12-06T06:58:25.401Z',
    _ts: '1574704568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884dbb',
    tranx_type: 'fsu',
    type: 'FSU',
    version: '12',
    airline_code: '618',
    mawb_no: '73195426',
    no_of_pieces: '6',
    weight_kg: '1207',
    status: 'FOH',
    raw_message: 'FSU/12\n618-73195426JFKFRA/T6K1207\nFOH/06DEC1500/JFK/T6K1207/GEODIS USA  INC',
    date: '2019-12-06T20:01:39.420Z',
    _ts: '1574704568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884dbc',
    tranx_type: 'fsu',
    type: 'FSU',
    version: '6',
    airline_code: '618',
    mawb_no: '73195426',
    no_of_pieces: '6',
    weight_kg: '1207.0',
    status: 'RCS',
    raw_message: 'FSU/6\n618-73195426JFKFRA/T6K1207.0\nRCS/06DEC1500/JFK/T6K1207.0/GEODIS USA  INC',
    date: '2019-12-06T20:01:43.533Z',
    timestamp: '2019-12-06T15:00:00.000Z',
    _ts: '1574704568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884dbd',
    tranx_type: 'fsu',
    status: 'STASTD',
    type: 'STASTD',
    shipment_number: '618-79819191',
    origin: 'SIN',
    destination: 'FRA',
    local_STD: '1970-01-01T08:00:00',
    local_STA: '1970-01-01T01:00:00',
    pieces: '9',
    part_indicator: '',
    _ts: '1574704568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
  {
    id: '540f1eb4-da3c-718e-f9ba-5081d1884dbe',
    tranx_type: 'fwb',
    type: 'FWB',
    version: '16',
    airline_code: '618',
    mawb_no: '73195426',
    status: 'FWB',
    raw_message:
      'FWB/16\n618-73195426SINFRA/T9K680\nFLT/SQ0326/10\nRTG/FRASQ\nSHP/3230211\n/EES FREIGHT SERVICES PTE LTD\n/9 AIRLINE ROAD 01-22 CARGO AGENTS\n/CHANGI AIRFREIGHT/SINGAPORE\n/SG/819827/TE/65430211/FX/65430213\nCNE\n/MBS LOGISMBS LOGISTICS GMBH\n/HANSESTRASSE 57 51149 KOLN\n/GERMANY/GERMANY\n/DE/00000/TE/4922039338420/FX/49220393389420\nAGT//3230064\n/EES FREIGHT SERVICES PTE LTD\n/CHANGI AIRFREIGHT\nSSR/PLS NTY CNEE IMMED UPON ARRIVAL.\n/ONE CONSOL POUCH ATTACHED.\nACC/GEN/FREIGHT PREPAID\n/GEN/FK-XPS\nCVD/SGD/PP/PP/NVD/NCV/XXX\nRTD/1/P9/K680.0/CQ/W2130/R8.67/T18467.1\n/NC/CONSOLIDATION AS PER\n/2/NC/MANIFEST ATTACHED\n/3/ND//CMT120-100-88/2\n/4/ND//CMT120-100-127/7\nOTH/P/MYC1278XBC383.4CGC8\nPPD/WT18467.1\n/OC1669.4/CT20136.5\nCER/EES FREIGHT SERVICES\nISU/09APR20/SIN/KAGEN\nREF///AGT/EESSIN81/SIN',
    date: '2020-04-09T07:39:16.964Z',
    timestamp: '2020-04-09T07:39:16.964Z',
    _ts: '1574704568',
    _partitionKey: '618-73195426',
    ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
  },
];

describe('test isBoxCMawb function', () => {
  test('When lmd is not BoxC, return false', () => {
    // Assign
    const lmdName = 'Traders Express';

    // Act
    const result = MawbService.isLmdBoxC(lmdName);

    // Assert
    expect(result).toEqual(false);
  });

  test('When lmd is BoxC, return false', () => {
    // Assign
    const lmdName = 'BoxC Vietnam';

    // Act
    const result = MawbService.isLmdBoxC(lmdName);

    // Assert
    expect(result).toEqual(true);
  });
});

describe('test updateBoxCMawb function', () => {
  test('When lmd name is not boxC, do nothing :)', async () => {
    // Assign
    const mockParam = {
      lmdName: 'BoxC Vietnam',
      mawbNo: '123',
      mawbId: 'mawbId',
      gaylordNos: ['GL1', 'GL2'],
      gaylords: [{ id: '1' }, { id: '2' }],
    };
    MawbService.isLmdBoxC = vi.fn().mockReturnValueOnce(false);

    // Act
    await MawbService.updateBoxCMawb(mockParam);

    // Assert
    expect(Daos.mawb.addItem).not.toHaveBeenCalled();
  });

  test("When lmd name is BoxC, add 'pending boxc status' to the mawb", async () => {
    // Assign
    const mockParam = {
      lmdName: 'BoxC Vietnam',
      mawbNo: '123',
      mawbId: 'mawbId',
      gaylords: [
        { id: '1', overpack_id: 123 },
        { id: '2', overpack_id: 456 },
      ],
    };
    const expParam = {
      _partitionKey: '123',
      date: mockDate,
      manifest_id: '444',
      gaylords: ['1', '2'],
      overpacks: [123, 456],
      ref_id: 'mawbId',
      status: 'BoxC Manifest Triggered',
      tranx_type: 'boxc-triggered',
    };
    MawbService.isLmdBoxC = vi.fn().mockReturnValueOnce(true);
    Daos.mawb.addItem = vi.fn().mockResolvedValueOnce('result');
    Daos.gaylord.updateItemResolveConflict = vi.fn().mockResolvedValueOnce('result');
    MawbService.createBoxCManifest = vi.fn().mockResolvedValueOnce('444');

    // Act
    await MawbService.updateBoxCMawb(mockParam);

    // Assert
    expect(Daos.mawb.addItem).toHaveBeenCalledWith(expParam);
  });
});

describe('getMawb', () => {
  test('getMawb:given can run', async () => {
    // Arrange
    axios.get = vi.fn().mockResolvedValue({ data: [{ id: 1 }, { id: 2 }] });

    // Act
    const results = await MawbService.getMawb('1234');

    // Assert
    expect(results).toEqual([{ id: 1 }, { id: 2 }]);
  });
  test('getMawb:given error', async () => {
    // Arrange
    axios.get = vi.fn(() => Promise.reject(false));

    // Act
    const results = await MawbService.getMawb('1234');

    // Assert
    expect(results).toEqual([]);
  });
});

describe('getByMawbNo', () => {
  test('getByMawbNo:given can run', async () => {
    // Arrange
    axios.get = vi.fn().mockResolvedValue({ data: [{ id: 1 }, { id: 2 }] });

    // Act
    const results = await MawbService.getByMawbNo('1234');

    // Assert
    expect(results).toEqual([{ id: 1 }, { id: 2 }]);
  });
  test('getByMawbNo:given error', async () => {
    // Arrange
    axios.get = vi.fn(() => Promise.reject(false));

    // Act
    const results = await MawbService.getByMawbNo('1234');

    // Assert
    expect(results).toEqual([]);
  });
});

describe('createBoxCManifest', () => {
  test('createBoxCManifest:given can run', async () => {
    // Arrange
    axios.post = vi.fn().mockResolvedValue({
      data: {
        success: true,
        manifestId: 1,
        message: 'test',
      },
    });

    // Act
    const results = await MawbService.createBoxCManifest('1234');

    // Assert
    expect(results).toBe(1);
  });
  test('createBoxCManifest:given error', async () => {
    // Arrange
    vi.spyOn(axios, 'post').mockImplementationOnce(() => Promise.reject(new Error('test')));

    // Act

    // Assert
    await expect(MawbService.createBoxCManifest('1234')).rejects.toThrowError(
      'Create boxc manifest failed! "1234"',
    );
  });
});

describe('getCreatedTx', () => {
  test('getCreatedTx:given can run', async () => {
    // Arrange

    // Act
    const results = MawbService.getCreatedTx(mawb_tranx);

    // Assert
    expect(results.status).toEqual('NEW');
  });
  test('getCreatedTx:given empty arr', async () => {
    // Arrange

    // Act
    const results = MawbService.getCreatedTx([]);

    // Assert
    expect(results).toEqual(undefined);
  });
});

describe('getGaylords', () => {
  test('getGaylords:given can run', async () => {
    // Arrange

    // Act
    const results = MawbService.getGaylords(mawb_tranx);

    // Assert
    expect(results).toEqual([
      {
        id: '540f1eb4-da3c-718e-f9ba-5081d1884db9',
        tranx_type: 'add-gaylord',
        gaylord_no: 'GL-19-00000154',
        _ts: '1574694568',
        _partitionKey: '618-73195426',
        ref_id: '540f1eb4-da3c-718e-f9ba-5081d1884db6',
      },
    ]);
  });
});

describe('Test func createCustomManifest', () => {
  const flightInfo = {
    mawb_no: '618-********',
  };
  test('Should return success if coutry is US', async () => {
    // Arrange
    MawbService.getCreatedTx = vi.fn().mockReturnValueOnce({ point_of_discharge: 'XXX' });

    OperationHubService.getAll = vi.fn().mockResolvedValueOnce('allOpHubs');
    MerchantService.getAllMerchants = vi.fn().mockResolvedValueOnce('allMerchants');
    SystemConfigurationsService.getConfigByName = vi.fn().mockResolvedValueOnce('FALSE');
    GaylordServices.getGLsbyMAWB = vi.fn().mockResolvedValueOnce([
      {
        destination_group: 'SIN01',
      },
    ]);

    DestinationService.getAllDestItems = vi.fn().mockResolvedValueOnce([]);

    ManifestItemService.parcelClassification = vi.fn().mockResolvedValueOnce({
      destination: {
        country: 'US',
      },
      allConversionRates: '',
    });

    // Act
    const result = await MawbService.createCustomManifest([], flightInfo);

    // Assert
    expect(result).toEqual({ success: true, data: [] });
  });

  test('Should return success if coutry is UK', async () => {
    // Arrange
    MawbService.getCreatedTx = vi.fn().mockReturnValueOnce({ point_of_discharge: 'XXX' });

    OperationHubService.getAll = vi.fn().mockResolvedValueOnce('allOpHubs');
    MerchantService.getAllMerchants = vi.fn().mockResolvedValueOnce('allMerchants');
    SystemConfigurationsService.getConfigByName = vi.fn().mockResolvedValueOnce('FALSE');
    GaylordServices.getGLsbyMAWB = vi.fn().mockResolvedValueOnce([
      {
        destination_group: 'SIN01',
      },
    ]);

    DestinationService.getAllDestItems = vi.fn().mockResolvedValueOnce([]);

    ManifestItemService.parcelClassification = vi.fn().mockResolvedValueOnce({
      destination: {
        country: 'UK',
      },
      allConversionRates: '',
      parcelsAbove: ['test1'],
      parcelsBelow: ['test2'],
      allParcels: [{ id: 'test1' }, { id: 'test2' }],
    });
    DestinationService.getCBByDGAndPOD = vi.fn().mockResolvedValueOnce([]);
    ManifestItemService.generateCustomManifestOnly = vi.fn().mockResolvedValueOnce([]);

    ManifestItemService.generatePdfProformaInvoice = vi
      .fn()
      .mockResolvedValueOnce({ success: true, buffer: JSON.stringify('test') });
    AzureBlobStorage.uploadFile = vi.fn().mockResolvedValueOnce('test');
    // Act
    await MawbService.createCustomManifest([], flightInfo);

    // Assert
    expect(ManifestItemService.generatePdfProformaInvoice).toBeCalledWith(
      [{ id: 'test1' }],
      'allMerchants',
      'allOpHubs',
    );
  });
  test('Should return success if coutry is MY', async () => {
    // Arrange
    MawbService.getCreatedTx = vi.fn().mockResolvedValueOnce('mawbCreatedTx');

    OperationHubService.getAll = vi.fn().mockResolvedValueOnce('allOpHubs');
    MerchantService.getAllMerchants = vi.fn().mockResolvedValueOnce('allMerchants');
    SystemConfigurationsService.getConfigByName = vi.fn().mockResolvedValueOnce('FALSE');
    GaylordServices.getGLsbyMAWB = vi.fn().mockResolvedValueOnce([
      {
        destination_group: 'SIN01',
      },
    ]);

    DestinationService.getAllDestItems = vi.fn().mockResolvedValueOnce([]);

    ManifestItemService.parcelClassification = vi.fn().mockResolvedValueOnce({
      destination: {
        country: 'MY',
      },
      allConversionRates: '',
      parcelsAbove: ['test1'],
      parcelsBelow: ['test2'],
      allParcels: [{ id: 'test1' }, { id: 'test2' }],
    });
    DestinationService.getCBByDGAndPOD = vi.fn().mockResolvedValueOnce([]);
    ManifestItemService.generateCustomManifestOnly = vi.fn().mockResolvedValueOnce([]);
    ManifestItemService.generatePdfProformaInvoice = vi
      .fn()
      .mockResolvedValueOnce({ success: true, buffer: JSON.stringify('test') });
    AzureBlobStorage.uploadFile = vi.fn().mockResolvedValueOnce('test');

    // Act
    await MawbService.createCustomManifest([], flightInfo);

    // Assert
    expect(ManifestItemService.generatePdfProformaInvoice).toBeCalledWith(
      [{ id: 'test1' }, { id: 'test2' }],
      'allMerchants',
      'allOpHubs',
    );
  });

  test('Should return false if cannot generate custom manifest', async () => {
    // Arrange
    MawbService.getCreatedTx = vi.fn().mockResolvedValueOnce({
      point_of_discharge: 'POD',
    });

    OperationHubService.getAll = vi.fn().mockResolvedValueOnce('allOpHubs');
    MerchantService.getAllMerchants = vi.fn().mockResolvedValueOnce('allMerchants');
    SystemConfigurationsService.getConfigByName = vi.fn().mockResolvedValueOnce('FALSE');
    GaylordServices.getGLsbyMAWB = vi.fn().mockResolvedValueOnce([
      {
        destination_group: 'SIN01',
      },
    ]);

    DestinationService.getAllDestItems = vi.fn().mockResolvedValueOnce([]);

    ManifestItemService.parcelClassification = vi.fn().mockResolvedValueOnce({
      destination: {
        country: 'JP',
      },
      allConversionRates: '',
    });
    DestinationService.getCBByDGAndPOD = vi.fn().mockResolvedValueOnce('customBroker');
    ManifestItemService.generateCustomManifestOnly = vi.fn().mockResolvedValueOnce([]);

    // Act
    const result = await MawbService.createCustomManifest([], flightInfo);

    // Assert
    expect(result).toEqual({
      success: false,
      message: `Cannot generate custom manifest for mawb ${flightInfo.mawb_no}`,
    });
  });

  test('Should return success', async () => {
    // Arrange
    MawbService.getCreatedTx = vi.fn().mockResolvedValueOnce({
      point_of_discharge: 'POD',
    });

    OperationHubService.getAll = vi.fn().mockResolvedValueOnce('allOpHubs');
    MerchantService.getAllMerchants = vi.fn().mockResolvedValueOnce('allMerchants');
    SystemConfigurationsService.getConfigByName = vi.fn().mockResolvedValueOnce('FALSE');
    GaylordServices.getGLsbyMAWB = vi.fn().mockResolvedValueOnce([
      {
        destination_group: 'SIN01',
      },
    ]);

    DestinationService.getAllDestItems = vi.fn().mockResolvedValueOnce([]);

    ManifestItemService.parcelClassification = vi.fn().mockResolvedValueOnce({
      destination: {
        country: 'JP',
      },
      allConversionRates: '',
    });
    DestinationService.getCBByDGAndPOD = vi.fn().mockResolvedValueOnce('customBroker');

    const archiveFile = [
      {
        fileName: 'Manifest.xlsx',
        content: 'content',
      },
    ];
    ManifestItemService.generateCustomManifestOnly = vi.fn().mockResolvedValueOnce(archiveFile);
    AzureBlobStorage.generateBlobNameWithTimestamp = vi.fn().mockReturnValue('');
    AzureBlobStorage.uploadFile = vi.fn();

    // Act
    const result = await MawbService.createCustomManifest([], flightInfo);

    // Assert
    expect(result).toEqual({
      success: true,
      data: archiveFile,
    });
  });

  test('Should log and throw error when server error', async () => {
    // Arrange
    const err = new Error();
    err.message = 'Something went wrong!';
    OperationHubService.getAll = vi.fn().mockRejectedValueOnce(err);
    MerchantService.getAllMerchants = vi.fn().mockRejectedValueOnce(err);
    GaylordServices.getGLsbyMAWB = vi.fn().mockRejectedValueOnce(err);

    try {
      // Act
      await MawbService.createCustomManifest([], flightInfo);
    } catch (error) {
      // Assert
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining(
          'type=ERROR, name=MawbService, function=createCustomManifest, mawbNo=618-********, error=Something went wrong!',
        ),
      );
      expect(error.message).toEqual('Something went wrong!');
    }
  });
});

describe('test generateOdPermitFile function', () => {
  it('should cause no errors default template', async () => {
    // Arrange
    const awbNo = 'AWB01';
    const awbParcels = [
      {
        tracking_id: 'PXLUS0000123F01NU8',
        merchant_name: 'MERCHANT01',
        merchant_declared_currency: 'SGD',
        merchant_account_number: 'US00001',
        item: [
          {
            description: 'BenQ T-Shirt',
            quantity: '2',
            SKU: 'BenQ01',
            origin_country: 'Singapore',
            total_declared_value: '100',
          },
        ],
        operation_hub: 'SIN01',
      },
    ];
    const mockOphub = {
      country_iso2: 'SG',
    };

    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce(mockOphub);
    MawbService.ODPermitDefaultTemplateContent = vi.fn().mockReturnValueOnce([]);
    const mockBuffer = Buffer.from(
      JSON.stringify([{ 'Product SKU': 'BenQ01', 'HS Code': '123456', Description: 'xxyyzz' }]),
    );
    AzureBlobStorage.getLatestFileByTimestamp = vi.fn().mockResolvedValueOnce({
      name: 'US00001_SKU/US00001_SKU-**************.json',
    });
    AzureBlobStorage.downloadBlobBuffer = vi.fn().mockResolvedValueOnce(mockBuffer);
    ExcelUtils.createBuffer = vi.fn();
    AzureBlobStorage.uploadFile = vi.fn();

    // Act
    const res = await MawbService.generateOdPermitFile(awbNo, awbParcels);

    // Assert
    expect(MawbService.ODPermitDefaultTemplateContent).toHaveBeenCalledWith({ awbNo, awbParcels });
    expect(ExcelUtils.createBuffer).toHaveBeenCalled();
    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
    expect(res).toBeInstanceOf(Object);
    expect(res).toHaveProperty('success', true);
  });

  it('should cause no errors korea template', async () => {
    // Arrange
    const awbNo = 'AWB01';
    const awbParcels = [
      {
        tracking_id: 'PXLUS0000123F01NU8',
        merchant_name: 'MERCHANT01',
        merchant_declared_currency: 'SGD',
        merchant_account_number: 'US00001',
        item: [
          {
            description: 'BenQ T-Shirt',
            quantity: '2',
            SKU: 'BenQ01',
            origin_country: 'Singapore',
            total_declared_value: '100',
          },
        ],
        operation_hub: 'ICN01',
      },
    ];
    const mockOphub = {
      country_iso2: 'KR',
    };

    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce(mockOphub);
    MawbService.ODPermitKoreaTemplateContent = vi.fn().mockReturnValueOnce([]);
    const mockBuffer = Buffer.from(
      JSON.stringify([{ 'Product SKU': 'BenQ01', 'HS Code': '123456', Description: 'xxyyzz' }]),
    );
    AzureBlobStorage.getLatestFileByTimestamp = vi.fn().mockResolvedValueOnce({
      name: 'US00001_SKU/US00001_SKU-**************.json',
    });
    AzureBlobStorage.downloadBlobBuffer = vi.fn().mockResolvedValueOnce(mockBuffer);
    ExcelUtils.createBuffer = vi.fn();
    AzureBlobStorage.uploadFile = vi.fn();

    // Act
    const res = await MawbService.generateOdPermitFile(awbNo, awbParcels);

    // Assert
    expect(MawbService.ODPermitKoreaTemplateContent).toHaveBeenCalledWith({ awbNo, awbParcels });
    expect(ExcelUtils.createBuffer).toHaveBeenCalled();
    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
    expect(res).toBeInstanceOf(Object);
    expect(res).toHaveProperty('success', true);
  });

  it('should cause errors on uploading file', async () => {
    // Arrange
    const awbNo = 'AWB01';
    const awbParcels = [
      {
        tracking_id: 'PXLUS0000123F01NU8',
        merchant_name: 'MERCHANT01',
        merchant_declared_currency: 'SGD',
        merchant_account_number: 'US00001',
        item: [
          {
            description: 'BenQ T-Shirt',
            quantity: '2',
            SKU: 'BenQ01',
            origin_country: 'Singapore',
            total_declared_value: '100',
          },
        ],
      },
    ];

    const mockOphub = {
      country_iso2: 'SG',
    };

    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce(mockOphub);
    MawbService.ODPermitDefaultTemplateContent = vi.fn().mockReturnValueOnce([]);
    ExcelUtils.createBuffer = vi.fn();
    AzureBlobStorage.uploadFile = vi.fn().mockImplementationOnce(() => {
      throw new Error('Upload file error');
    });

    // Act
    const res = await MawbService.generateOdPermitFile(awbNo, awbParcels);

    // Assert
    expect(ExcelUtils.createBuffer).toHaveBeenCalled();
    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
    expect(res).toBeInstanceOf(Object);
    expect(res).toHaveProperty('success', false);
  });
});

describe('test createConsoleManifestAndUpload', () => {
  beforeEach(() => {
    MawbService.getMawbTrxDataByMawbNo = vi.fn().mockResolvedValue({
      mawbTx: [
        {
          tranx_type: 'created',
        },
        {
          status: 'FINISH',
        },
      ],
      gaylords: [],
    });
    MawbService.getCreatedTx = vi.fn().mockReturnValue({
      tranx_type: 'created',
      status: 'NEW',
      latest_tracking_status: 'FINISH',
      first_sector: {
        flight_number: 'flight_number',
      },
    });
    DestinationService.getCBByPOD = vi.fn().mockResolvedValue({
      name: 'CB',
    });
    vi.useRealTimers();
    AzureBlobStorage.uploadFile = vi.fn();
  });

  test('should run normally as expected for SG', async () => {
    const detailsMock = [{}, {}];
    ConsoleManifestUtils.getDetails = vi.fn().mockReturnValue(detailsMock);
    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValueOnce([
      {
        operation_hub: 'SIN01',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce({
      operation_hub: 'SIN01',
      airport_code: 'SIN',
      country: 'Singapore',
      country_iso2: 'SG',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('SGD');
    CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValue({
      origin_currency_selected: 'SGD',
      destination_currency: 4.2,
      valid_from: new Date(),
      valid_to: new Date(),
      currency: 'MYR',
    });
    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'Singapore',
      },
      {
        merchant_account_number: '2',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'Singapore',
      },
    ]);

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  test('should run normally as expected for UK', async () => {
    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValue([
      {
        operation_hub: 'LDN01',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
        item: [
          {
            seq_no: 1,
            description: 'toilet',
            quantity: '1',
            CPC: '123',
            subtotal_weight: '1',
            total_declared_value: '3',
          },
        ],
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValue({
      operation_hub: 'LDN01',
      airport_code: 'LHR',
      country: 'United Kingdom',
      country_iso2: 'GB',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('GBP');
    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
        eori: '123',
      },
      {
        merchant_account_number: '2',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
        eori: '123',
      },
    ]);
    CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValue({
      origin_currency_selected: 'GBP',
      destination_currency: 4.2,
      valid_from: new Date(),
      valid_to: new Date(),
      currency: 'MYR',
    });
    ConsoleManifestUtils.getDetails = vi.fn();
    ConsoleManifestUtils.generateUKExcel = vi.fn();

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  test('should run normally as expected for HK', async () => {
    const detailsMock = [{}, {}];
    ConsoleManifestUtils.getDetails = vi.fn().mockReturnValue(detailsMock);
    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValueOnce([
      {
        operation_hub: 'HKG01',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
        item: [
          {
            hs_code: 'hs_code',
            merchant_declared_currency: 'merchant_declared_currency',
            merchant_account_number: 'merchant_account_number',
          },
        ],
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce({
      operation_hub: 'HKG01',
      airport_code: 'HKG',
      country: 'Hong Kong',
      country_iso2: 'HK',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('HKD');
    CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValue({
      origin_currency_selected: 'HKD',
      destination_currency: 4.2,
      valid_from: new Date(),
      valid_to: new Date(),
      currency: 'MYR',
    });
    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
      {
        merchant_account_number: '2',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
    ]);
    vi.spyOn(ConsoleManifestUtils, 'generateCustomManifestPDF').mockResolvedValueOnce(
      Buffer.from('test'),
    );

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  test('should run normally as expected for TW', async () => {
    const detailsMock = [{}, {}];
    ConsoleManifestUtils.getDetails = vi.fn().mockReturnValue(detailsMock);
    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValueOnce([
      {
        operation_hub: 'HKG01',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
        merchant_name: 'merchant_name',
        item: [
          {
            hs_code: 'hs_code',
            merchant_declared_currency: 'merchant_declared_currency',
            merchant_account_number: 'merchant_account_number',
          },
        ],
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce({
      operation_hub: 'TPE01',
      airport_code: 'TPE',
      country: 'Taiwan',
      country_iso2: 'TW',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('TWD');
    CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValue({
      origin_currency_selected: 'TWD',
      destination_currency: 4.2,
      valid_from: new Date(),
      valid_to: new Date(),
      currency: 'MYR',
    });
    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
      {
        merchant_account_number: '2',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
    ]);
    ConsoleManifestUtils.exportConsoleManifestForTaiwanExport = vi.fn();
    ConsoleManifestUtils.exportConsoleManifestForTaiwanExportNoDeclaration = vi.fn();

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  test('should run normally as expected for KR with dual conversion rates', async () => {
    const detailsMock = [{}, {}];
    ConsoleManifestUtils.getDetails = vi.fn().mockReturnValue(detailsMock);
    ConsoleManifestUtils.generateKoreaDetailsWithUSDConversion = vi.fn().mockReturnValue([
      {
        merchant_declared_currency: 'USD',
        item: [{ total_declared_value: 100 }],
      },
    ]);
    ConsoleManifestUtils.generateKoreaExcel = vi.fn().mockReturnValue(Buffer.from('excel'));

    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValueOnce([
      {
        operation_hub: 'ICN01',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
        item: [
          {
            hs_code: 'hs_code',
            merchant_declared_currency: 'MYR',
            merchant_account_number: 'merchant_account_number',
          },
        ],
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce({
      operation_hub: 'ICN01',
      airport_code: 'ICN',
      country: 'South Korea',
      country_iso2: 'KR',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('KRW');

    CurrencyConversionService.getExchangeRateOldFormat = vi
      .fn()
      .mockResolvedValueOnce({
        origin_currency_selected: 'MYR',
        destination_currency: 300.5,
        valid_from: new Date(),
        valid_to: new Date(),
        currency: 'KRW',
      })
      .mockResolvedValueOnce({
        origin_currency_selected: 'MYR',
        destination_currency: 0.24,
        valid_from: new Date(),
        valid_to: new Date(),
        currency: 'USD',
      });

    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'South Korea',
      },
    ]);

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(CurrencyConversionService.getExchangeRateOldFormat).toHaveBeenCalledTimes(2);
    expect(CurrencyConversionService.getExchangeRateOldFormat).toHaveBeenCalledWith(
      'MYR',
      'KRW',
      expect.any(Date),
    );
    expect(CurrencyConversionService.getExchangeRateOldFormat).toHaveBeenCalledWith(
      'MYR',
      'USD',
      expect.any(Date),
    );

    expect(ConsoleManifestUtils.generateKoreaDetailsWithUSDConversion).toHaveBeenCalledWith(
      expect.any(Array),
      expect.any(Array),
      'KRW',
    );

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  test('should run normally as expected for MY', async () => {
    const detailsMock = [{}, {}];
    ConsoleManifestUtils.generateMalaysiaDetails = vi.fn().mockReturnValue(detailsMock);
    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValueOnce([
      {
        operation_hub: 'KUL99',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
        item: [
          {
            hs_code: 'hs_code',
            merchant_declared_currency: 'merchant_declared_currency',
            merchant_account_number: 'merchant_account_number',
          },
        ],
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce({
      operation_hub: 'KUL99',
      airport_code: 'KUL',
      country: 'Malaysia',
      country_iso2: 'MY',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('MYR');
    CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValue({
      origin_currency_selected: 'MYR',
      destination_currency: 1,
      valid_from: new Date(),
      valid_to: new Date(),
      currency: 'MYR',
    });
    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
      {
        merchant_account_number: '2',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
    ]);
    vi.spyOn(ConsoleManifestUtils, 'generateCustomManifestPDF').mockResolvedValue(
      Buffer.from('buffer'),
    );

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  test('should run normally as expected for MY using PDF kit', async () => {
    SystemConfigurationsService.getConfigByName = vi.fn().mockResolvedValue('PDF KIT');
    const detailsMock = [{}, {}];
    ConsoleManifestUtils.generateMalaysiaDetails = vi.fn().mockReturnValue(detailsMock);
    ConsoleManifestUtils.generateCustomManifestPDF = vi
      .fn()
      .mockResolvedValue(Buffer.from('buffer'));
    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValueOnce([
      {
        operation_hub: 'KUL99',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
        item: [
          {
            hs_code: 'hs_code',
            merchant_declared_currency: 'merchant_declared_currency',
            merchant_account_number: 'merchant_account_number',
          },
        ],
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce({
      operation_hub: 'KUL99',
      airport_code: 'KUL',
      country: 'Malaysia',
      country_iso2: 'MY',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('MYR');
    CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValue({
      origin_currency_selected: 'MYR',
      destination_currency: 1,
      valid_from: new Date(),
      valid_to: new Date(),
      currency: 'MYR',
    });
    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
      {
        merchant_account_number: '2',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'United Kingdom',
      },
    ]);

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  test('should run with custom broker V2 normally as expected', async () => {
    const detailsMock = [{}, {}];
    ConsoleManifestUtils.getDetails = vi.fn().mockReturnValue(detailsMock);
    ManifestItemService.getManifestItemsFromMultipleGaylords = vi.fn().mockResolvedValueOnce([
      {
        operation_hub: 'SIN01',
        merchant_account_number: '1',
        merchant_declared_currency: 'MYR',
      },
    ]);
    OperationHubService.getOperationHubByName = vi.fn().mockResolvedValueOnce({
      operation_hub: 'SIN01',
      airport_code: 'SIN',
      country: 'Singapore',
      country_iso2: 'SG',
    });
    MawbService.getCreatedTx = vi.fn().mockReturnValue({
      point_of_discharge: 'SIN',
      tranx_type: 'created',
      status: 'NEW',
      latest_tracking_status: 'FINISH',
    });
    CountryISOService.getCurrencyByCountry = vi.fn().mockReturnValue('SGD');
    CurrencyConversionService.getExchangeRateOldFormat = vi.fn().mockResolvedValue({
      origin_currency_selected: 'SGD',
      destination_currency: 4.2,
      valid_from: new Date(),
      valid_to: new Date(),
      currency: 'MYR',
    });
    MerchantService.getMerchantsByNames = vi.fn().mockResolvedValue([
      {
        merchant_account_number: '1',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'Singapore',
      },
      {
        merchant_account_number: '2',
        merchant_name: 'merchant_name',
        street: 'street',
        city: 'city',
        state: 'state',
        postal_code: 'postal_code',
        country: 'Singapore',
      },
    ]);

    await MawbService.createConsoleManifestAndUpload('mawbNo', 'fileName');

    expect(AzureBlobStorage.uploadFile).toHaveBeenCalled();
  });

  describe('getMultipleMawbs', () => {
    it('should group MAWBs by _partitionKey', async () => {
      // Arrange
      const mockDate = new Date('2024-11-19T10:00:00.000Z');
      const mockTs = 1_731_316_800; // Mocked timestamp
      const mockQuery = { query: 'mockQueryValue' };
      const mockMawbNos = ['618-********', '618-********'];
      const mockFields = ['tranx_type', 'status', 'id', '_partitionKey'];

      const mockMawbs = [
        { _partitionKey: '618-********', tranx_type: 'created', status: 'NEW', id: '1' },
        {
          _partitionKey: '618-********',
          tranx_type: 'add-gaylord',
          gaylord_no: 'CTN-24-00150284',
          id: '2',
        },
        { _partitionKey: '618-********', tranx_type: 'created', status: 'NEW', id: '3' },
        {
          _partitionKey: '618-********',
          tranx_type: 'start-scanning',
          gaylord_no: 'CTN-24-00150295',
          id: '4',
        },
      ];

      vi.spyOn(global, 'Date').mockImplementation(() => mockDate as unknown as string);
      vi.spyOn(mawbHelper, 'getMawbRecycleTs').mockReturnValue(mockTs);
      vi.spyOn(mawbHelper, 'getMawbQuery').mockReturnValue(mockQuery);
      vi.spyOn(Daos.mawb, 'find').mockResolvedValue(mockMawbs);

      // Act
      const result = await MawbService.getMultipleMawbs(mockMawbNos, mockFields);

      // Assert
      expect(mawbHelper.getMawbRecycleTs).toHaveBeenCalledWith(mockDate);
      expect(mawbHelper.getMawbQuery).toHaveBeenCalledWith(mockMawbNos, mockTs, mockFields);
      expect(Daos.mawb.find).toHaveBeenCalledWith(mockQuery);
      expect(result).toEqual({
        '618-********': [
          { _partitionKey: '618-********', tranx_type: 'created', status: 'NEW', id: '1' },
          {
            _partitionKey: '618-********',
            tranx_type: 'add-gaylord',
            gaylord_no: 'CTN-24-00150284',
            id: '2',
          },
        ],
        '618-********': [
          { _partitionKey: '618-********', tranx_type: 'created', status: 'NEW', id: '3' },
          {
            _partitionKey: '618-********',
            tranx_type: 'start-scanning',
            gaylord_no: 'CTN-24-00150295',
            id: '4',
          },
        ],
      });
    });
  });
});
