export const CONSTANTS = {
  NUMBER_OF_PARCEL_MESSAGES_TO_QUEUE: 1000,
  HOURS_IN_MILISECONDS: 60 * 60 * 1000,
  PLS_BOOKING_REFERENCE_NUMBER_PREFIX: 'PXL',
  CUSTOM_BROKER_CODES: {
    CB_FBE_YYZ: 'CB_FBE_YYZ',
    CB_NJV_KUL: 'CB_NJV_KUL',
  },
  BOOKING_MONITOR_STATUS: {
    PLS_REJECT: 'Rejected by <PERSON><PERSON>',
    LMD_REJECT: 'L<PERSON> reject booking',
    LMD_RECEIVE: '<PERSON><PERSON> receive booking',
    RECEIVED_AT_WAREHOUSE: 'Received at Warehouse',
    PACKED_TO_GAYLORD: 'Packed to <PERSON><PERSON>',
    PACKED_TO_MAWB: 'Packed to MAW<PERSON>',
    PICKED_UP: 'Picked up',
    EXPIRED: 'Expired',
    BOOKED: 'Booked',
    CANCELLED: 'Cancelled',
    SORTED: 'Sorted',
    HOLD_REJECTED_BY_LMD: 'HOLD: Rejected by <PERSON><PERSON>',
    HOLD_HS_MISSING: 'HOLD: HS Missing',
    HOLD_DIMS_MISSING: 'HOLD: Dimension & Weight Missing',
    HOLD_HS_DIMS_MISSING: 'HOLD: HS and Dimension & Weight Missing',
    HOLD_CANCELLED: 'HOLD: Cancelled',
    HOLD_SPLIT_PARCEL: 'HOLD: Split Parcel',
    HOLD_PENDING_BOOKING_UPDATES: 'HOLD: Pending Booking Updates',
    PENDING_BOOKING_UPDATES: 'Pending Booking Updates',
    SPLIT_PARCEL_CREATED: 'Split Parcel: Created',
    SPLIT_PARCEL_COMPLETED_PROCESSING: 'Split Parcel: Completed Processing',
    UPDATE_OF_RECIPIENT_DETAILS: 'Update of Recipient Details',
    DAMAGE_INTERNAL: 'Damage (Internal)',
    DANGEROUS_GOOD_INTERNAL: 'DG (Internal)',
    ADDRESS_CHANGE: 'Address Change',
    LOST: 'Lost',
    DAMAGED: 'Damaged',
    TRANSLATED: 'Translated',
    RELABELLED: 'Relabelled',
    REPACKAGED: 'Repackaged',
    DISPOSED: 'Disposed',
    RETURNED_TO_MERCHANT: 'Returned to merchant',
  },
  USER_ACTIVITY: {
    PLATFORM: {
      MP_PORTAL: 'Merchant Portal',
      ADMIN_PORTAL: 'Admin Portal',
      LSP_PORTAL: 'LSP Portal',
      FMD_PORTAL: 'FMD Portal',
    },
    CATEGORY: {
      BOOKINGS: 'Bookings',
      INVOICES: 'Invoices',
      WAREHOUSE: {
        PARCELS: 'Parcels',
        CONTAINERS: 'Containers',
        MAWBS: 'MAWBs',
      },
      CONFIGURATIONS: {
        DISTRIBUTIONS: {
          OPERATION_HUBS: 'Configurations > Distributions > Operation Hubs',
          CUSTOM_BROKERS: 'Configurations > Distributions > Custom Brokers',
          FIRST_MILES: 'Configurations > Distributions > First Miles',
          ZONES: 'Configurations > Distributions > Zones',
          COUNTRIES: 'Configurations > Distributions > Countries',
          AIR_FREIGHT_RATES: 'Configurations > Distributions > Air Freight Rates',
          MANUAL_DT: 'Configurations > Distributions > Manual DT',
          DUTY_AND_TAX: 'Configurations > Distributions > Duty and Tax',
        },
        DESTINATION_GROUPS: 'Configurations > Destination Groups',
        RATES: 'Configurations > Rates',
        MERCHANTS: 'Configurations > Merchants',
        PARCELS: {
          PARCEL_UPDATE: 'Configurations > Parcels > Parcel Update',
          REPOST_LOST_DAMAGE: 'Configurations > Parcels > Report Lost/Damage',
          FORCE_TRIGGER_STATUS: 'Configurations > Parcels > Force Trigger Status',
          CANCEL_PARCEL: 'Configurations > Parcels > Cancel Parcel',
          PENDING_TRACKING_IDS: 'Configurations > Parcels > Pending Tracking IDs',
          COMMERCIAL_INVOICES: 'Configurations > Parcels > Commercial Invoices',
        },
        SURCHARGE: {
          FUEL: 'Configurations > Surcharge > Fuel',
          REPORT_EXCEPTION: 'Report Exception',
          HISTORY_OF_EXCEPTION: 'Configurations > Surcharge > History of Exception',
          EXCEPTION_RATES: 'Configurations > Surcharge > Exception Rates',
        },
        FINANCE: {
          EXCHANGE_RATES: 'Configurations > Finance > Exchange Rates',
          TAX_RATES: 'Configurations > Finance > Tax Rates',
          INVOICING: 'Configurations > Finance > Invoicing',
          MAWB_AMOUNT_UPLOAD: 'Configurations > Finance > MAWB Amount Upload',
          HS_CODE: 'Configurations > Finance > HS Code',
          TAX_CODE: 'Configurations > Finance > Tax Code',
        },
        SYSTEM_CONFIGURATIONS: {
          SYSTEM_CONFIGURATIONS: 'Configurations > System Configurations',
          LMD_CONFIGURATIONS: 'Configurations > System Configurations > LMD Configurations',
        },
        USER_ACCOUNTS: 'User Accounts',
        LOGINS_AND_LOGOUTS: 'Logins and Logouts',
        OTHER_ACTIVITIES: {
          REPORTS: 'Other Activities > Reports',
          SEARCH_HISTORIES: 'Search Histories',
        },
      },
    },
    DETAILS: {
      REQUEST_PAYLOAD: 'Request Payload',
      MERCHANT: {
        ID: 'Merchant ID',
        NAME: 'Merchant Name',
      },
      OPERATION_HUB: {
        ID: 'Operation Hub ID',
        NAME: 'Operation Hub Name',
      },
      USER: {
        ID: 'User ID',
        NAME: 'User Name',
      },
      FMD_DRIVER: {
        ID: 'FMD Driver ID',
        NAME: 'FMD Driver Name',
      },
      LMD: {
        ID: 'LMD ID',
      },
    },
    ACTION: {
      NAME: {
        LOGGED_IN: 'logged in',
        LOGGED_OUT: 'logged out',
        BOOKED: 'booked',
        CANCELLED: 'cancelled',
        CREATED: 'created',
        DISABLED: 'disabled',
        ENABLED: 'enabled',
        CLOSED: 'closed',
        APPROVED: 'approved',
        DISAPPROVED: 'disapproved',
        PRINTED: 'printed',
        SEARCHED: 'searched',
        FINISHED: 'finished',
        DELETED: 'deleted',
        WITHDREW: 'withdrew',
        EDITED: 'edited',
        SCANNED: 'scanned',
        ADDED: 'added',
        REMOVED: 'removed',
        UPLOADED: 'uploaded',
        CHANGED: 'changed',
        UPDATED: 'updated',
        SWITCHED: 'switched',
        DOWNLOADED: 'downloaded',
        SUSPENDED: 'suspended',
        ACTIVATED: 'activated',
        DEACTIVATED: 'deactivated',
      },
      SUBJECT_TYPE: {
        BOOKING: 'booking(s)',
        SHIPMENT: 'shipment(s)',
        PARCEL: 'parcel(s)',
        CONTAINER: 'container(s)',
        MAWB: 'mawb(s)',
        PD_INVOICE: 'PD invoice(s)',
        DT_INVOICE: 'DT invoice(s)',
        COMMERCIAL_INVOICE: 'commercial invoice(s)',
        OPERATION_HUB: 'operation hub(s)',
        CUSTOM_BROKER: 'custom_brokers(s)',
        COUNTRY: 'country (or countries)',
        DESTINATION_COUNTRY: 'destination country (or destination countries)',
        POINT_OF_DISCHARGE: 'point(s) of discharge',
        LMD: 'lmd(s)',
        SERVICE_OPTION: 'service option(s)',
        ZONE: 'zone(s)',
        KBN: 'kbn(s)',
        AIR_FREIGHT_RATE: 'air freight rate(s)',
        HS_CODE: 'hs code(s)',
        RATE_SHEET: 'rate sheet(s)',
        SYSTEM_CONFIGURATION: 'system configuration(s)',
        LMD_CONFIGURATION: 'lmd configuration(s)',
        USER_ACCOUNT: 'user account(s)',
        MERCHANT_ACCOUNT: 'merchant account(s)',
        LSP_ACCOUNT: 'lsp account(s)',
        FMD_ACCOUNT: 'fmd account(s)',
        FMD: {
          COMPANY: 'FMD company',
          DRIVER: 'FMD driver(s)',
        },
        RECEIVE_EMAIL: 'receiving email',
        SKU_FILE: 'SKU file',
        PARCEL_DIMS_WEIGHT_UPDATE_DOCCUMENT: 'parcel dims weight update document',
      },
    },
  },
  PARCEL_STATUS: [
    'Booked',
    'LMD receive booking',
    'LMD reject booking',
    'Received at Warehouse',
    'Picked up',
    'Sorted',
    'Pending Booking Updates',
  ],
  AZURE_STORAGE_QUEUE_NAME: {
    REBOOKED_TRACKING_ID_CANCELLATION: 'rebooked-tracking-id-cancellation',
    MERCHANT_WEBHOOK_QUEUE: 'merchant-status-webhook',
    PARCELS_UPDATE_AFTER_INVOICING: 'parcels-update-after-invoicing',
    PARCELS_UPDATE_PARENT_SPLIT_STATUS: 'parcels-update-parent-split-status',
    LMD_BOOKING: 'lmd-booking',
    BRIDGES_LSP_QUEUE: 'bridges-shipment',
  },
  REBOOK_STATUS: {
    IN_PROGRESS: 'IN PROGRESS',
    COMPLETED: 'COMPLETED',
    EXCEPTION: 'EXCEPTION',
  },
  MASTER_LIST_UPLOAD_STATUS: {
    FAILED: 'FAILED',
    IN_PROGRESS: 'IN PROGRESS',
    COMPLETED: 'COMPLETED',
  },
  MANUAL_DT_UPLOAD: {
    PARTITION_KEY: 'dt_manual_upload',
  },
  RAISE_SURCHARGE: {
    PARTITION_KEY: 'surcharge_upload',
    PARTITION_KEY_ITEMS: 'manually_raise',
    STATUS: {
      PENDING: 'Pending',
      APPROVED: 'Approved',
      REJECTED: 'Rejected',
    },
  },
  UPLOAD_STATUS: {
    PENDING: 'Pending',
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
  },
  HS_CODE_PARTITION_KEY: {
    MASTER_LIST_UPLOAD: 'MASTER_LIST_UPLOAD',
  },
  DELTA_MASTER_LIST_FILE_NAME: 'Delta_Master_List.csv',
  MAWB_PREFIX: '618-',
  RUN_DATE: {
    FIRST_OF_MONTH: '1st of next month',
    END_OF_MONTH: 'last day of the same month',
  },
  PARCEL_EXCEPTION_STATUS: [
    'HOLD: Rejected by LMD',
    'HOLD: HS Missing',
    'HOLD: Dimension & Weight Missing',
    'HOLD: HS and Dimension & Weight Missing',
    'HOLD: Cancelled',
  ],
  MANUAL_DT_ERROR_LIST: {
    invalid_parcel_id: `Parcel's ID is not found. We accept both PXL and LMD ID.`,
    invalid_tax_currency: 'Invalid. Please input appropriate currency.',
    invalid_value:
      'Invalid. Please ensure values or rate percentage declared in numerical values only.',
    invalid_tax_value_and_tax_rate_percent:
      'Invalid. Please input appropriate tax_rate_percent or tax_value. They cannot exist simultaneously.',
    other_errors: 'System Error. Please check with L2 or product team',
  },
  SURCHARGE_ERROR_LIST: {
    invalid_parcel_id: `Parcel's ID is not found. We accept both PXL, LMD ID and merchant account number.`,
    invalid_charge_subtype: 'Surcharge not found.',
    invalid_charge_value: 'Invalid charge value. Please input up to 2 decimal placing.',
    invalid_charge_currency: 'Invalid Currency.',
    other_errors: 'System Error. Please check with L2 or product team',
  },
  ANOMALY_REGEX: /^[^!"#$%&'()*+:;<=>@[\\\]^`{|}~´‘’-]+$/,
  ANOMALY_ARRAY: [
    '~',
    '!',
    '@',
    '#',
    '$',
    '%',
    '^',
    '&',
    '*',
    '(',
    ')',
    '-',
    '+',
    '=',
    '{',
    '}',
    '[',
    ']',
    '|',
    ':',
    ';',
    '"',
    `'`,
    '´',
    '’',
    '‘',
    '`',
    '<',
    '>',
  ],
  COMMERCIAL_INVOICE_COUNTRY: {
    TW: {
      currency: 'TWD',
      label: 'TWD',
    },
    MY: {
      currency: 'MYR',
      label: 'RM',
    },
    GB: {
      currency: 'GBP',
      label: 'GBP',
    },
    KR: {
      currency: 'USD',
      label: 'USD',
    },
  },
};

export * from './country.constant.js';
