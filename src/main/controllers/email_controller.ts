import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { ArchiverService } from '~/services/archiver.service.js';
import { BatchJobService } from '~/services/batchJob-service.js';
import { CBService } from '~/services/cb.service.js';
import { DestinationService } from '~/services/destination-services.js';
import { EmailService } from '~/services/email.services.js';
import { GaylordServices } from '~/services/gaylord.service.js';
import { InvoicingServices } from '~/services/invoicing-services.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';
import { MawbService } from '~/services/mawb-service.js';
import { MerchantService } from '~/services/merchant-service.js';
import { OperationHubService } from '~/services/operation-hub.service.js';
import PdInvoiceService from '~/services/pd-invoice-service.js';
import { RateServices } from '~/services/rate-services.js';
import { SftpService } from '~/services/sftp.service.js';
import { SystemConfigurationsService } from '~/services/systemConfigurationsService.js';

import { DtV2InvoicingUtils } from '~/utilities/dt-v2-invoicing-utils.js';
import { EmailUtils } from '~/utilities/emailUtils.js';
import { AppLogger } from '~/utilities/logUtils.js';

import { config } from '~/configs/index.js';
import { CONSTANTS } from '~/constants/index.js';

const logger = new AppLogger({ name: 'EmailController', business: ENUM.FunctionName.EMAIL });

export default class EmailController {
  async remindCBZipPassword(_req, res) {
    try {
      const data = await CBService.remindCBZipPassword();

      return res.json({
        success: true,
        message:
          'The passwords for the manifest documents have been safely delivered to our trusted custom brokers.',
        data,
      });
    } catch (error) {
      logger.error({
        message:
          'Failed to send emails to custom brokers to remind the change of the password for the manifest documents',
        error,
      });

      return res.json({
        success: false,
        message: error.message,
      });
    }
  }

  /**
   * Return object has buffer
   * @param req
   * @param res
   * @returns {Promise<void>}
   */
  async generateCustomClearanceDocumentAndSendMail(req, res) {
    const { MAWB: mawbNo, job } = req.body;
    logger.info({ message: 'start sending CB email part 2', mawbNo });

    try {
      const [allOpHubs, allMerchants, gaylords] = await Promise.all([
        OperationHubService.getAll(),
        MerchantService.getAllMerchants(false),
        GaylordServices.getGLsbyMAWB(mawbNo),
      ]);

      const destinations = await DestinationService.getAllDestItems();
      const parcelClassificationData = await ManifestItemService.parcelClassification(
        gaylords,
        allMerchants,
        destinations,
      );
      const { destination, allParcels, allConversionRates } = parcelClassificationData;
      const countryCode = destination.country;

      const lmdName = gaylords[0].lmd_provider_name;

      if (countryCode === 'US' && lmdName !== ENUM.lmdNames.LMG) {
        logger.info({ message: 'Skip sending part 2 email for US', lmd: lmdName, mawbNo });
        await BatchJobService.completeBatchJob(job.id, job._partitionKey);

        return res.json({
          requestSuccess: true,
        });
      }

      const mawbTx = await MawbService.getByMawbNo(mawbNo);
      const createTx = MawbService.getCreatedTx(mawbTx);
      const mawb = {
        id: createTx._partitionKey,
        tracking_status: mawbTx,
        point_of_discharge: createTx?.point_of_discharge,
      };
      const isLMDAuPost = gaylords[0].lmd_provider_name === ENUM.lmdNames.AusPost;
      const isBoxCJapan = gaylords[0].lmd_provider_name === ENUM.lmdNames.BoxCJapan;
      const isNZPost = gaylords[0].lmd_provider_name === ENUM.lmdNames.NZPost;
      const customBrokerList = destinations.find(
        (item) => item.id === gaylords[0].destination_group,
      ).custom_brokers;
      const customBroker = destinations.find(
        (el) =>
          el.item_type === ENUM.destinationGroupType.CUSTOM_BROKER &&
          customBrokerList.includes(el.id) &&
          el.discharge_point === createTx.point_of_discharge.toUpperCase(),
      );

      const customBrokerName = customBroker.discharge_point;

      if (countryCode === 'MY') {
        const isEastWestConfig =
          (await SystemConfigurationsService.getConfigByName(
            ENUM.versionConfigurations.EAST_WEST_CONFIG,
          )) === ENUM.version.TRUE;

        if (isEastWestConfig) {
          const result = await RateServices.getRateZone(allParcels);
          const malayParcels = allParcels.map((parcel) => {
            const pxl = result.find((x) => x.id === parcel.id);

            return {
              ...parcel,
              lmdZone: pxl.name,
            };
          });
          const eastParcel = malayParcels.filter((p) => p.lmdZone.toLowerCase().includes('east'));
          const westParcel = malayParcels.filter((p) => p.lmdZone.toLowerCase().includes('west'));

          const eastTotalWeight = eastParcel.reduce(
            (total, { weight }) => total + Number.parseFloat(weight),
            0,
          );
          const westTotalWeight = westParcel.reduce(
            (total, { weight }) => total + Number.parseFloat(weight),
            0,
          );
          req.body = {
            ...req.body,
            noOfEast: eastParcel.length,
            noOfWest: westParcel.length,
            eastTotalWeight,
            westTotalWeight,
          };

          if (eastParcel.length > 0) {
            await ManifestItemService.generateAndSendmailCBpart2MY(
              mawb,
              gaylords,
              parcelClassificationData,
              allOpHubs,
              allConversionRates,
              customBrokerName,
              allMerchants,
              'EAST',
              customBroker,
              req,
              eastParcel,
            );
          }

          if (westParcel.length > 0) {
            await ManifestItemService.generateAndSendmailCBpart2MY(
              mawb,
              gaylords,
              parcelClassificationData,
              allOpHubs,
              allConversionRates,
              customBrokerName,
              allMerchants,
              'WEST',
              customBroker,
              req,
              westParcel,
            );
          }
        } else {
          const stastdPieces = mawbTx.find((tx) => tx.status === 'STASTD')?.pieces;
          const totalWeight = allParcels.reduce(
            (total, { weight }) => total + Number.parseFloat(weight),
            0,
          );
          req.body = {
            ...req.body,
            pieces: stastdPieces || 0,
            totalWeight,
          };
          await ManifestItemService.generateAndSendmailCBpart2MY(
            mawb,
            gaylords,
            parcelClassificationData,
            allOpHubs,
            allConversionRates,
            customBrokerName,
            allMerchants,
            '',
            customBroker,
            req,
            allParcels,
          );
        }

        await MawbService.patch({
          id: createTx.id,
          partitionKey: createTx._partitionKey,
          is_pre_alert_email_part_2_triggered: true,
        });
      } else {
        const { lmd, archive, local_sta, local_std, flight_no, mawbPdfData } =
          await ManifestItemService.generateCustomManifest(
            mawb,
            gaylords,
            parcelClassificationData,
            allOpHubs,
            true,
            allConversionRates,
            customBrokerName,
          );

        const aboveItems = allParcels.filter((p) =>
          parcelClassificationData.parcelsAbove.includes(p.id),
        );

        const emailAchrive4AuPost = [];

        // If LMD = LMG then no need proforma invoice
        if (lmd.lmd_provider_name !== ENUM.lmdNames.LMG) {
          const pdfData = await ManifestItemService.generatePdfProformaInvoice(
            aboveItems,
            allMerchants,
            allOpHubs,
          );
          let proforma_buffer: any = '';

          if (pdfData.success) {
            proforma_buffer = Buffer.from(JSON.parse(pdfData.buffer));
            archive.push({ name: 'proforma.pdf', buffer: proforma_buffer });

            if (isLMDAuPost) {
              emailAchrive4AuPost.push({ name: 'proforma.pdf', buffer: proforma_buffer });
            }
          }
        }

        if (mawbPdfData.success) {
          if (isNZPost) {
            const fileName = `${mawbNo}_awb.pdf`;
            archive.push({ name: fileName, buffer: mawbPdfData.mawb_buffer });
            await SftpService.sendNZPreAlertDocsViaSFTP(
              mawbPdfData.mawb_buffer,
              `./awb/${fileName}`,
            );
            await AzureBlobStorage.uploadFile(
              mawbPdfData.mawb_buffer,
              AzureBlobStorage.generateBlobNameWithTimestamp(`NZPost/${fileName}`),
              config.azureStorageContainer.MAWB,
            );
            logger.info({
              message: 'Successfully upload MAWB PDF file for NZPost through SFTP',
              'mawb number': mawbNo,
            });
          }

          if (isLMDAuPost) {
            emailAchrive4AuPost.push({ name: 'mawb.pdf', buffer: mawbPdfData.mawb_buffer });
          }

          archive.push({ name: 'mawb.pdf', buffer: mawbPdfData.mawb_buffer });
        }

        try {
          await AzureBlobStorage.uploadFile(
            await ArchiverService.zip(archive),
            AzureBlobStorage.generateBlobNameWithTimestamp(`${mawbNo}.zip`),
            config.azureStorageContainer.MAWB,
          );
        } catch (error) {
          logger.error({ message: `MAWB upload failed for ${mawbNo}`, error });
        }

        if (!customBroker) {
          throw new Error(`Custom broker not found for LMD ${lmd.lmd_provider_name || lmd.id}`);
        }

        let stastdPieces;

        if (countryCode === 'PH' || countryCode === 'TW') {
          stastdPieces = mawbTx.find((tx) => tx.status === 'STASTD')?.pieces;
        }

        const zipPassword = await CBService.createZipPassword(customBroker.custom_broker_code);

        const zipBuffer = isLMDAuPost
          ? await ArchiverService.zip(emailAchrive4AuPost, zipPassword)
          : await ArchiverService.zip(archive, zipPassword);

        if (!isBoxCJapan) {
          await EmailService.sendPart2Email(
            customBroker,
            createTx,
            flight_no,
            local_sta,
            local_std,
            req,
            allParcels,
            zipBuffer,
            '',
            gaylords,
            mawbPdfData.chargeableWeight,
            stastdPieces,
            destination,
            lmd,
          );

          await MawbService.patch({
            id: createTx.id,
            partitionKey: createTx._partitionKey,
            is_pre_alert_email_part_2_triggered: true,
          });
        }
      }

      logger.info({ message: 'end sending CB email part 2', mawbNo });

      await BatchJobService.completeBatchJob(job.id, job._partitionKey);
      res.json({
        requestSuccess: true,
      });
    } catch (error) {
      logger.error({ message: error.message, mawbNo, error });
      res.json({
        requestSuccess: false,
        message: error.message,
      });
    }
  }

  async ParcelDeliveryInvoiceSendMail(req, res) {
    const invoiceLogger = logger.extends({
      business: ENUM.FunctionName.INVOICE,
    });

    try {
      invoiceLogger.info({
        message: 'Start trigger',
        body: req.body,
      });

      const {
        isWebJob,
        merchantName,
        invoiceType,
        year,
        month,
        date = new Date().getDate(),
      } = req.body;

      const [bankDetails, taxCountryManagementInfo] = await Promise.all([
        InvoicingServices.getBankDetails(),
        InvoicingServices.getTaxCountryManagementInfo(),
      ]);

      if (!bankDetails?.length) {
        return res.status(500).json({
          success: false,
          message: `${ENUM.FunctionName.INVOICE} failed: Cannot get Bank Details from Blob Storage`,
        });
      }

      if (!taxCountryManagementInfo?.length) {
        return res.status(500).json({
          success: false,
          message: `${ENUM.FunctionName.INVOICE} failed: Cannot get Tax Country Management from Blob Storage`,
        });
      }

      // get Merchants to Invoice
      const merchantsForInvoice = await InvoicingServices.getMerchantsForInvoicing(
        merchantName,
        taxCountryManagementInfo,
      );

      if (process.env.IS_TRIGER_DT_INVOICE === 'true') {
        if (!invoiceType || invoiceType === config.invoice_type.dt) {
          invoiceLogger.info({ message: 'Start DT' });

          const timeToInvoice =
            date === 1
              ? new Date(Date.UTC(year, month - 1, date))
              : new Date(Date.UTC(year, month, 0));

          const merchantsForDTInvoice = merchantsForInvoice.filter(
            (merchant) => merchant.invoicing_info?.tax,
          );

          await DtV2InvoicingUtils.handleDTInvoicing(
            timeToInvoice,
            bankDetails,
            taxCountryManagementInfo,
            isWebJob,
            merchantsForDTInvoice,
          );

          invoiceLogger.info({ message: 'End DT' });
        }
      } else {
        invoiceLogger.info({
          message: 'DT INVOICE TRIGGER is OFF',
          info: `${date === 1 ? month : month + 1}/${year}`,
        });
      }

      const pdResults = [];

      if (!invoiceType || invoiceType === config.invoice_type.pd) {
        let merchants = merchantsForInvoice;

        if (isWebJob) {
          merchants =
            date === 1
              ? merchants.filter(
                  (item) =>
                    item.auto_PD_invoice !== false &&
                    (!item.invoice_run_date ||
                      item.invoice_run_date === CONSTANTS.RUN_DATE.FIRST_OF_MONTH),
                )
              : merchants.filter(
                  (item) =>
                    item.auto_PD_invoice !== false &&
                    item.invoice_run_date === CONSTANTS.RUN_DATE.END_OF_MONTH,
                );
        }

        await PdInvoiceService.prepareInvoiceMetadata();

        for (const merchant of merchants) {
          invoiceLogger.info({ message: 'Start PD', merchantName: merchant.merchant_name });
          const startDayCountInvoice =
            date === 1
              ? new Date(Date.UTC(year, month - 2, 0))
              : new Date(Date.UTC(year, month - 1, 0));
          pdResults.push(
            await EmailService.sendMailToMerchant(
              date,
              month,
              year,
              startDayCountInvoice,
              merchant,
              bankDetails,
              taxCountryManagementInfo,
            ),
          );
          logger.info({
            message: 'End PD',
            merchantName: merchant.merchant_name,
            lastPdResult: pdResults.at(-1),
          });
        }
      }

      // Log return results as sometime job take long time to run and client is timeout
      invoiceLogger.info({ message: 'End successfully' });

      return res.status(200).json({
        success: true,
        message: `${ENUM.FunctionName.INVOICE} run successfully`,
        pdResults,
      });
    } catch (error) {
      invoiceLogger.error({ message: 'End with exception', error });

      return res.status(500).json({
        success: false,
        message: `${ENUM.FunctionName.INVOICE} failed: ${error.stack}`,
      });
    }
  }

  async ParcelDeliveryInvoiceCheck(req, res) {
    const invoiceCheckLogger = logger.extends({
      business: ENUM.FunctionName.INVOICE_CHECK,
    });

    try {
      invoiceCheckLogger.info({ message: 'Start trigger', body: req.body });

      const { date = new Date().getDate(), month, year, merchantName } = req.body;
      let merchants = [];
      let startDayCheck;
      let endDayCheck;
      const listParcelsMissInvoice = [];

      if (merchantName) {
        invoiceCheckLogger.info({ message: 'Get merchant', merchantName });
        merchants = await MerchantService.getMerchantByName(
          AesUtils.CrtCounterEncrypt(merchantName),
          true,
        );
      } else {
        invoiceCheckLogger.info({ message: 'Get all merchants' });
        merchants = await ManifestItemService.getMerchantByTaxCountry();
      }

      if (date === 1) {
        merchants = merchants.filter(
          (merchant) =>
            !merchant.invoice_run_date ||
            merchant.invoice_run_date === CONSTANTS.RUN_DATE.FIRST_OF_MONTH,
        );
        startDayCheck = new Date(Date.UTC(year, month - 4, date));
        endDayCheck = new Date(Date.UTC(year, month - 1, date));
        invoiceCheckLogger.info({ message: 'Get list merchants run at start of month', merchants });
      } else {
        merchants = merchants.filter(
          (merchant) => merchant.invoice_run_date === CONSTANTS.RUN_DATE.END_OF_MONTH,
        );
        startDayCheck = new Date(Date.UTC(year, month - 3, 0));
        endDayCheck = new Date(Date.UTC(year, month - 1, date));
        invoiceCheckLogger.info({ message: 'Get list merchants run at end of month', merchants });
      }

      for (const merchant of merchants) {
        invoiceCheckLogger.info({
          message: 'Start checking invoice',
          merchantName: AesUtils.CrtCounterDecrypt(merchant.merchant_name),
        });

        const [listParcelMissPDInvoice, listParcelMissPDAndDTInvoice] = await Promise.all([
          ManifestItemService.getMissingPDReadyToInvoiceParcel(
            merchant,
            startDayCheck,
            endDayCheck,
          ),
          ManifestItemService.getMissingPDAndDTReadyToInvoiceParcel(
            merchant,
            startDayCheck,
            endDayCheck,
          ),
        ]);

        for (const parcel of listParcelMissPDInvoice) {
          listParcelsMissInvoice.push({
            id: parcel.id,
            merchantName: AesUtils.CrtCounterDecrypt(merchant.merchant_name),
            type: '1',
          });
        }

        for (const parcel of listParcelMissPDAndDTInvoice) {
          listParcelsMissInvoice.push({
            id: parcel.id,
            merchantName: AesUtils.CrtCounterDecrypt(merchant.merchant_name),
            type: '2',
          });
        }
      }

      await EmailService.sendMailCheckInvoiceToMerchant(listParcelsMissInvoice);
      res.status(200).json({
        success: true,
        message: 'Send email check invoice successfully',
      });
    } catch (error) {
      invoiceCheckLogger.error({ message: 'End with exception', error });
      res.status(500).json(error);
    }
  }

  async sendInvoiceReportForCheckAndBalanceEmail(req, res) {
    try {
      logger.info({ message: 'Start sending invoice report for check and balance email' });
      const { date, month, year } = req.body;

      if (!date || !month || !year) {
        return res.status(400).json({
          success: false,
          message: 'Missing date, month or year',
        });
      }

      const merchants = await MerchantService.getMerchantForCheckAndBalanceEmail(date);

      if (!merchants?.length) {
        return res.status(500).json({
          success: false,
          message: 'No merchant found',
        });
      }

      const tableDatas = [];
      const errorParcels = [];

      for (const merchant of merchants) {
        const dataPerMerchant = {
          merchant_name: merchant.merchant_name,
          noOfParcelsForPD: 0,
          noOfParcelsForDT: 0,
        };
        let start = new Date(year, month - 2);
        let end = new Date(year, month - 1);

        if (date !== 1) {
          start = new Date(year, month - 1, 0);
          end = new Date(year, month, 0);
        }

        const [pdParcels, dtParcels] = await Promise.all([
          ManifestItemService.getPDReadyToInvoiceParcels(merchant.merchant_name, start, end),
          ManifestItemService.getDutiesAndTaxesParcels(end, start, merchant),
        ]);

        for (const parcel of pdParcels) {
          if (!+parcel.weight) {
            errorParcels.push({ id: parcel.id, message: 'Weight Error' });
          } else if (!+parcel.length || !+parcel.width || !+parcel.height) {
            errorParcels.push({ id: parcel.id, message: 'Dimension Error' });
          } else {
            ++dataPerMerchant.noOfParcelsForPD;
          }
        }

        for (const parcel of dtParcels) {
          if (!+parcel.weight) {
            errorParcels.push({ id: parcel.id, message: 'Weight Error' });
          } else if (!+parcel.length || !+parcel.width || !+parcel.height) {
            errorParcels.push({ id: parcel.id, message: 'Dimension Error' });
          } else {
            ++dataPerMerchant.noOfParcelsForDT;
          }
        }

        tableDatas.push(dataPerMerchant);
      }

      const errorListCsv = EmailService.getErrorListCsv(errorParcels);
      const mailOptions = EmailService.getInvoiceReportContent(tableDatas, req.body, errorListCsv);
      await EmailUtils.sendEmailSendgrid(mailOptions, {
        fromFunction: 'sendInvoiceReportForCheckAndBalanceEmail',
      });

      logger.info({ message: 'End sending invoice report for check and balance email' });
      res.status(200).json({
        success: true,
        message: 'Sending Email successfully',
      });
    } catch (error) {
      logger.error({
        message: 'Error while sending invoice report for check and balance email',
        error: error.message || error,
      });
      res.status(500).json({
        success: false,
        message: error.message || error,
      });
    }
  }
}
