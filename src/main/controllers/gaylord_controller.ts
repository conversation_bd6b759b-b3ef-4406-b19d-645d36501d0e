import fs from 'fs';

import axios from 'axios';
import pick from 'lodash/pick.js';
import uniq from 'lodash/uniq.js';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import { ENUM } from '~/models/enum.js';
import { NzPostServices } from '~/models/lmd_integration/services/nzPost_services.js';

import { AustraliaPostService } from '~/services/australia-post.service.js';
import { BatchJobService } from '~/services/batchJob-service.js';
import { CommercialInvoiceService } from '~/services/commercial-invoice-doc-service.js';
import { DestinationService } from '~/services/destination-services.js';
import { DutyAndTaxService } from '~/services/duty_and_tax_service.js';
import { GaylordServices } from '~/services/gaylord.service.js';
import { LMGService } from '~/services/lmg.service.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';
import { MawbService } from '~/services/mawb-service.js';
import { MerchantWebhookApi } from '~/services/merchant_webhook_api.js';
import { ShipmentService } from '~/services/shipments/shipmentService.js';

import { BaseDaoUtils } from '~/utilities/baseDao.utils.js';
import { CsvUtils } from '~/utilities/csvUtils.js';
import { FileUtils } from '~/utilities/fileUtils.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { SftpUtils } from '~/utilities/sftpUtils.js';

import { config } from '~/configs/index.js';
import { CONSTANTS } from '~/constants/index.js';
import { Daos } from '~/daos/index.js';
import { IMawbCreated } from '~/types/mawb.type.js';

const logger = new AppLogger({ name: 'GaylordController' });

export default class GaylordController {
  /**
   * add multiple parcels to a gaylord
   */
  async scanParcels(req: any, res: any) {
    try {
      const isFromLSPPortal = req.headers['pls-name'] === ENUM.PlsAppName.LspService;
      const isFromNewLSPPortal =
        req.headers[ENUM.PlsRequestHeaders.CLIENT] === ENUM.PlsAppName.LSP_PORTAL;

      const { files, fields } = req.formData;

      const [csvData, gaylord] = await Promise.all([
        fs.promises.readFile(files.file[0].filepath),
        GaylordServices.getGaylordById(req.params.gaylordId),
      ]);

      const parsedCsvData = await CsvUtils.parseString(csvData.toString());

      logger.info({ isFromLSPPortal, fields });

      const inputTrackingIds = uniq(
        parsedCsvData.map((item) => item.shipment_tracking_id.toUpperCase()).filter(Boolean),
      );
      const parcels = await ShipmentService.findShipmentsByTrackingIds(inputTrackingIds);

      if (!parcels?.length) {
        return res.json({
          success: false,
          message: 'Bulk Upload Booking File empty',
        });
      }

      const isMawb = gaylord.mawb_no.startsWith(CONSTANTS.MAWB_PREFIX);

      const parcelsMap = new Map(
        parcels.flatMap((parcel) => {
          if (parcel.domestic_tracking_id) {
            return [
              [parcel.domestic_tracking_id, { parcel, isKeyDomestic: true }],
              [parcel.tracking_id, { parcel, isKeyDomestic: false }],
            ];
          }

          return [[parcel.tracking_id, { parcel, isKeyDomestic: false }]];
        }),
      );

      const errorTrackingIds = [];
      const newTrackingIdFromDomesticInputOrRebook = [];
      const successTrackingIds = [];
      const newSuccessTrackingIds = [];

      for (const inputTrackingId of inputTrackingIds) {
        const data = parcelsMap.get(inputTrackingId);

        // Disallow shipments which are not found
        if (!data) {
          errorTrackingIds.push({
            trackingId: inputTrackingId,
            message: 'Parcel not found',
            lmdRejectReason: '',
          });
          continue;
        }

        // Disallow Postal shipments for bulk upload to MAWB
        if (isMawb && data?.parcel?.service_option === ENUM.parcelOption.postal) {
          errorTrackingIds.push({
            trackingId: inputTrackingId,
            message: 'Postal shipment is disallowed for bulk upload to MAWB',
            lmdRejectReason: '',
          });
          continue;
        }

        // Disallow shipments which are being rebooked
        if (data?.parcel?.is_rebooking) {
          errorTrackingIds.push({
            trackingId: inputTrackingId,
            message:
              'This parcel is being re-booked by Parxl and cannot be process for now.' +
              ' Please speak to Parxl Operation team for assistance if required',
            lmdRejectReason: '',
          });
          continue;
        }

        const result = await GaylordServices.reBookAndValidateParcel(
          data.parcel,
          gaylord,
          isFromLSPPortal,
          isFromNewLSPPortal,
        );

        if (result.success) {
          if (data.parcel.isRebook || data.isKeyDomestic) {
            newTrackingIdFromDomesticInputOrRebook.push(data.parcel.tracking_id);
          }

          successTrackingIds.push(inputTrackingId);
          newSuccessTrackingIds.push(data.parcel.tracking_id);
        } else {
          errorTrackingIds.push({
            trackingId: inputTrackingId,
            message: result.message,
            lmdRejectReason: data.parcel.error_booking_msg || '',
          });
        }
      }

      logger.info({ newTrackingIdFromDomesticInputOrRebook, errorTrackingIds, successTrackingIds });

      // update parcels
      await GaylordServices.updateParcelsAfterAddInGaylord(
        gaylord,
        successTrackingIds.map((trackingId) => parcelsMap.get(trackingId)),
        new Date(),
        fields,
      );

      // update gaylord
      const objUpdateGaylord: any = {
        id: gaylord.id,
        parcels: successTrackingIds.map((trackingId) => {
          const { parcel } = parcelsMap.get(trackingId);

          return parcel.id;
        }),
        destination_group: gaylord.destination_group,
        lmd_provider_name: gaylord.lmd_provider_name,
      };

      if (gaylord.latest_tracking_status !== ENUM.gaylordStatus.incompleted) {
        objUpdateGaylord.tracking_status = [
          {
            status: ENUM.gaylordStatus.incompleted,
            date: new Date(),
          },
        ];
        objUpdateGaylord.latest_tracking_status = ENUM.gaylordStatus.incompleted;
      }

      if (!gaylord.operation_hub) {
        objUpdateGaylord.operation_hub = fields.operationsHub;
      }

      await Daos.gaylord.updateItemResolveConflict(objUpdateGaylord);

      // update mawb
      const pointOfDischarge = await DestinationService.getPOD(gaylord.destination_group);
      const mawbTxs = await MawbService.getMawb(gaylord.mawb_no);
      const mawbCreatedTx = MawbService.getCreatedTx(mawbTxs);
      await Daos.mawb.patch({
        id: mawbCreatedTx.id,
        partitionKey: mawbCreatedTx._partitionKey,
        point_of_discharge: pointOfDischarge,
      });
      logger.info({
        message: `Point of discharge of MAWB ${gaylord.mawb_no} has been updated to ${pointOfDischarge}`,
      });

      res.json({
        success: true,
        newTrackingIds: newTrackingIdFromDomesticInputOrRebook,
        receivedParcels:
          newSuccessTrackingIds.length - newTrackingIdFromDomesticInputOrRebook.length,
        totalParcels: inputTrackingIds.length,
        trackingIdsSuccess: newSuccessTrackingIds,
        errorShipments: errorTrackingIds,
        destinationGroup: gaylord.destination_group || '',
        lmd_provider_name: gaylord.lmd_provider_name || '',
      });
    } catch (error) {
      logger.error({ error });
      res.json({
        success: false,
        data: error.message,
      });
    }
  }

  async downloadBulkBookingUploadTemplate(_req: any, res: any) {
    try {
      const TEMPLATE_FILE_NAME = 'Bulk_Booking_Upload_Template.csv';
      const fileStream = await AzureBlobStorage.downloadFile(
        TEMPLATE_FILE_NAME,
        config.azureStorageContainer.TEMPLATE,
      );
      res.statusCode = 200;
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', `attachment; filename="${TEMPLATE_FILE_NAME}"`);
      const buffer = await FileUtils.streamToBuffer(fileStream);
      res.end(buffer);
    } catch (error) {
      logger.error({ error });

      res.status(500).json({
        isSuccess: false,
        message: error,
      });
    }
  }

  async PushToCcnSftp(mawb: any) {
    if (
      !(await SftpUtils.putFile(
        {
          host: config.ccn.sftp_host,
          port: Number(config.ccn.sftp_port),
          username: config.ccn.username,
          password: config.ccn.password,
        },
        Buffer.from(mawb),
        `/PLS/${mawb}.txt`,
      ))
    ) {
      logger.error({ message: 'error when upload file to sftp', mawb });
    }
  }

  async updateParcelInGaylord(parcels: any[]) {
    try {
      const ts = new Date();
      await DutyAndTaxService.injectDutyAndTaxAmountForParcels(parcels);
      const updateParcelsPayload = parcels.map((parcel) => {
        return {
          id: parcel.id,
          sales_tax_currency: parcel.sales_tax_currency,
          duty_tax_currency: parcel.duty_tax_currency,
          item___overwrite: parcel.item,
          [BaseDaoUtils.getArrayKey(parcel, 'DT_invoicing_status')]: [
            {
              status: ENUM.invoiceStatus.DT_ready_to_invoice,
              timestamp: ts,
            },
          ],
        };
      });
      const { successItems, errorItems } = await Daos.manifest_items.bulkUpdate(
        updateParcelsPayload,
        20,
        1000,
      );

      for (const errorItem of errorItems)
        logger.error({
          message: 'exception when update parcel',
          parcelId: errorItem.item?.id,
          error: errorItem.error,
        });

      for (const successItem of successItems) {
        const parcel = successItem.item;
        const lastStatus = parcel.tracking_status?.slice(-1)[0];
        await MerchantWebhookApi.updateStatus(
          parcel,
          parcel.id,
          ENUM.parcelStatus.packed_to_gaylord,
          lastStatus?.date || lastStatus?.timestamp || new Date(),
        );
      }
    } catch (error) {
      logger.error({ message: 'exception when update parcel', error });
    }
  }

  /**
   * Call finance-api to create overpack and get label
   */
  async createOverpack(req: any, res: any) {
    const url = `${process.env.FINANCE_URL}/boxc/overpacks`;
    const gaylord = req.body;
    const gaylordNo = gaylord.gaylord_no;
    const overpackId = gaylord.overpack_id;

    try {
      const foundManifestItems = await GaylordServices.getManifestItemsByGaylordNo(gaylordNo);
      const overpackShipments = foundManifestItems.map((item) => ({
        tracking_number: item.tracking_id,
      }));

      const data = {
        gaylord_no: gaylordNo,
        overpack_id: overpackId,
        overpackData: {
          overpack: {
            weight: gaylord.weight,
            height: gaylord.height,
            width: gaylord.width,
            length: gaylord.length,
            shipments: overpackShipments,
          },
        },
      };

      const response = await axios.post(url, data);
      const {
        message,
        message: { shipments },
        code,
        success,
      } = response.data;

      if (!success) {
        // Check if there is any shipment level error
        if (shipments && shipments.length > 0) {
          const errorShipments = shipments.filter((s: any) => !!s.error);

          if (errorShipments && errorShipments.length > 0) {
            return res.json({
              success: false,
              errorType: 'shipmentError',
              message: errorShipments,
            });
          }
        } else {
          return res.json({
            success: false,
            errorType: 'overpackError',
            message,
            code,
          });
        }
      }

      return res.json({
        success: true,
        message,
      });
    } catch (error) {
      logger.error({ error: error.message ?? error });

      return res.json({
        success: false,
        errorType: 'unknownError',
        message: error.message,
      });
    }
  }

  /**
   * Get overpack label from AzureBlob
   */
  async getLabelOverpack(req: any, res: any) {
    const fileName = req.params.id;

    try {
      const isExist = await AzureBlobStorage.isFileExist(
        config.azureStorageContainer.OVERPACK_LABEL,
        `${fileName}.pdf`,
      );

      if (!isExist) {
        throw new Error(`Cannot find overpack label with file name ${fileName}.pdf`);
      }

      const fileStream = await AzureBlobStorage.downloadFile(
        `${fileName}.pdf`,
        config.azureStorageContainer.OVERPACK_LABEL,
      );
      res.statusCode = 200;
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="' + `${fileName}.pdf` + '"');
      fileStream.pipe(res);
    } catch (error) {
      logger.error({ message: 'Cannot get overpack label', error });
      res.status(500).json({
        message: 'Something wrong cant get label',
      });
    }
  }

  async finishMawb(req: any, res: any) {
    const postObj = req.body;
    const awb = postObj.mawb;
    const user = res.locals?.decoded?.user_info || {};
    postObj.gaylords = Array.from(new Set(postObj.gaylords));

    try {
      if (!(postObj && Array.isArray(postObj.gaylords) && postObj.gaylords.length > 0)) {
        throw new Error('There is no Container in Airway Bill');
      }

      // ----- Get and Update Gaylords by ids -----
      const gaylords = await GaylordServices.getGaylordsByIds(postObj.gaylords);

      if (gaylords.some((it) => it.latest_tracking_status !== ENUM.gaylordStatus.readyToClosed)) {
        throw new Error('Unable to close MAWB as not all containers are ready to close');
      }

      const result = [];

      for (const glId of postObj.gaylords) {
        result.push(await GaylordServices.updateGLandClose(glId, awb));
      }

      // ----- Update Parcels in Gaylords -----
      const fields = [
        'id',
        'merchant_account_number',
        'merchant_name',
        'merchant_declared_currency',
        'tracking_id',
        'errorReason',
        'country',
        'lmd',
        'item',
        'DT_invoicing_status',
        'recipient_first_name',
        'recipient_last_name',
        'email',
        'shipment_notification',
        'is_ready_for_shipment_email_sent',
        'origin_country',
        'service_option',
        'incoterm',
        'landmark_shipment_group_id',
        'operation_hub',
        'weight',
        'weight_unit',
        'merchant_order_no',
      ];

      const parcels = await ManifestItemService.getManifestItemsFromMultipleGaylords(
        postObj.gaylords,
        fields,
      );

      if (!req.body.origin_country) {
        postObj.origin_country = parcels[0].origin_country;
      }

      // Send packed_to_gaylord status to merchant webhook
      const parcelsNotInvoiced = parcels.filter(
        ({ DT_invoicing_status }) =>
          !DT_invoicing_status?.some((it: any) =>
            [
              ENUM.invoiceStatus.DT_ready_to_invoice,
              ENUM.invoiceStatus.DT_invoiced,
              ENUM.invoiceStatus.DT_invoicing_error,
            ].includes(it.status),
          ),
      );

      if (parcelsNotInvoiced.length > 0) {
        this.updateParcelInGaylord(parcelsNotInvoiced);
      }

      // ----- Update MAWB & Gaylords -----
      // GET all Mawb Items
      const mawbTxs = await MawbService.getMawb(awb);

      if (!mawbTxs || mawbTxs.length === 0) {
        throw new Error('Can not find mawb');
      }

      const isFinished = mawbTxs.some((it: any) => it.status === ENUM.mawbStatus.fin);

      if (isFinished) {
        throw new Error('Airway Bill is already Closed');
      }

      const { destination, lmd_provider_name: lmd } = gaylords[0];

      // Get item tranx_type "created" & all GL id in MAWB
      const mawbCreatedTx: IMawbCreated = MawbService.getCreatedTx(mawbTxs);
      const customBrokerID = mawbCreatedTx?.custom_broker;
      const gaylordsNo = new Set(MawbService.getGaylords(mawbTxs).map((x) => x.gaylord_no));

      if (lmd.includes(ENUM.lmdNames.LMG)) {
        await LMGService.createShipmentGroup(parcels, mawbCreatedTx, awb);
      }

      // 1. Re-add gaylord if mawb don't have gaylord no
      const addMawbTranxPromises = [];

      for (const gaylord_no of postObj.gaylords) {
        if (!gaylordsNo.has(gaylord_no)) {
          const gaylordTx = {
            tranx_type: 'add-gaylord',
            gaylord_no,
            _partitionKey: awb,
            ref_id: mawbCreatedTx.id,
          };
          addMawbTranxPromises.push(
            Daos.mawb.addItem(gaylordTx).catch((error) => {
              logger.error({ caller: 'finishMawb', message: 'add-gaylord got exception', error });
            }),
          );
        }
      }

      if (addMawbTranxPromises.length > 0) await Promise.all(addMawbTranxPromises);

      // 2. Add tranx_type "finish" for MAWB
      // check if the finish tranx exist
      const findTranxFinish = mawbTxs.filter((txs: any) => txs.tranx_type === 'finish');

      if (Array.isArray(findTranxFinish) && findTranxFinish.length === 0) {
        const finishGaylordTx = {
          gaylords: postObj.gaylords,
          status: ENUM.mawbStatus.fin,
          finish_date: new Date(),
          console_manifest_creation_date: new Date(),
          date: new Date(),
          _partitionKey: awb,
          ref_id: mawbCreatedTx.id,
          tranx_type: 'finish',
        };
        await Daos.mawb.addItem(finishGaylordTx);
      }

      // 3. Update status is FINISH in tranx_type "created"
      let updateMawb: any = {
        id: mawbCreatedTx.id,
        latest_tracking_status: ENUM.mawbStatus.fin,
      };

      // Assign flight info
      updateMawb = Object.assign(updateMawb, postObj.pointOfDischarges);

      // get point of discharge incase pod in payload is falsy
      if (!postObj.pointOfDischarges.point_of_discharge) {
        const pointOfDischarge = await DestinationService.getPOD(mawbCreatedTx.destination_group);
        const consignee = customBrokerID
          ? await DestinationService.getConsigneeByCBId(customBrokerID)
          : await DestinationService.getConsignee(
              mawbCreatedTx.destination_group,
              pointOfDischarge,
            );
        updateMawb.point_of_discharge = pointOfDischarge;
        updateMawb.consignee = consignee;

        postObj.pointOfDischarges.point_of_discharge = updateMawb.point_of_discharge;
      }

      try {
        await Daos.mawb.updateItemResolveConflict(updateMawb, awb);
      } catch (error) {
        logger.error({ message: 'could not update mawb', updateMawb, error });
      }

      // 4. IF LMD is BoxC => add tranx_type "boxc-triggered"
      const isLmdBoxC = MawbService.isLmdBoxC(lmd);

      if (isLmdBoxC) {
        const boxCParams = {
          lmdName: lmd,
          mawbNo: awb,
          mawbId: mawbCreatedTx.id,
          gaylordNos: postObj.gaylords,
          gaylords,
        };
        await MawbService.updateBoxCMawb(boxCParams);
      } else {
        // ----- Trigger Email Part 1 -----
        // While confing MANUAL_CREATE MAWB is OFF & LMD was not BoxC
        if (parcels[0].service_option.toLowerCase() !== ENUM.parcelOption.postal) {
          GaylordServices.triggerEmailPart1(awb);
        }
      }

      // ----- Trigger AusPost to create Order -----
      if (destination === ENUM.auPost.destination || lmd === ENUM.lmdNames.AusPost) {
        logger.info({
          message: `Start trigger AusPost to create order ${awb}`,
          lmd: ENUM.lmdNames.AusPost,
        });

        AustraliaPostService.createOrder(awb).catch((error) => {
          logger.error({ caller: 'finishMawb', message: 'Order AU Post failed', awb, error });
        });
      }

      // ----- Push MAWB to CCN via SFTP -----
      this.PushToCcnSftp(awb);

      // ----- Create Jobs -----
      // 1. Call API to create Job inform custom DB every hours
      const cbJob = await BatchJobService.getInformCBJob(awb);

      if (!cbJob && !isLmdBoxC) {
        GaylordServices.createCbJob(awb, mawbCreatedTx.id, postObj.pointOfDischarges);
      }

      // 2. Create batch job listening to LMDs to update parcel tracking status
      const LMDNameArray = [ENUM.lmdNames.AusPost, ENUM.lmdNames.Janio, ENUM.lmdNames.Evri];

      if (LMDNameArray.includes(lmd)) BatchJobService.createLMDStatusBatchJob(awb, parcels);

      // 3. Create batch job listening to CB status
      if (lmd === ENUM.lmdNames.CouriersPlease)
        BatchJobService.createGetCBStatusBatchJobForAMS(awb, parcels);

      const parcelsForShipmentEmail = parcels.map((parcel) =>
        pick(parcel, [
          'id',
          'email',
          'recipient_first_name',
          'merchant_name',
          'is_ready_for_shipment_email',
          'shipment_notification',
          'lmd',
        ]),
      );

      // if origin_country is MY, gen MY cosole manifest and upload to blob
      if (postObj.origin_country === 'MY') {
        const fileName = `${awb}_console_manifest.zip`;
        const filePath = `${awb}/${fileName}`;
        await MawbService.createConsoleManifestAndUpload(awb, filePath);
      }

      // ----- Generate Commercial Invoice -----
      CommercialInvoiceService.generateCommercialInvoice(postObj).catch((error) => {
        logger.error({ message: 'Generate Commercial Invoice failed', error });
      });
      // ----- Generate OD Permit Declaration Info -----
      MawbService.generateOdPermitFile(awb, parcels).catch((error) => {
        logger.error({ message: 'Generate OD Permit Declaration Info failed', error });
      });
      // ----- Send Ready for Shipment Email to End Customer -----
      axios
        .post(`${config.finApi}/notification/shipment-email`, parcelsForShipmentEmail)
        .catch((error) => {
          logger.error({ message: 'Send Ready for Shipment Email failed', error });
        });

      logger.info({
        message: 'Close MAWB Succeeded',
        mawb_no: awb,
        gaylord_no_list: postObj.gaylords.join(', '),
        action_by_user_name: user.name,
        action_by_user_email: user.email,
        platform: res.locals?.platform === ENUM.PlsAppName.LspService ? 'LSP Portal' : 'PLS Admin',
      });

      if (lmd === ENUM.lmdNames.NZPost) {
        NzPostServices.triggerUpdateNzStatus(awb);
      }

      return res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      const message = error.message || error;
      logger.error({ awb, error });

      return res.json({
        success: false,
        data: message,
      });
    }
  }
}
