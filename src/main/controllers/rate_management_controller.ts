import fs from 'fs';

import { Jwt, JwtPayload } from 'jsonwebtoken';
import streamToBuffer from 'stream-to-buffer';
import XLSX from 'xlsx';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { EmailService } from '~/services/email.services.js';
import { HawbService } from '~/services/hawb.service.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';
import { RateManagementService } from '~/services/rate-management.service.js';
import { RateServices } from '~/services/rate-services.js';

import { DateUtils } from '~/utilities/dateUtils.js';
import { FileUtils } from '~/utilities/fileUtils.js';
import { JWT } from '~/utilities/jwt.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { getCellValue } from '~/utilities/rate-sheet.util.js';
import { RateApprovalUtils } from '~/utilities/rate_approval_utils.js';

import { config } from '~/configs/index.js';
import { Daos } from '~/daos/index.js';
import {
  IMerchantRateV2,
  IRateSheetValidationResult,
  TRateSheetServiceOption,
} from '~/types/rate.type.js';

const logger = new AppLogger({ name: 'RateManagementController' });
const webjobLogger = logger.extends({
  webJob: ENUM.WebjobName.NOTIFY_RATE_SHEET_EXPIRY,
});

export default class RateManagementController {
  async notifyOfExpiryRateSheet(_req: any, res: any) {
    try {
      const querySpec = {
        query: `
        SELECT c.validity_to, c.validity_from, c.merchant_account_number
        FROM rateMerchant c WHERE c.type="merchant_rate"
        AND c.notify_of_expiry = true
        AND c.isActive = true
        AND c.status != 'Withdrawn'
        `,
      };
      const rates = await Daos.rateMerchant.find(querySpec);

      const merchantRateMapMonth = new Map();
      const merchantRateMapWeek = new Map();

      for (const rate of rates) {
        const dayBetweenNowAndRateExpire = DateUtils.getDayDifference(
          new Date(),
          new Date(rate.validity_to),
        );

        if (dayBetweenNowAndRateExpire !== 30 && dayBetweenNowAndRateExpire !== 7) continue;

        let merchantRateMap: any;

        if (dayBetweenNowAndRateExpire === 30) {
          merchantRateMap = merchantRateMapMonth;
          rate.typeOfRate = ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.MONTH;
        } else {
          merchantRateMap = merchantRateMapWeek;
          rate.typeOfRate = ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.WEEK;
        }

        if (merchantRateMap.has(rate.merchant_account_number)) {
          merchantRateMap.get(rate.merchant_account_number).rates.push(rate);
        } else {
          merchantRateMap.set(rate.merchant_account_number, {
            rates: [rate],
          });
        }
      }

      if (merchantRateMapMonth.size === 0 && merchantRateMapWeek.size === 0) {
        webjobLogger.info({
          message: 'No rate validity going to be expire in month time and week time',
        });

        return res.status(200).json({
          success: true,
          message: 'No rate validity going to be expire in month time and week time',
        });
      }

      const merchantQuery = {
        query:
          'SELECT c.merchant_name, c.merchant_account_number FROM merchant c WHERE ARRAY_CONTAINS(@merchantAccNoList, c.merchant_account_number)',
        parameters: [
          {
            name: '@merchantAccNoList',
            value: [...merchantRateMapMonth.keys(), ...merchantRateMapWeek.keys()],
          },
        ],
      };

      const merchants = await Daos.merchant.find(merchantQuery);

      for (const merchant of merchants) {
        merchant.merchant_name = AesUtils.CrtCounterDecrypt(merchant.merchant_name || '');

        if (merchantRateMapMonth.has(merchant.merchant_account_number)) {
          merchantRateMapMonth.get(merchant.merchant_account_number).merchant = merchant || {};
        }

        if (merchantRateMapWeek.has(merchant.merchant_account_number)) {
          merchantRateMapWeek.get(merchant.merchant_account_number).merchant = merchant || {};
        }
      }

      const sended = [];
      const errors = [];

      if (merchantRateMapMonth.size > 0) {
        await EmailService.sendRateSheetExpiryNotifyEmail(
          ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.MONTH,
          merchantRateMapMonth,
        )
          .then((response) => {
            if (response.success === false) {
              errors.push({
                type: ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.MONTH,
                error: response.message,
              });
            } else {
              sended.push('month');
              webjobLogger.info({
                caller: 'notifyOfExpiryRateSheet',
                message: 'Rate sheet expire in month time notify sended',
              });
            }
          })
          .catch((error) => {
            webjobLogger.error({
              message:
                'Ratesheet Expiry Notification error failed to send validity expire month time expire',
              error,
            });
            errors.push({
              type: ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.MONTH,
              error,
            });
          });
      }

      if (merchantRateMapWeek.size > 0) {
        await EmailService.sendRateSheetExpiryNotifyEmail(
          ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.WEEK,
          merchantRateMapWeek,
        )
          .then((response) => {
            if (response.success === false) {
              errors.push({
                type: ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.WEEK,
                error: response.message,
              });
            } else {
              sended.push('week');
              webjobLogger.info({
                caller: 'notifyOfExpiryRateSheet',
                message: 'Rate sheet expire in week time notify sended',
              });
            }
          })
          .catch((error) => {
            webjobLogger.error({
              message: 'failed to send validity expire week time expire',
              error,
            });
            errors.push({
              type: ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.WEEK,
              error,
            });
          });
      }

      if (errors.length > 0) {
        return res.status(500).json({
          success: false,
          message: `Web job ${ENUM.WebjobName.NOTIFY_RATE_SHEET_EXPIRY} failed: ${JSON.stringify(errors)}`,
        });
      }

      webjobLogger.info({ message: 'Ratesheet Expiry Notification finished' });
      res.status(200).json({
        success: true,
        message: `Sended notify of rate sheet going to be expired in ${sended.join(' and ')} time`,
      });
    } catch (error) {
      webjobLogger.error({
        message: 'Something went wrong while running notifyOfExpiryRateSheet',
        error,
      });
      res.status(500).json({
        success: false,
        message: `Web job ${ENUM.WebjobName.NOTIFY_RATE_SHEET_EXPIRY} failed: ${error.stack || error}`,
      });
    }
  }

  async getAllRateMerchant(_req: any, res: any) {
    try {
      const querySpec = {
        query: "SELECT * FROM rateMerchant d WHERE d.type = 'merchant_rate'",
      };
      const merchantRates = await Daos.rateMerchant.find(querySpec);

      if (merchantRates.length === 0) {
        return res.json({
          success: false,
          message: 'There is currently no merchant rate',
        });
      }

      for (const rate of merchantRates) {
        if (rate) {
          rate.merchant_account_number = AesUtils.CrtCounterDecrypt(rate.merchant_account_number);
        }
      }

      return res.json({
        success: true,
        data: merchantRates,
      });
    } catch (error) {
      logger.error({ message: 'Unable to get rate merchants', error });

      return res.json({
        success: false,
        message: error.message,
      });
    }
  }

  async getAllMerchant(_req: any, res: any) {
    try {
      const merchants = await Daos.merchant.find(
        'SELECT d.merchant_account_number, d.merchant_name, d.id FROM merchant d',
      );
      const merchantMap = new Map(
        merchants.map((merchant) => [
          merchant.merchant_account_number,
          {
            id: merchant.id,
            merchant_account_number: AesUtils.CrtCounterDecrypt(merchant.merchant_account_number),
            merchant_name: AesUtils.CrtCounterDecrypt(merchant.merchant_name),
          },
        ]),
      );

      const rateMerchantQuerySpec = {
        query: `SELECT * FROM d 
                  WHERE d.type = 'merchant_rate'
                  AND d.isActive = true 
                  AND d.status != 'Withdrawn'
                  AND ARRAY_CONTAINS(@merchant_account_number_list, d._partitionKey)
        `,
        parameters: [
          {
            name: '@merchant_account_number_list',
            value: merchants.map((merchant) => merchant.merchant_account_number),
          },
        ],
      };
      const merchantRates = await Daos.rateMerchant.find(rateMerchantQuerySpec);

      for (const rate of merchantRates) {
        const {
          merchant_account_number: merchantAccNo,
          validity_from: validityFrom,
          validity_to: validityTo,
          approver_name: approverName,
          submiter_name: submiterName,
        } = rate;

        const merchant: any = merchantMap.get(merchantAccNo) || {};

        if (merchant && !merchant.validity) {
          merchant.validity = [];
        }

        merchant.validity.push({
          ...rate,
          from: validityFrom,
          to: validityTo,
          approver_name: AesUtils.CrtCounterDecrypt(approverName),
          submiter_name: AesUtils.CrtCounterDecrypt(submiterName),
        });
      }

      return res.json({
        data: [...merchantMap.values()],
      });
    } catch (error) {
      logger.error({ message: 'Error while get rateMerchant info', error });

      return res.json({
        success: false,
        message: error,
      });
    }
  }

  async toggleNotifyOfValidityExpiry(req: any, res: any) {
    const { validityId } = req.params;
    const { notifyOfExpiry, merchantAccNo } = req.body;

    try {
      await Daos.rateMerchant.updateItemResolveConflict(
        {
          id: validityId,
          notify_of_expiry: notifyOfExpiry,
        },
        AesUtils.CrtCounterEncrypt(merchantAccNo),
      );
      res.sendStatus(204);
    } catch (error) {
      logger.error({ message: 'Error while toggle notify of validity expiry', error });
      res.sendStatus(500);
    }
  }

  async createMerchant(req: any, res: any) {
    AesUtils.encryptMerchant(req.body);

    try {
      const data = await Daos.merchant.addItem(req.body);
      res.json({
        data,
        success: true,
        message: 'Create successfully.',
      });
    } catch (error) {
      res.json({
        data: error,
        success: false,
        message: 'Error--',
      });
    }
  }

  async getRateMerchantById(req: any, res: any) {
    try {
      const { id } = req.params;
      const querySpec = {
        query: 'SELECT * FROM rateMechant d Where d.id = @id',
        parameters: [
          {
            name: '@id',
            value: id,
          },
        ],
      };
      const rateMerchants = await Daos.rateMerchant.find(querySpec);

      if (rateMerchants.length === 0) {
        return res.json({
          success: false,
          message: 'There is currently no rate merchants',
        });
      }

      const querySpec1 = {
        query: 'SELECT * FROM mechant d Where d.merchant_account_number = @merchant_account_number',
        parameters: [
          {
            name: '@merchant_account_number',
            value: rateMerchants[0].merchant_account_number,
          },
        ],
      };

      const merchants = await Daos.merchant.find(querySpec1);

      if (merchants.length === 0) {
        return res.json({
          success: false,
          message: `Merchant account ${rateMerchants[0].merchant_account_number} not found!`,
        });
      }

      rateMerchants[0].merchantName = AesUtils.CrtCounterDecrypt(merchants[0].merchant_name);
      rateMerchants[0].merchant_account_number = AesUtils.CrtCounterDecrypt(
        merchants[0].merchant_account_number,
      );
      rateMerchants[0].merchant_name = AesUtils.CrtCounterDecrypt(merchants[0].merchant_name);

      return res.json({
        success: true,
        data: rateMerchants,
      });
    } catch (error) {
      logger.error({ message: 'Unable to get rate merchant by id', error });

      return res.json({
        success: false,
        message: error,
      });
    }
  }

  async getRateManagementbyIdRateMerchant(req: any, res: any) {
    try {
      const { id } = req.params;
      const querySpec = {
        query:
          "SELECT * FROM rateManagement d Where d.type = 'merchant_rate_item' AND d.rate_table_id = @id",
        parameters: [
          {
            name: '@id',
            value: id,
          },
        ],
      };

      const rates = await Daos.rateMerchant.find(querySpec);

      if (rates.length === 0) {
        return res.json({
          success: false,
          message: 'There is currently no merchant rates',
        });
      }

      const hasHeaderItem = rates.find((item) => item.origin === 'Origin');

      if (hasHeaderItem) {
        // 2530: delete data containing header (Origin, Destination...)
        Daos.rateMerchant.deleteItem(hasHeaderItem.id, hasHeaderItem._partitionKey);
      }

      return res.json({
        success: true,
        data: hasHeaderItem ? rates.filter((item) => item.origin !== 'Origin') : rates,
      });
    } catch (error) {
      logger.error({ message: 'Unable to get rate management by rate merchant', error });

      return res.json({
        success: false,
        message: error.message,
      });
    }
  }

  async uploadMerchantRateFile(req: any, res: any) {
    const { files } = req.formData;
    const fileName = files.file[0].originalFilename;
    const filePath = files.file[0].filepath;

    logger.info({ message: `Start handling ${fileName}` });

    const base64 = Buffer.from(fs.readFileSync(filePath)).toString('base64');
    const workbook = XLSX.read(base64, { type: 'base64' });
    const rateSheet = workbook.Sheets[workbook.SheetNames[0]];
    const serviceOption: TRateSheetServiceOption = getCellValue(rateSheet, 2, 7);
    let rateSheetValidationResult: IRateSheetValidationResult;

    try {
      /**
       * STEP 1: Validate rate sheet
       */
      if (serviceOption.trim() === ENUM.RATE_SHEET_SERVICE_OPTION.B2B) {
        logger.info({
          message: 'Start validate B2B merchant ratesheet:',
          fileName,
        });

        rateSheetValidationResult = await RateManagementService.validateB2BRateSheet(
          rateSheet,
          {
            merchantAccNoFromRequest: req.query.merchant_no,
          },
          serviceOption,
        );

        if (!rateSheetValidationResult.success) {
          logger.error({ message: rateSheetValidationResult.message });

          return res.status(400).json({
            success: false,
            message: rateSheetValidationResult.message,
          });
        }
      } else {
        logger.info({
          message: 'Start validate B2C merchant ratesheet:',
          fileName,
        });

        rateSheetValidationResult = await RateManagementService.validateB2CRateSheet(rateSheet, {
          merchantAccNoFromRequest: req.query.merchant_no,
        });

        if (!rateSheetValidationResult.success) {
          logger.error({ message: rateSheetValidationResult.message });

          return res.status(400).json({
            success: false,
            message: rateSheetValidationResult.message,
          });
        }
      }

      logger.info({
        message: 'Finish validate merchant ratesheet:',
        fileName,
      });

      const {
        rates: rateSheetArr,
        version: rateSheetVersion,
        rateChargeType,
        emptyRows,
      } = rateSheetValidationResult.data;

      /**
       * STEP 2: Deactivate rate items if available then create new active rate with is_uploading status
       */
      const newMerchantRate = await RateManagementService.updateRateAndRateItems(
        fileName,
        rateSheetArr,
        rateSheetVersion,
        serviceOption,
        rateChargeType && rateChargeType === 'Flat Rate',
      );
      const newMerchantRateId = newMerchantRate.id;

      /**
       * STEP 3: Upload Rate Sheet to Blob Storage & return the response early
       */
      await AzureBlobStorage.uploadRateSheet(
        Buffer.from(fs.readFileSync(filePath)),
        newMerchantRateId,
      );
      logger.info({
        message: `Finish uploading ${fileName} to Blob Storage`,
        fileName: `${newMerchantRateId}.xlsx`,
      });

      res.status(201).json({
        success: true,
        message: `Rate Sheet ${fileName} is uploaded successfully`,
        data: newMerchantRateId,
      });

      const merchantNo = rateSheetArr[3][2];
      const merchantAccountNumber = AesUtils.CrtCounterEncrypt(merchantNo);

      /**
       * STEP 4: Update database, set merchant rate is_loading status to false
       */
      if (serviceOption === ENUM.RATE_SHEET_SERVICE_OPTION.B2B) {
        logger.info({
          message: 'Start create merchant_rate_item for B2B ratesheet',
        });

        const rateItems = await (rateChargeType === ENUM.RATE_CHARGE_TYPE.WEIGHT_BREAK
          ? RateManagementService.createB2BWeightBreakMerchantRateItem(rateSheetArr, {
              fileName,
              rateId: newMerchantRateId,
              emptyRows,
            })
          : RateManagementService.createB2BFlatRateMerchantRateItem(rateSheetArr, {
              fileName,
              rateId: newMerchantRateId,
            }));

        const { isActive, status } = await RateManagementService.updateMerchantRateFinishUpload(
          newMerchantRateId,
          merchantAccountNumber,
        );

        /**
         * STEP 5: Re-calculate HAWB chargeable weight and PD B2B amount
         */
        if (isActive && status === ENUM.approval_status.approved) {
          logger.info({
            message: 'Re-calculate chargeable weight for hawbs after approving rate sheet',
            newMerchantRate,
          });

          await HawbService.updateHawbChargeableWeightAndPdAmount(merchantAccountNumber, {
            ...(newMerchantRate as IMerchantRateV2),
            rateItems,
          });
        } else {
          logger.info({
            message:
              'Rate Sheet have not approved yet, will NOT re-calculate chargeable weight for hawbs',
            newMerchantRate,
          });
        }
      } else {
        logger.info({
          message: 'Start create merchant_rate_item for B2C ratesheet',
        });
        const merchantRateItems =
          rateSheetVersion === 2
            ? await RateManagementService.createMerchantRateItemsV2(rateSheetArr, {
                fileName,
                rateId: newMerchantRateId,
              })
            : await RateManagementService.createMerchantRateItems(rateSheet, rateSheetArr, {
                fileName,
                rateId: newMerchantRateId,
              });

        const merchantRate = await RateManagementService.updateMerchantRateFinishUpload(
          newMerchantRateId,
          merchantAccountNumber,
        );

        /**
         * STEP 5: Re-calculate chargeable weight
         */
        const {
          isActive,
          status,
          merchant_account_number: merchantAccountNumberEncrypted,
        } = merchantRate;

        // If merchant rate is active and has been approved
        // Then re-calculate chargeable weight for all parcels which are not invoiced yet
        if (isActive && status === ENUM.approval_status.approved) {
          logger.info({
            message: 'Re-calculate chargeable weight for parcels after approving rate sheet',
            merchantRate,
          });

          await this.reCalculateChargeableWeight(
            { ...merchantRate, rates: merchantRateItems },
            merchantAccountNumberEncrypted,
          );
        } else {
          logger.info({
            message:
              'Rate Sheet have not approved yet, will NOT re-calculate chargeable weight for parcels',
            merchantRate,
          });
        }
      }
    } catch (error: any) {
      logger.error({ message: `Handling ${fileName} failed!`, error });

      if (!res.headersSent) {
        return res.status(500).json({
          success: false,
          message: error.message,
        });
      }
    }
  }

  async setApprover(req: any, res: any) {
    const submitter = res.locals.decoded.user_info;
    const { id, approver_id, _partitionKey } = req.body;

    try {
      const data = await RateApprovalUtils.setRateSheetApprover(
        id,
        submitter,
        approver_id,
        AesUtils.CrtCounterEncrypt(_partitionKey),
      );

      return res.json({ data });
    } catch (error) {
      return res.json({
        requestSuccess: false,
        message: error,
      });
    }
  }

  async approveRateSheet(req: any, res: any) {
    try {
      const userToken = req.headers['token'];
      const decoded: string | Jwt | JwtPayload = JWT.verify(userToken);
      const userEmail = decoded['user_info'].email.toLowerCase();
      const { id, is_approved: isApproved, notes, _partitionKey } = req.body;
      const merchantAccountNumberEncrypted = AesUtils.CrtCounterEncrypt(_partitionKey);

      const data = await RateApprovalUtils.approveRateMerchant(
        id,
        userEmail,
        isApproved,
        notes,
        merchantAccountNumberEncrypted,
      );

      res.json({ data });

      if (isApproved && !data.is_uploading) {
        logger.info({
          message: 'Re-calculate chargeable weight for parcels after approving rate sheet',
          data,
        });

        if (data.service_option === ENUM.RATE_SHEET_SERVICE_OPTION.B2B) {
          const rateItems = await RateServices.getMerchantRateItemsByRateTableId(
            id,
            merchantAccountNumberEncrypted,
          );
          const rateData = { ...data, rateItems };

          await HawbService.updateHawbChargeableWeightAndPdAmount(
            merchantAccountNumberEncrypted,
            rateData,
          );
        } else {
          await this.reCalculateChargeableWeight(data, merchantAccountNumberEncrypted);
        }
      } else {
        logger.info({
          message:
            'Rate Sheet is being uploaded, will NOT re-calculate chargeable weight for parcels',
          data,
        });
      }
    } catch (error) {
      logger.error({ error });

      return res.json({
        requestSuccess: false,
        message: error,
      });
    }
  }

  async reCalculateChargeableWeight(rateSheet: any, merchantAccountNumberEncrypted: string) {
    try {
      // Re-calculate Chargeable Weight for following parcels
      const {
        id: rateSheetId,
        validity_from: fromDate,
        validity_to: toDate,
        rates: merchantRateItems,
        version,
      } = rateSheet;

      if (!merchantRateItems?.length) {
        // 1. get merchant rate items
        const rates = await RateServices.getMerchantRateItemsByRateTableId(
          rateSheetId,
          merchantAccountNumberEncrypted,
        );

        Object.assign(rateSheet, { rates });
      }

      // 2. get parcel is within the validity dates and not invoiced yet
      const parcelsNotInvoicedYet =
        await ManifestItemService.getParcelsToRecalculateChargeableWeight(
          merchantAccountNumberEncrypted,
          fromDate,
          toDate,
        );

      const parcelsToUpdate = [];

      // 3. re-calculate chargeable weight
      for (const parcel of parcelsNotInvoicedYet) {
        // 3.1 get rate by origin_country, country/destination, service_option, lmd_zone/rate_zone
        const rateItem =
          version === 2
            ? RateServices.getRateV2(parcel, rateSheet)
            : RateServices.getRate(parcel, rateSheet);

        if (!rateItem) {
          logger.error({ message: 'No rate found for parcel', parcelId: parcel.id });

          continue;
        }

        const dataUpdate: any = {
          id: parcel.id,
          ...rateItem,
        };

        parcelsToUpdate.push(dataUpdate);
      }

      // 4. update to db
      if (parcelsToUpdate.length > 0) {
        ManifestItemService.bulkUpdateParcelsAfterApprovedRateSheet(parcelsToUpdate, rateSheetId);
      }
    } catch (error) {
      logger.error({ error });
    }
  }

  async withdrawRateSheet(req: any, res: any) {
    const { id, _partitionKey } = req.body;
    const user_token = req.headers['token'];
    const decoded: string | Jwt | JwtPayload = JWT.verify(user_token);
    const user_email = decoded['user_info'].email.toLowerCase();

    try {
      const data = await RateApprovalUtils.withdrawRateSheet(
        id,
        user_email,
        AesUtils.CrtCounterEncrypt(_partitionKey),
      );

      return res.json({ data });
    } catch (error) {
      return res.json({
        requestSuccess: false,
        message: error,
      });
    }
  }

  async downloadRateSheet(req: any, res: any) {
    const { id } = req.params;

    try {
      const fileStream = await AzureBlobStorage.downloadRateSheet(id);

      if (!fileStream) {
        logger.error({ message: `Failed to download Rate Sheet ${id}.xlsx` });
        res.statusCode = 404;
        res.end();

        return;
      }

      const buffer = await FileUtils.streamToBuffer(fileStream);

      res.statusCode = 200;
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      );
      res.setHeader('Content-Disposition', `attachment; filename="${id}.xlsx"`);
      res.end(buffer);
    } catch (error) {
      logger.error({ message: `Failed to download Rate Sheet ${id}.xlsx`, error });
      res.statusCode = 404;
      res.end();
    }
  }

  async downloadRateSheetTemplate(req: any, res: any) {
    try {
      const { type } = req.params;
      const fileName =
        type === 'B2B' ? 'B2B Rate Sheet Template V2.xlsx' : 'ratesheet_template_v2.xlsx';

      const fileStream = await AzureBlobStorage.downloadFile(
        fileName,
        config.azureStorageContainer.TEMPLATE,
      );
      res.statusCode = 200;
      res.setHeader('Content-Type', 'application/text');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

      return await new Promise((resolve) => {
        streamToBuffer(fileStream, (error, buffer) => {
          if (error) {
            res.end(JSON.stringify(error));
            resolve(false);

            return;
          }

          res.end(buffer);
          resolve(true);
        });
      });
    } catch (error) {
      logger.error({ error });

      Promise.resolve(false);
    }
  }

  async getAirFreigtRateAfterConversion(req: any, res: any) {
    try {
      const { destination, origin, currency } = req.query;
      const airFreightRate = await RateServices.getAirFreightRate(origin, destination);

      if (Array.isArray(airFreightRate)) {
        const { exchangeRate }: any = await CurrencyConversionService.getExchangeRate(
          'SGD',
          currency,
        );
        const rateCharge = airFreightRate[0].rate * exchangeRate;

        return res.json({
          success: true,
          rateCharge,
        });
      }

      return res.json({
        success: false,
        message: 'air freight rate is not avaiable',
      });
    } catch (error) {
      return res.json({
        success: false,
        message: error,
      });
    }
  }
}
