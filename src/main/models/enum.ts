import { EXT } from '~/types/ext.type.js';

export const ENUM = {
  gaylordStatus: {
    new: 'New',
    readyToClosed: 'Ready To Close',
    incompleted: 'Incompleted',
    closing: 'Closing',
    closed: 'Closed',
    deleted: 'Deleted',
    notFound: 'Not Found',
  },
  parcelStatus: {
    /* Begin LSP Status */
    booked: 'booked',
    cancelled: 'cancelled',
    expired: 'expired',
    lmd_receive_booking: 'lmd_receive_booking',
    lmd_reject_booking: 'lmd_reject_booking',
    pending_booking_updates: 'pending_booking_updates',
    fmd_pickup_scheduled: 'fmd_pickup_scheduled',
    dropoff_scheduled: 'dropoff_scheduled',
    pending_pickup: 'pending_pickup',
    picked_up: 'picked_up',
    enroute_to_processing_hub: 'enroute_to_processing_hub',
    received_at_warehouse: 'received_at_warehouse',
    rejected_at_warehouse: 'rejected_at_warehouse',
    sorted: 'sorted',
    packed_to_gaylord: 'packed_to_gaylord',
    gaylord_ready_to_close: 'gaylord_ready_to_close',
    close_gaylord: 'close_gaylord',
    fwb_received: 'fwb_received',
    rcs_received_by_airline: 'rcs_received_by_airline',
    dep_from_origin_airport: 'dep_from_origin_airport',
    dep_from_transit_airport: 'dep_from_transit_airport',
    arr_at_destination_airport: 'arr_at_destination_airport',
    arr_at_transit_airport: 'arr_at_transit_airport',
    dlv_shipment_handed_over_to_consignee: 'dlv_shipment_handed_over_to_consignee',
    parcel_held_by_customs: 'parcel_held_by_custom',
    arrived_at_destination_custom_facility: 'arrived_at_destination_custom_facility',
    custom_cleared: 'custom_cleared',
    enroute_to_destination_sorting_hub: 'enroute_to_destination_sorting_hub',
    arrived_and_processing_at_sorting_hub: 'arrived_and_processing_at_sorting_hub',
    on_vehicle_for_delivery: 'on_vehicle_for_delivery',
    successful_delivery: 'successful_delivery',
    delivery_unsuccessful: 'delivery_unsuccessful',
    return_to_sq_triggered: 'return_to_sq_triggered',
    returned_to_sq: 'returned_to_sq',
    parcel_collected: 'parcel_collected',
    disposed: 'disposed',
    relabelled: 'relabelled',
    repackaged: 'repackaged',
    address_change: 'address_change',
    in_transit: 'in_transit',
    possible_delay_from_last_mile: 'possible_delay_from_last_mile',
    awaiting_collection: 'awaiting_collection',
    lost: 'lost',
    damaged: 'damaged',
    final_unsuccessful_delivery: 'final_unsuccessful_delivery',
    cn38_closed: 'cn38_closed',
    mawb_closed: 'mawb_closed',
    translated: 'translated',
    damaged_at_destination: 'damaged_at_destination',
    awaiting_delivery_instructions: 'awaiting_delivery_instructions',
    damage_internal: 'damage_internal',
    update_of_recipient_details: 'update_of_recipient_details',
    hold_rejected_by_lmd: 'hold_rejected_by_lmd',
    hold_hs_missing: 'hold_hs_missing',
    hold_dims_missing: 'hold_dimension_weight_missing',
    hold_hs_dims_missing: 'hold_hs_dimension_weight_missing',
    hold_cancelled: 'hold_cancelled',
    hold_split_parcel: 'hold_split_parcel',
    split_parcel_created: 'split_parcel_created',
    split_parcel_completed_processing: 'split_parcel_completed_processing',
    dg_internal: 'dg_internal',
    returned_to_merchant: 'returned_to_merchant',
    /* End LSP Status */

    ready_for_lodge_in: 'Ready for Lodge-in',
    notInManifest: 'Not in Manifest',
    alreadyScanned: 'Already Scanned',

    au: {
      error: 'ERROR',
      in_transit: 'In transit',
      delivery: 'Delivered',
      cannot_be_delivered: 'Cannot be delivered',
      article_damaged: 'Article damaged',
    },
  },
  parcelOption: {
    standard: 'standard',
    plus: 'plus',
    selfCollect: 'self-collect',
    postal: 'postal',
    express: 'express',
    b2b: 'b2b',
  },
  shipmentType: {
    domestic: 'domestic',
    international: 'international',
    returnDomestic: 'return-domestic',
  },
  auPost: {
    group: {
      EXPRESS_POST: 'Express Post',
      PARCEL_POST: 'Parcel Post',
    },
    descriptions: {
      ONBOARD_FOR_DELIVERY: 'Onboard for delivery',
      ITEM_PROCESSED_AT_FACILITY: 'Item processed at facility',
    },
    destination: 'AU',
  },
  batchJobType: {
    INFORM_CB: 'Inform CB',
    PARTITION_KEY: 'JobInformCb',
  },
  mawbStatus: {
    new: 'NEW',
    fin: 'FINISH',
    rcs: 'RCS',
    rcf: 'RCF',
    def: 'DEF',
    nfd: 'NFD',
    dlv: 'DLV',
    arr: 'ARR',
    arr_transit: 'ARR_TRANSIT',
    dep_transit: 'DEP_TRANSIT',
    dep: 'DEP',
    fwb: 'FWB',
    stastd: 'STASTD',
    boxCTriggered: 'BoxC Manifest Triggered',
  },
  serviceType: {
    standard: 'Standard',
    plus: 'Plus',
    selfCollect: 'Self-collect',
    others1: 'Others 1',
  },
  invoiceStatus: {
    ready_to_invoice: 'ready to invoice',
    invoiced: 'invoiced',
    DT_ready_to_invoice: 'DT ready to invoice',
    DT_invoiced: 'DT invoiced',
    DT_invoicing_error: 'DT invoicing error',
    not_available: 'NA',
    PD_ready_to_invoice: 'PD ready to invoice',
    service_expired: 'service expired',
    PD_invoice_error: 'PD invoicing error',
    PD_invoice_withheld: 'PD invoice withheld',
    PD_invoiced: 'PD invoiced',
    PD_reviewed_for_invoice: 'PD reviewed for invoice',
    PD_waived: 'PD waived',
  },
  approval_status: {
    created: 'Created',
    pending_for_approval: 'Pending for Approval',
    approved: 'Approved',
    rejected: 'Rejected',
    withdrawn: 'Withdrawn',
  },
  rti_info: {
    standardOrPlus: {
      deliveredOrUndeliverd1st: 'Delivered, Undelivered (1st attempt) - International/Domestic',
      deliveredOrUndeliverd2nd: 'Delivered, Undelivered (2nd attempt) - International/Domestic',
      deliveredOrUndeliverd3rd: 'Delivered, Undelivered (3rd attempt) - International/Domestic',
      receivedAtWarehouseArrivedProcessingAtSortingHub:
        'Received at warehouse - International, Arrived and processing at sorting hub - Domestic',
      notApplicable: 'Not Applicable',
    },
    selfCollect: {
      readyForCollectionAtLocker: 'Ready for collection at locker - International/Domestic',
      parcelCollectedOrNotCollected:
        'Parcel collected/Parcel not collected - International/Domestic',
      receivedAtWarehouseArrivedProcessingAtSortingHub:
        'Received at warehouse - International, Arrived and processing at sorting hub - Domestic',
      notApplicable: 'Not Applicable',
    },
    freight: {
      receivedAtWarehouse: 'Received at warehouse - International',
      notApplicable: 'Not Applicable',
    },
    postal: {
      deliveredOrUndeliverd1st: 'Delivered, Undelivered (1st attempt) - International/Domestic',
      deliveredOrUndeliverd2nd: 'Delivered, Undelivered (2nd attempt) - International/Domestic',
      deliveredOrUndeliverd3rd: 'Delivered, Undelivered (3rd attempt) - International/Domestic',
      receivedAtWarehouseArrivedProcessingAtSortingHub:
        'Received at warehouse - International, Arrived and processing at sorting hub - Domestic',
      notApplicable: 'Not Applicable',
    },
  },
  USER_ROLES_SERVER: {
    administrator: 'administrator',
    fulfillment_supervisor: 'fulfillment_supervisor',
    fulfillment_operator: 'fulfillment_operator',
    country_manager: 'country_manager',
    commercial_user: 'commercial_user',
    finance_user: 'finance_user',
    head_of_department: 'head_of_department',
    lsp_service_user: 'lsp_service_user',
    operation_user: 'operation_user',
  },
  axiosErrorCode: {
    timeout: 'ECONNABORTED',
  },
  LMD_SERVICE: {
    NJV: [
      'Ninjavan Singapore',
      'Ninjavan Malaysia',
      'Ninjavan Vietnam',
      'Ninjavan Indonesia',
      'Ninjavan Thailand',
    ],
    AUS_POST: ['Australia Post'],
    NZ_POST: ['New Zealand Post'],
    CJ_EXPRESS: ['CJ Express'],
    KERRY_HK: 'Kerry Hong Kong',
    EVRI_UK: 'EVRi UK',
    COURIERSPLEASE: 'CouriersPlease',
    LMG: ['Landmark Global'],
  },
  dateTimeFormat: {
    YYYYMMDD: 'YYYYMMDD',
    MMMYYYY: 'MMMYYYY',
    DDMMMYYYY: 'DD-MMM-YYYY',
  },
  invoiceType: {
    PD: 'parcelDelivery',
    DT: 'dutiesAndTaxes',
    DTv2: 'dutiesAndTaxesV2',
  },
  revenueReportType: {
    CGO_PLS_REPORT: 'CGO PLS REPORT',
  },
  labelType: {
    PARXL: 'PARXL',
    LMD: 'LMD',
  },
  measurements: {
    unit: {
      lb: 'lb',
      kg: 'kg',
      cm: 'cm',
      in: 'in',
      cm3: 'cm3',
      in3: 'in3',
    },
    weight: 'weight',
    width: 'width',
    height: 'height',
    length: 'length',
  },
  fmdType: {
    company: 'company',
    driver: 'driver',
  },
  taxRate: {
    status: {
      pending_for_approval: 'Pending Approval',
      approved: 'Approved',
      rejected: 'Rejected',
      change_requested: 'Change Requested',
    },
    action: {
      approved: 'Approved',
      rejected: 'Rejected',
      submit: 'Submit',
      edit: 'Edit',
    },
    type: {
      duty_and_tax: 'duty_and_tax',
      duty_and_tax_approval_path: 'duty_and_tax_approval_path',
      air_freight_rate: 'air_freight_rate',
    },
    taxType: {
      duty: 'duty',
      sales: 'sales',
      other: 'others',
    },
    valueToCheck: {
      CIF: 'CIF',
    },
  },
  surchargeEnum: {
    status: {
      pending_for_approval: 'Pending Approval',
      approved: 'Approved',
      rejected: 'Rejected',
    },
    objectType: {
      fuelSurcharge: 'fuel_surcharge',
      manual_tax_duty: 'manual_tax_duty',
      manual_tax_duty_history: 'manual_tax_duty_history',
      chargeable_exception: 'chargeable_exception',
      rate_exception: 'rate_exception',
      parcel_exception: 'parcel_exception',
      manually_raise: 'manually_raise',
    },
    uploadFile: {
      parcel_id: 'Parcel / Merchant ID',
      surcharge: 'Surcharge',
      charge_value: 'Charge Value',
      charge_currency: 'Charge Currency',
    },
  },
  portals: {
    merchantPortal: 'merchantPortal',
    lspPortal: 'lspPortal',
  },
  cacheName: {
    TAX_CODE: 'TaxCode',
    DESTINATION_V2: 'destinationV2',
    CUSTOM_BROKER: 'customBroker',
    COUNTRIES: 'countries',
    TIMEZONE_PREFIX: 'timezone_',
    SYSTEM_CONFIGURATIONS: 'SystemConfigurations',
    HS_Code: 'hscode',
    PARCEL_STATUSES: 'parcelStatuses',
    COLLECTION_POINTS: 'collection_points',
    NJV_ACCESS_TOKEN: 'NinjavanToken',
    MERCHANT: 'MERCHANT',
    EXCHANGE_RATE: 'exchangeRate',
    HS_CODE_MASTER_LIST_MAPPING: 'hscodeMasterListMapping',
    CAT_1_MERCHANT_LIST: 'cat_1_merchant_list',
    DELAY_CONFIGURATION: 'Delay_configurations',
    US_TARIFF_CONDITIONS: 'US_Tariff_Conditions',
  },
  invoiceChargeType: {
    delivery: 'Delivery Fee',
    dutyAndTax: 'Duty and Tax',
    surcharge: 'Surcharge',
    indirectTax: 'Indirect Tax',
  },
  invoiceChargeSubType: {
    DDP: 'DDP Admin',
    pickup: 'First Mile Surcharge',
    relabelled: 'Relabel',
    repackaged: 'Repack',
    translated: 'Translation',
    address_change: 'Address Change',
  },

  invoiceChargeTypeDBValue: {
    SURCHARGE: 'surcharge',
  },
  lmdNames: {
    AusPost: 'Australia Post',
    AustraliaBoxC: 'Australia BoxC',
    BoxC: 'BoxC',
    BoxCJapan: 'BoxC Japan',
    BoxCUK: 'BoxC UK',
    BoxCHK: 'BoxC Hong Kong',
    CanadaBoxC: 'Canada BoxC',
    CouriersPlease: 'CouriersPlease',
    Evri: 'EVRi UK',
    Hanjin: 'Hanjin',
    Janio: 'Janio',
    KerryHK: 'Kerry Hong Kong',
    KerryTH: 'Kerry',
    NZPost: 'New Zealand Post',
    NinjavanMY: 'Ninjavan Malaysia',
    NinjavanPH: 'Ninjavan Philippines',
    NinjavanSG: 'Ninjavan Singapore',
    NinjavanVN: 'Ninjavan Vietnam',
    Pickupp: 'Pickupp Taiwan',
    Sagawa: 'Sagawa Japan',
    WMGTaiwan: 'World Marketing Group Taiwan',
    WMGKorea: 'World Marketing Group Korea',
    WMGMalaysia: 'World Marketing Group Malaysia',
    WMGIndonesia: 'World Marketing Group Indonesia',
    WMGThailand: 'World Marketing Group Thailand',
    LMG: 'Landmark Global',
  },
  mawbTransaction: {
    new: 'new',
    created: 'created',
    boxCTriggered: 'boxc-triggered',
    finish: 'finish',
  },
  versionConfigurations: {
    EAST_WEST_CONFIG: 'EAST_WEST_CONFIG',
    NON_GST_REGISTERED_SINGAPORE: 'NON_GST_REGISTERED_SINGAPORE',
    ZIP_ENCRYPTION_METHOD_AES256: 'ZIP_ENCRYPTION_METHOD_AES256',
    UK_TARIFF: 'UK_TARIFF',
    COMMERCIAL_INVOICE: 'COMMERCIAL_INVOICE',
    AR_NEW_UPDATE: 'AR_NEW_UPDATE',
    AR_CSV_DATA: 'AR_CSV_DATA',
    LMG_MANIFEST: 'LMG_MANIFEST',
  },
  version: {
    V1: 'V1',
    V2: 'V2',
    TRUE: 'TRUE',
    FALSE: 'FALSE',
    ON: 'ON',
    OFF: 'OFF',
    TRUE_V1: 'TRUE-V1',
    TRUE_V2: 'TRUE-V2',
    PDF_KIT: 'PDF KIT',
    WITH_TAX: 'With Tax on Credit Line',
    WITHOUT_TAX: 'Without Tax',
  },
  destinationGroupType: {
    DEST_GROUP: 'destinationGroup',
    POINT_OF_DISCHARGE: 'pointOfDischarge',
    COUNTRY: 'country',
    CAPACITY: 'capacity',
    LMD: 'lmd',
    CUSTOM_BROKER: 'customBroker',
    SERVICE_OPTION: 'serviceOption',
    ZONE_LMD_DESTINATIONGROUP_MAPPING: 'zone_lmd_destinationGroup_mapping',
    ZONE_DESTINATIONGROUP_MAPPING: 'zone_destinationGroup_mapping',
  },

  // Log enum
  FunctionName: {
    MAIN: 'Main',
    LABEL: 'Label',
    LMDSTATUS: 'LmdStatus',
    BOOKING: 'Booking',
    GAYLORD: 'Gaylord',
    FREIGHT_RATE: 'FreightRate',
    INVOICE: 'Invoice',
    INVOICE_CHECK: 'Invoice Check',
    INVOICE_DT_V2: 'Invoice DT V2',
    B2C_ACCOUNT_MANAGEMENT: 'B2CAccountManagement',
    WEBHOOK: 'MerchantWebhook',
    AZURE_BLOB_STORAGE: 'AzureBlobStorage',
    AZ_STORAGE_QUEUE: 'AzureStorageQueue',
    EMAIL: 'Email',
    SYSTEM_CONFIGURATIONS: 'SystemConfigurations',
    REBOOK: 'Rebook',
    SET_INACTIVE_B2C_ACCOUNTS: 'SetInactiveB2CAccounts',
  },
  BankDetails: {
    FILE_NAME: 'BankDetailsMapping.xlsx',
    FIELD_ACCOUNT_TYPE: 'Account Type',
    FIELD_TITLE: 'Title',
    FIELD_BANK_DETAILS: 'Bank Details',
    PARTITION_KEY: 'bank_details',
    PREFIX: 'BankDetailsMapping',
    EXTENSION: EXT.XLSX,
  },
  conditionalBookingList: {
    FILE_NAME: 'Conditional Booking List.xlsx',
    PREFIX: 'Conditional Booking List',
    EXTENSION: EXT.XLSX,
  },
  TaxDisplay: {
    TAX_COUNTRY_MANAGEMENT_FILE_NAME: 'Tax Country Management.xlsx',
    FIELD_TAX_COUNTRY: 'Tax Country',
    FIELD_TAX_TYPE: 'Tax Type',
    FIELD_SR_FOOTER: 'SR Footer',
    FIELD_ZR_FOOTER: 'ZR Footer',
    FIELD_OS_FOOTER: 'OS Footer',
    FIELD_ADDRESS: 'Address',
    FIELD_COMPANY_REG_NO: 'Company Reg No',
    FIELD_TAX_REG_NO: 'Tax Reg No',
    FIELD_PDF_TAX_DISPLAY: 'PDF Tax Display',
    FIELD_OTHER_INFO_1: 'Other Info 1',
    FIELD_OTHER_INFO_2: 'Other Info 2',
    FIELD_OTHER_INFO_3: 'Other Info 3',
    PARTITION_KEY: 'tax_country_management',
    PREFIX: 'Tax Country Management',
    EXTENSION: EXT.XLSX,
  },
  INDIRECT_TAX: {
    GL_CODE_FILE_NAME: 'Indirect Tax GL Code.csv',
  },
  surchargeExceptionType: [
    'Return to Sender',
    'Disposal SR',
    'Disposal ZR',
    'Redelivery',
    'Relabeling',
    'Quarantine',
    'Inspection',
  ],
  currencies: ['SGD', 'KRW', 'AUD', 'USD', 'GBP', 'EUR'],
  customBroker: {
    AMS: 'AMS',
  },
  redeliveryReasons: {
    REBOOK: 'ReBook',
    REDATTEMPT: 'ReAttempt',
  },
  ISOCodes: {
    AU: 'AU',
    NZ: 'NZ',
    GB: 'GB',
    JP: 'JP',
    KR: 'KR',
    MY: 'MY',
    SG: 'SG',
    TH: 'TH',
    US: 'US',
    HK: 'HK',
    PH: 'PH',
    VN: 'VN',
    TW: 'TW',
    CA: 'CA',
  },
  PlsAppName: {
    LspService: 'lsp-service',
    PLS_UI: 'PLS-UI',
    PLS_API: 'INTERNAL-API',
    PlsAdmin: 'PLS Admin',
    LSP_PORTAL: 'LSP Portal',
  },
  PlsRequestHeaders: {
    CLIENT: 'x-pls-client',
    AZ_TOKEN: 'x-pls-az-token',
  },
  currency: {
    PHP: 'PHP',
  },
  RATE_SHEET_NOTIFY: {
    TYPE_OF_NOTIFY: {
      MONTH: 'MONTH',
      WEEK: 'WEEK',
    },
  },
  CAPACITY: {
    UNLIMITED: 'UNLIMITED',
  },
  SERVER_INSTANCE_ID: process.env.WEBSITE_INSTANCE_ID,

  WebjobName: {
    AR_FILE_SFTP: 'AR_FILE_SFTP',
    AU_POST_SFTP: 'AU_Post_SFTP',
    NOTIFY_RATE_SHEET_EXPIRY: 'NotifyRateSheetExpiry',
    NOTIFY_NEW_TRACKING_ID: 'NotifyNewTrackingIDs',
    PULL_EXCHANGE_RATE_DAILY: 'PullExchangeRateDaily',
    SET_PD_CLOSE_STATUS: 'SetPdCloseStatus',
    SEND_LMD_PRE_ALERT: 'SendLMDPreAlert',
    INVOICE: 'Invoice',
    SPARC_IMPORT_AWB: 'SPARC_ImportAWB',
    SUSPENSION_USER: 'SuspensionUser',
    REPORT_BOOKED_SHIPMENT: 'ReportBookedShipment',
  },
  DE_MINIMIS_TIER: {
    A: 'A',
    B: 'B',
  },
  bank_account: {
    SG_SGD_ACCOUNT: 'SG SGD Account',
  },
  PARCEL_RELATION_TYPE: {
    INDIVIDUAL: 'Individual',
    PARENT: 'Parent',
    CHILD: 'Child',
  },
  COUNTRIES: {
    TW: 'TW',
    US: 'US',
    HK: 'HK',
    MY: 'MY',
    KR: 'KR',
    GB: 'GB',
    SG: 'SG',
    AU: 'AU',
    JP: 'JP',
  },
  REBOOK_TYPE: {
    CHANGE_DG: 'change destination group',
    CHNAGE_BOOKING: 'change booking',
  },
  REBOOK_PARTITION_KEYS: {
    CHANGE_DG: 'change_dg',
    CHANGE_BOOKING: 'change_booking',
    CHANGE_BOOKING_PARCEL: 'change_booking_parcel',
  },
  chargeType: {
    GROSS_WEIGHT: 'gross weight',
    VOLUMETRIC_WEIGHT: 'volumetric weight',
  },
  MerchantType: {
    NORMAL: 'normal',
    CATEGORY_1: 'category_1',
  },
  SOCKET: {
    // This should be in sync with client side
    ROOMS: {
      CUSTOM_MANAGEMENT: 'CUSTOM_MANAGEMENT',
      REBOOK: 'REBOOK',
    },
    EVENTS: {
      CUSTOM_MANAGEMENT: {
        UPLOAD_MASTER_LIST_FILE: 'CUSTOM_MANAGEMENT:UPLOAD_MASTER_LIST_FILE',
        UPDATE: 'CUSTOM_MANAGEMENT:UPDATE',
      },
      REBOOK: {
        REBOOK_CHANGE_DG: 'REBOOK:REBOOK_CHANGE_DG',
        UPDATE: 'REBOOK:UPDATE',
      },
      APP: {
        JOIN_ROOM: 'WS:JOIN_ROOM',
        LEAVE_ROOM: 'WS:LEAVE_ROOM',
        APP_AUTH: 'APP:AUTH',
        AUTH_ERROR: 'AUTH:ERROR',
      },
    },
  },
  BLOB_NAME: {
    DELAY_CONFIGURATION: 'DELAY_CONFIGURATIONS.JSON',
  },
  IM_NOTIFICATION: 'IM_Notification',
  LMG: {
    LABEL_FORMAT: {
      PDF: 'PDF',
    },
    LABEL_ENCODING: {
      LINKS: 'LINKS',
    },
  },
  RATE_SHEET_SERVICE_OPTION: {
    B2B: 'B2B',
    B2C: 'B2C',
  },
  RATE_CHARGE_TYPE: {
    FLAT_RATE: 'Flat Rate',
    WEIGHT_BREAK: 'Weight Break',
  },
} as const;

export enum COUNTRY_CODE_2 {
  HK = 'HK',
  MY = 'MY',
}

export enum InvoiceStatus {
  NOT_AVAILABLE = 'NA',
  PD_READY_TO_INVOICE = 'PD ready to invoice',
  PD_INVOICED = 'PD invoiced',
}

export enum DE_MINIMIS_TIER {
  TIER_A = 'A',
  TIER_B = 'B',
}

export enum YES_OR_NO {
  YES = 'Y',
  NO = 'N',
}

export enum CURRENCIES_NAME {
  USD = 'USD',
}
