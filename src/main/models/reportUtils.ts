import axios from 'axios';
import { format, parseISO } from 'date-fns';
import range from 'lodash/range.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ParcelUtils } from '~/models/parcelUtils.js';

import CountryISOService from '~/services/countryISO-service.js';
import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';

import { CustomManifestUtils } from '~/utilities/customManifestUtils.js';
import { DateUtils } from '~/utilities/dateUtils.js';
import { DimensionUtils } from '~/utilities/dimensionUtils.js';
import { ExcelUtils, IField } from '~/utilities/excelUtils.js';
import { InvoiceUtils } from '~/utilities/invoiceUtils.js';
import { separateThousands } from '~/utilities/numberUtils.js';
import { UnitConverterUtils } from '~/utilities/unitConverterUtils.js';

import { config } from '~/configs/index.js';
import { CONSTANTS } from '~/constants/index.js';
import { ParcelToInvoice } from '~/types/manifest-item.type.js';
import { IMerchant } from '~/types/merchant.type.js';
import { IOperationHub } from '~/types/operation-hub.type.js';

import { MerchantService } from '../services/merchant-service.js';
import { OperationHubService } from '../services/operation-hub.service.js';
import { Merchant } from '../types/commercial-invoice.type.js';
import { Container } from '../types/container.type.js';
import { ManifestItem } from '../types/manifest-item.type.js';
import { IMawbCreated } from '../types/mawb.type.js';

import { ENUM } from './enum.js';

export const ReportUtils = {
  setGaylord(parcel: { gaylord_no: string; gaylord: any }, gaylords: any[]) {
    if (!parcel.gaylord_no || parcel.gaylord_no === '') {
      return;
    }

    const gaylord = gaylords.find((item: { id: any }) => {
      return parcel.gaylord_no === item.id;
    });
    parcel.gaylord = gaylord;
  },

  setMawbNo(parcel: { gaylord_no: string; gaylord: any; mawb_no: any }) {
    if (!parcel.gaylord_no || parcel.gaylord_no === '') {
      return;
    }

    const { gaylord } = parcel;

    if (gaylord && gaylord.mawb_no) {
      parcel.mawb_no = gaylord.mawb_no;
    }
  },

  getTrackingStatus(tracking_status: any[], status: any) {
    return tracking_status.filter((item: { status: any }) => {
      return item.status === status;
    });
  },

  getParcelTrackingStatusDate(parcel: any, status: any, i: any) {
    if (!parcel.tracking_status || parcel.tracking_status.length === 0) {
      return;
    }

    const lstStatus = this.getTrackingStatus(parcel.tracking_status, status);

    if (i) {
      return lstStatus[i] ? lstStatus[i].date : null;
    }

    return lstStatus.at(-1) ? lstStatus.at(-1).date : null;
  },

  async generatePdInvoice(data, merchant, taxInfoArr) {
    data.totalDue = InvoiceUtils.calculatePdTotalDue({
      routings: data.routings,
      surcharges: data.surcharges,
    });
    const taxInfo = taxInfoArr.find(
      (item) => item.taxCountry?.toLowerCase() === merchant.taxCountry?.toLowerCase(),
    );
    const payload = {
      merchant: {
        name: merchant.merchant_invoice_name || merchant.merchant_name,
        accountNumber: merchant.merchant_account_number,
        phoneNumber: merchant.phone_number,
        addressLine1: data.merchant_address_line1,
        addressLine2: data.merchant_address_line2,
        addressLine3: data.merchant_address_line3,
        type: data.merchant_type,
        taxType: merchant.taxType,
        taxCountry: merchant.taxCountry,
        bankDetails: merchant.bankDetail,
      },
      invoice: {
        number: data.invoiceNumber,
        generationTime: data.invoice_generation_datetime,
        date: data.invoice_date,
        month: data.invoiceMonth,
        rateCurrency: data.invoiceRateCurrency,
        creditTerm: data.creditTerm,
      },
      station: { ...taxInfo },
      routings: data.routings,
      surcharges: data.surcharges,
      taxZone: data.taxZone,
    };
    const response = await axios.post(
      `${process.env.PDF_FUNC_URL}/invoice-pd`,
      { payload },
      { responseType: 'arraybuffer' },
    );

    return response.data;
  },

  createDTExcelBuffer(
    invoiceData: {
      invoiceCurrency: string;
      parcels: ParcelToInvoice[];
      totalTaxInMerchantRateCurrency: number;
      totalSurcharge: number;
      totalDue: number;
      totalSurchargeTax: number;
    },
    merchantInvoiceData: { merchant_name: string; merchantAccountNo: string; invoicingInfo: any },
    invoiceNumber: string,
  ) {
    const parcelWithSurcharges = invoiceData.parcels.filter((item) => item.ddp);

    const header: any = [
      ['Merchant Name: ', merchantInvoiceData.merchant_name],
      ['Merchant Account No: ', merchantInvoiceData.merchantAccountNo],
      [],
      ['DUTY AND TAX INVOICE DETAILS'],
    ];

    const dutyAndTaxFields = [
      { label: 'PLS Shipment Booking Reference', value: 'id', default: '' },
      {
        label: 'Shipment Tracking ID',
        value: (row: { tracking_id: any }) => ({ v: row.tracking_id, t: 's' }),
        default: '',
      },
      { label: 'Merchant Order No', value: 'merchant_order_no', default: '' },
      { label: 'Rate Zone', value: 'rateZone', default: '' },
      { label: 'Tax code', value: 'taxCode', default: '' },
      { label: 'Charge Type', value: () => 'Duty and Tax' },
      { label: 'Rate Currency', default: invoiceData.invoiceCurrency },
      {
        label: 'Rate',
        value: (item) =>
          ExcelUtils.showValueByCurrency(item.totalActualTax, invoiceData.invoiceCurrency),
        default: '',
      },
      { label: 'Tax Currency', default: invoiceData.invoiceCurrency },
      { label: 'Tax', value: () => '0.00' },
    ];

    const ddpSurchargeFields = [
      { label: 'PLS Shipment Booking Reference', value: 'id', default: '' },
      {
        label: 'Shipment Tracking ID',
        value: (row: { tracking_id: any }) => ({ v: row.tracking_id, t: 's' }),
        default: '',
      },
      { label: 'Fee Type', value: (item) => item.ddp.type, default: '' },
      { label: 'Tax code', value: (item) => item.ddp.taxCode, default: '' },
      { label: '', value: '', default: '' },
      { label: '', value: '', default: '' },
      { label: 'Rate Currency', default: invoiceData.invoiceCurrency },
      {
        label: 'Rate',
        value: (item) => ExcelUtils.showValueByCurrency(item.ddp.rate, invoiceData.invoiceCurrency),
        default: '',
      },
      { label: 'Tax Currency', default: invoiceData.invoiceCurrency },
      {
        label: 'Tax',
        value: (item) => ExcelUtils.showValueByCurrency(item.ddp.tax, invoiceData.invoiceCurrency),
        default: '',
      },
    ];

    const contentExcel = header
      .concat(ExcelUtils.generateSheetData(invoiceData.parcels, dutyAndTaxFields))
      .concat([])
      .concat([
        [
          '',
          '',
          '',
          '',
          '',
          'SUBTOTAL',
          invoiceData.invoiceCurrency,
          {
            v: invoiceData.totalTaxInMerchantRateCurrency,
            z: '#,##0.00',
            t: 'n',
          },
          invoiceData.invoiceCurrency,
          '0.00',
        ],
      ]);

    if (parcelWithSurcharges.length > 0) {
      contentExcel.push(
        [],
        ['SURCHARGE INVOICE DETAILS'],
        ...ExcelUtils.generateSheetData(parcelWithSurcharges, ddpSurchargeFields),
        [],
        [
          '',
          '',
          '',
          '',
          '',
          'SUBTOTAL',
          invoiceData.invoiceCurrency,
          {
            v: invoiceData.totalSurcharge,
            z: '#,##0.00',
            t: 'n',
          },
          invoiceData.invoiceCurrency,
          {
            v: invoiceData.totalSurchargeTax,
            z: '#,##0.00',
            t: 'n',
          },
        ],
      );
    }

    return ExcelUtils.createBufferXLSX(`${invoiceNumber} detail`, contentExcel);
  },

  export_custom_broker_manifest_SEA_Template(data: any, currency: any) {
    const fields: any = [
      {
        label: 'LOADING PORT',
        value: (row: { op_hub: { airport_code: any } }) => row.op_hub && row.op_hub.airport_code,
      },
      {
        label: 'MAWB NO',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
      },
      {
        label: 'VESSEL NAME',
        value: (row: { mawb: { flight_no: any } }) => row.mawb && row.mawb.flight_no,
      },
      {
        label: 'ARRIVAL DATE',
        value: (row: { mawb: { STA: any } }) => row.mawb && row.mawb.STA,
      },
      {
        label: 'DISCHARGE PORT',
        value: 'airport_code',
      },
      {
        label: 'HAWB NO',
        value: 'tracking_id',
      },
      {
        label: 'MAWB NO',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
      },
      {
        label: 'MAWB DATE',
        value: (row: { mawb: { mawb_date: any } }) => row.mawb && row.mawb.mawb_date,
      },
      {
        label: 'SHIPPER COUNTRY CODE',
        value: (row: { merchant: { country: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.country),
      },
      {
        label: 'SHIPPER NAME',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'SHIPPER ADDRESS',
        value: (row: {
          merchant: {
            street: string;
            city: string;
            state: string;
            postal_code: any;
            country: string;
          };
        }) =>
          row.merchant &&
          `${AesUtils.CrtCounterDecrypt(row.merchant.street)} ${AesUtils.CrtCounterDecrypt(row.merchant.city)} ${AesUtils.CrtCounterDecrypt(row.merchant.state)} ${row.merchant.postal_code} ${AesUtils.CrtCounterDecrypt(row.merchant.country)}`,
      },
      {
        label: 'SHIPPER PHONE',
        value: (row: { merchant: { phone_number: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.phone_number),
      },
      {
        label: 'CONSIGNEE COUNTRY CODE',
        value: (row: { country: string }) => CountryISOService.getCountryCode2(row.country),
      },
      {
        label: 'CONSIGNEE NAME',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE ADDRESS',
        value: (row: {
          recipient_addressline1: any;
          recipient_addressline2: any;
          recipient_addressline3: any;
          city_suburb: any;
          state: any;
          postcode: any;
        }) =>
          [
            row.recipient_addressline1,
            row.recipient_addressline2,
            row.recipient_addressline3,
            row.city_suburb,
            row.state,
            row.postcode,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'CONSIGNEE PHONE',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(' '),
      },
      {
        label: 'INSURANCE (USD)',
      },
      {
        label: 'FREIGHT (USD)',
        value: 'shipping_and_insurance_cost',
      },
      {
        label: 'WEIGHT (KG)',
        value: (row: {
          actual_weight_unit: any;
          weight_unit: any;
          actual_weight: any;
          weight: any;
        }) => {
          const unit = row.actual_weight_unit ? row.actual_weight_unit : row.weight_unit;
          const weight = row.actual_weight ? row.actual_weight : row.weight;

          return unit === ENUM.measurements.unit.lb
            ? separateThousands(Number(weight) * 0.453_592_37)
            : separateThousands(weight);
        },
      },
      {
        label: 'BAG NO',
        value: 'gaylord_no',
      },
      {
        label: 'REMARK',
        value: 'incoterm',
      },
      {
        label: 'SHIPPER POST CODE',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'DESCRIPTION OF GOODS',
        value: (row: { item: any[] }) => {
          if (Array.isArray(row.item)) {
            let text = '';
            row.item.forEach((x: { description: any }) => {
              text += `${x.description};`;
            });

            return text;
          }

          return '';
        },
      },
      {
        label: 'ORIGIN COUNTRY CODE',
        value: (row: { op_hub: { country: string } }) =>
          row.op_hub && CountryISOService.getCountryCode2(row.op_hub.country),
      },
      {
        label: 'PACKAGING QTY.',
        default: '1',
      },
      {
        label: 'PACKAGING TYPE CODE',
        default: 'PK',
      },
      {
        label: 'FOB',
        value: (row: { item: any[] }) => {
          let total = 0;

          if (Array.isArray(row.item)) {
            total = row.item.reduce(
              (acc: number, item: { total_declared_value: any }) =>
                acc + Number(item.total_declared_value),
              0,
            );
          }

          return total;
        },
      },
      {
        label: 'UOM',
      },
      {
        label: 'QTY',
        value: (row: { item: string | any[] }) => {
          if (Array.isArray(row.item)) {
            return row.item.length;
          }

          return 1;
        },
        default: '1',
      },
      {
        label: 'Product Code',
      },
      {
        label: 'Consignee Zip Code',
        value: 'postcode',
      },
      {
        label: 'Currency',
        value: 'merchant_declared_currency',
        default: currency,
      },
    ];

    return ExcelUtils.createBuffer('Manifest', data, fields);
  },

  async export_custom_broker_manifest_SG_Template(data) {
    function mapGstAndTaxCollectedForSG(taxRegisteredIdList, totalDeclaredValueSgd) {
      const encryptedSGCode = AesUtils.CrtCounterEncrypt('SG');
      const gst = taxRegisteredIdList.find((item) => item.country === encryptedSGCode);

      if (!gst) {
        return {
          gstId: 'NIL',
          taxCollected: 'NIL',
        };
      }

      let taxCollected = 'NIL';

      if (gst.taxAtPointOfSale && totalDeclaredValueSgd <= config.gst.threshold.sg) {
        taxCollected = (totalDeclaredValueSgd * 0.09).toFixed(2);
      }

      return {
        gstId: gst.taxRegisteredId,
        taxCollected,
      };
    }

    const cat1MerchantList = await MerchantService.getCat1MerchantList();

    const fields: any = [
      {
        label: 'LOADING PORT',
        value: (row: { op_hub: { airport_code: any } }) => row.op_hub && row.op_hub.airport_code,
      },
      {
        label: 'MAWB NO',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
      },
      {
        label: 'VESSEL NAME',
        value: (row: { mawb: { flight_no: any } }) => row.mawb && row.mawb.flight_no,
      },
      {
        label: 'ARRIVAL DATE',
        value: (row: { mawb: { STA: any } }) => row.mawb && row.mawb.STA,
      },
      {
        label: 'DISCHARGE PORT',
        value: 'airport_code',
      },
      {
        label: 'HAWB NO',
        value: 'tracking_id',
      },
      {
        label: 'MAWB NO',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
      },
      {
        label: 'MAWB DATE',
        value: (row: { mawb: { mawb_date: any } }) => row.mawb && row.mawb.mawb_date,
      },
      {
        label: 'SHIPPER COUNTRY CODE',
        value: (row: { merchant: { country: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.country),
      },
      {
        label: 'SHIPPER NAME',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'SHIPPER ADDRESS',
        value: (row: { merchant: { street: any; city: any; state: any; postal_code: any } }) => {
          if (!row.merchant) return '';

          return [
            row.merchant.street,
            row.merchant.city,
            row.merchant.state,
            row.merchant.postal_code,
          ]
            .filter(Boolean)
            .map(AesUtils.CrtCounterDecrypt)
            .join(' ');
        },
      },
      {
        label: 'SHIPPER PHONE',
        value: (row: { merchant: { phone_number: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.phone_number),
      },
      {
        label: 'CONSIGNEE COUNTRY CODE',
        value: (row: { country: string }) => CountryISOService.getCountryCode2(row.country),
      },
      {
        label: 'CONSIGNEE NAME',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE ADDRESS',
        value: (row: {
          recipient_addressline1: any;
          recipient_addressline2: any;
          recipient_addressline3: any;
          city_suburb: any;
          state: any;
          postcode: any;
        }) =>
          [
            row.recipient_addressline1,
            row.recipient_addressline2,
            row.recipient_addressline3,
            row.city_suburb,
            row.state,
            row.postcode,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'CONSIGNEE PHONE',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE EMAIL',
        value: (row) => CustomManifestUtils.getConsigneeEmail(row, cat1MerchantList),
      },
      {
        label: 'INSURANCE (USD)',
      },
      {
        label: 'FREIGHT (USD)',
        value: 'shipping_and_insurance_cost',
      },
      {
        label: 'WEIGHT (KG)',
        value: (row: {
          actual_weight_unit: any;
          weight_unit: any;
          actual_weight: any;
          weight: any;
        }) => {
          const unit = row.actual_weight_unit ? row.actual_weight_unit : row.weight_unit;
          const weight = row.actual_weight ? row.actual_weight : row.weight;

          return unit === ENUM.measurements.unit.lb
            ? separateThousands(UnitConverterUtils.lbtokg(Number(weight)))
            : separateThousands(weight);
        },
      },
      {
        label: 'BAG NO',
        value: 'gaylord_no',
      },
      {
        label: 'REMARK',
        value: 'incoterm',
      },
      {
        label: 'SHIPPER POST CODE',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'DESCRIPTION OF GOODS',
        value: (row: { item: any[] }) => {
          if (Array.isArray(row.item)) {
            return row.item.map((x: { description: any }) => x.description).join(';');
          }

          return '';
        },
      },
      {
        label: 'ORIGIN COUNTRY CODE',
        value: (row: { op_hub: { country: string } }) =>
          row.op_hub && CountryISOService.getCountryCode2(row.op_hub.country),
      },
      {
        label: 'PACKAGING QTY.',
        default: '1',
      },
      {
        label: 'PACKAGING TYPE CODE',
        default: 'PK',
      },
      {
        label: 'CIF',
        value: 'FOB',
      },
      {
        label: 'UOM',
      },
      {
        label: 'QTY',
        value: (row: { item: string | any[] }) => {
          if (Array.isArray(row.item)) {
            return row.item.length;
          }

          return 1;
        },
        default: '1',
      },
      {
        label: 'Product Code',
      },
      {
        label: 'Consignee Zip Code',
        value: 'postcode',
      },
      {
        label: 'Currency',
        default: 'SGD',
      },
      {
        label: 'GST REGISTERED ID',
        value: 'gstId',
      },
      {
        label: 'TAX COLLECTED AT POS',
        value: 'taxCollected',
      },
    ];

    for (const parcel of data) {
      let totalDeclaredValueInSGD = 0;

      if (
        parcel.shipment_type === ENUM.shipmentType.international &&
        Array.isArray(parcel.item) &&
        parcel.item.length > 0 &&
        !parcel.item[0].converted_subtotal_declared_value
      ) {
        await ParcelUtils.calculateConvertedCurrency(parcel);
        ManifestItemService.updateParcel({ id: parcel.id, item___overwrite: parcel.item });
      }

      if (Array.isArray(parcel.item)) {
        totalDeclaredValueInSGD = parcel.item.reduce(
          (
            acc: number,
            item: { converted_subtotal_declared_value: any; total_declared_value: any },
          ) => acc + Number(item.converted_subtotal_declared_value || item.total_declared_value),
          0,
        );
      }

      const { gstId, taxCollected } = mapGstAndTaxCollectedForSG(
        parcel.merchant?.tax_registered_id_list || [],
        totalDeclaredValueInSGD,
      );
      parcel.FOB = totalDeclaredValueInSGD;
      parcel.gstId = gstId;
      parcel.taxCollected = taxCollected;
    }

    return ExcelUtils.createBuffer('Manifest', data, fields);
  },

  async export_custom_broker_manifest_MY_Template(
    flightNo: any,
    STA: any,
    STD: any,
    data: any[],
    currency: any,
    mawb: { tracking_status: any[] },
    allConversionRates: any[],
    customBroker: any,
  ) {
    const mawbInfo = mawb.tracking_status.find(
      (m: { tranx_type: string }) => m.tranx_type === 'created',
    );

    const cat1MerchantList = await MerchantService.getCat1MerchantList();

    const fields: IField[] = this.getFieldsForMY(
      flightNo,
      STA,
      STD,
      currency,
      allConversionRates,
      customBroker,
      mawbInfo,
      cat1MerchantList,
    );

    return ExcelUtils.createBuffer(
      'Manifest',
      data.flatMap((parcel: { item: any[] }) => {
        const items = [];

        for (const item of parcel.item) {
          for (let i = 0; i < +item.quantity; i++) {
            items.push({
              ...item,
              quantity: 1,
              total_declared_value: (+item.total_declared_value / +item.quantity).toFixed(2),
              parcel,
            });
          }
        }

        return items;
      }),
      fields,
    );
  },

  async export_custom_broker_manifest_PH_Template(
    flightNo: any,
    STA: any,
    STD: any,
    data: any[],
    mawb: { tracking_status: any[] },
  ) {
    const mawbInfo = mawb.tracking_status.find(
      (m: { tranx_type: string }) => m.tranx_type === 'created',
    );

    const cat1MerchantList = await MerchantService.getCat1MerchantList();

    const fields: any = [
      {
        label: 'LOADING PORT',
        value: (row: { parcel: { op_hub: { airport_code: any } } }) =>
          row.parcel.op_hub && row.parcel.op_hub.airport_code,
      },
      {
        label: 'MAWB NO',
        value: (row: { parcel: { mawb: { mawb_no: any } } }) =>
          row.parcel.mawb && row.parcel.mawb.mawb_no,
      },
      {
        label: 'VESSEL NAME',
        value: () => {
          return flightNo ? `${flightNo}/${STD}` : '';
        },
      },
      {
        label: 'ARRIVAL DATE',
        value: () => STA,
      },
      {
        label: 'DISCHARGE PORT',
      },
      {
        label: 'HAWB NO',
        value: (row: { parcel: { tracking_id: any } }) => row.parcel.tracking_id,
      },
      {
        label: 'MAWB NO',
        value: (row: { parcel: { mawb: { mawb_no: any } } }) =>
          row.parcel.mawb && row.parcel.mawb.mawb_no,
      },
      {
        label: 'MAWB DATE',
        value: (row: { parcel: { mawb: { tracking_status: any } } }) => {
          if (row.parcel.mawb && row.parcel.mawb.tracking_status) {
            return new Date(mawbInfo.mawb_date).toISOString().split('.')[0];
          }
        },
      },
      {
        label: 'SHIPPER COUNTRY CODE',
        value: (row: { parcel: { merchant: { country: string } } }) =>
          row.parcel.merchant && AesUtils.CrtCounterDecrypt(row.parcel.merchant.country),
      },
      {
        label: 'SHIPPER NAME',
        value: (row: { parcel: { merchant: { merchant_name: string } } }) =>
          row.parcel.merchant && AesUtils.CrtCounterDecrypt(row.parcel.merchant.merchant_name),
      },
      {
        label: 'SHIPPER ADDRESS',
        value: (row: { parcel: { merchant: any } }) => {
          if (!row.parcel.merchant) return '';
          const { merchant } = row.parcel;
          let result = '';

          if (merchant.street) result += `${AesUtils.CrtCounterDecrypt(merchant.street)} `;

          if (merchant.city) result += `${AesUtils.CrtCounterDecrypt(merchant.city)} `;

          if (merchant.state) result += `${AesUtils.CrtCounterDecrypt(merchant.state)} `;

          if (merchant.postal_code) result += merchant.postal_code;

          return result;
        },
      },
      {
        label: 'SHIPPER PHONE',
        value: (row: { parcel: { merchant: { phone_number: string } } }) =>
          row.parcel.merchant && AesUtils.CrtCounterDecrypt(row.parcel.merchant.phone_number),
      },
      {
        label: 'CONSIGNEE COUNTRY CODE',
        value: (row: { parcel: { country: string } }) =>
          CountryISOService.getCountryCode2(row.parcel.country),
      },
      {
        label: 'CONSIGNEE NAME',
        value: (row: { parcel: { recipient_first_name: any; recipient_last_name: any } }) =>
          [row.parcel.recipient_first_name, row.parcel.recipient_last_name]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'CONSIGNEE ADDRESS',
        value: (row: {
          parcel: {
            recipient_addressline1: any;
            recipient_addressline2: any;
            recipient_addressline3: any;
            city_suburb: any;
            state: any;
            postcode: any;
          };
        }) =>
          [
            row.parcel.recipient_addressline1,
            row.parcel.recipient_addressline2,
            row.parcel.recipient_addressline3,
            row.parcel.city_suburb,
            row.parcel.state,
            row.parcel.postcode,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'CONSIGNEE PHONE',
        value: (row: { parcel: { phone_country_code: any; phone: any } }) =>
          [row.parcel.phone_country_code, row.parcel.phone].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE EMAIL',
        value: (row: any) => CustomManifestUtils.getConsigneeEmail(row.parcel, cat1MerchantList),
      },
      {
        label: 'INSURANCE (USD)',
      },
      {
        label: 'FREIGHT (USD)',
      },
      {
        label: 'WEIGHT (KG)',
        value: (row: { parcel: { weight_unit: any; weight: string } }) => {
          const unit = row.parcel.weight_unit;
          const weight = Number.parseFloat(row.parcel.weight);

          return unit === ENUM.measurements.unit.lb
            ? separateThousands(Number(weight) * 0.453_592_37)
            : separateThousands(weight);
        },
      },
      {
        label: 'BAG NO',
        value: (row: { parcel: { gaylord_no: any } }) => row.parcel.gaylord_no,
      },
      {
        label: 'REMARK',
        value: (row: { parcel: { incoterm: any } }) => row.parcel.incoterm,
      },
      {
        label: 'SHIPPER POST CODE',
        value: (row: { parcel: { merchant: { postal_code: any } } }) =>
          row.parcel.merchant && row.parcel.merchant.postal_code,
      },
      {
        label: 'DESCRIPTION OF GOODS',
        value: (row: { description: any }) => {
          return row.description;
        },
      },
      {
        label: 'ORIGIN COUNTRY CODE',
        value: (row: { parcel: { op_hub: { country: string } } }) =>
          row.parcel.op_hub && CountryISOService.getCountryCode2(row.parcel.op_hub.country),
      },
      {
        label: 'PACKAGING QTY.',
        default: '1',
      },
      {
        label: 'PACKAGING TYPE CODE',
        default: 'PK',
      },
      {
        label: 'FOB',
        value: (row: { converted_subtotal_declared_value: any }) =>
          row.converted_subtotal_declared_value,
      },
      {
        label: 'UOM',
      },
      {
        label: 'QTY',
        value: (row: { quantity: any }) => {
          return row.quantity;
        },
        default: '1',
      },
      {
        label: 'Product Code',
        value: (row: { hs_code: any }) => {
          return row.hs_code;
        },
      },
      {
        label: 'Consignee Zip Code',
        value: (row: { parcel: { postcode: any } }) => row.parcel.postcode,
      },
      {
        label: 'Currency',
        default: ENUM.currency.PHP,
      },
      {
        label: 'IS DANGEROUS GOOD',
        default: 'FALSE',
      },
      {
        label: 'LENGTH',
        value: (row: { parcel: { length: string; dimensions_unit: string } }) => {
          let value = Number.parseFloat(row.parcel.length);

          if (row.parcel.dimensions_unit === ENUM.measurements.unit.in) {
            value = DimensionUtils.getCMSide(value, ENUM.measurements.unit.in);
          }

          return value || '';
        },
      },
      {
        label: 'WIDTH',
        value: (row: { parcel: { width: string; dimensions_unit: string } }) => {
          let value = Number.parseFloat(row.parcel.width);

          if (row.parcel.dimensions_unit === ENUM.measurements.unit.in) {
            value = DimensionUtils.getCMSide(value, ENUM.measurements.unit.in);
          }

          return value || '';
        },
      },
      {
        label: 'HEIGHT',
        value: (row: { parcel: { height: string; dimensions_unit: string } }) => {
          let value = Number.parseFloat(row.parcel.height);

          if (row.parcel.dimensions_unit === ENUM.measurements.unit.in) {
            value = DimensionUtils.getCMSide(value, ENUM.measurements.unit.in);
          }

          return value || '';
        },
      },
    ];

    return ExcelUtils.createBuffer(
      'Manifest',
      data.flatMap((parcel: any) => {
        return parcel.item.map((item) => ({ ...item, parcel }));
      }),
      fields,
    );
  },

  export_custom_broker_manifest_KR_Template(data: any[], currency: any) {
    let fields: any = [
      {
        label: 'VESSEL NAME',
        value: (row: { mawb: { flight_no: any } }) => row.mawb && row.mawb.flight_no,
      },
      {
        label: 'ARRIVAL DATE',
        value: (row: { mawb: { STA: any } }) => row.mawb && row.mawb.STA,
      },
      {
        label: 'LOADING PORT',
        value: (row: { op_hub: { airport_code: any } }) => row.op_hub && row.op_hub.airport_code,
      },
      {
        label: 'DISCHARGE PORT',
        value: 'airport_code',
      },
      {
        label: 'MAWB NO',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
      },
      {
        label: 'MAWB DATE',
        value: (row: { mawb: { mawb_date: any } }) => row.mawb && row.mawb.mawb_date,
      },
      {
        label: 'BAG NO',
        value: 'gaylord_no',
      },
      {
        label: 'HAWB NO',
        value: 'tracking_id',
      },
      {
        label: 'SHIPPER NAME',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'SHIPPER ADDRESS',
        value: (row: {
          merchant: {
            street: string;
            city: string;
            state: string;
            postal_code: any;
            country: string;
          };
        }) =>
          row.merchant &&
          `${AesUtils.CrtCounterDecrypt(row.merchant.street)} ${AesUtils.CrtCounterDecrypt(row.merchant.city)} ${AesUtils.CrtCounterDecrypt(row.merchant.state)} ${row.merchant.postal_code} ${AesUtils.CrtCounterDecrypt(row.merchant.country)}`,
      },
      {
        label: 'SHIPPER POST CODE',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'SHIPPER COUNTRY CODE',
        value: (row: { merchant: { country: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.country),
      },
      {
        label: 'SHIPPER PHONE',
        value: (row: { merchant: { phone_number: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.phone_number),
      },
      {
        label: 'CONSIGNEE NAME',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE ADDRESS',
        value: (row: {
          recipient_addressline1: any;
          recipient_addressline2: any;
          recipient_addressline3: any;
          city_suburb: any;
          state: any;
          postcode: any;
        }) =>
          [
            row.recipient_addressline1,
            row.recipient_addressline2,
            row.recipient_addressline3,
            row.city_suburb,
            row.state,
            row.postcode,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'Consignee Zip Code',
        value: 'postcode',
      },
      {
        label: 'CONSIGNEE COUNTRY CODE',
        value: (row: { country: string }) => CountryISOService.getCountryCode2(row.country),
      },
      {
        label: 'CONSIGNEE PHONE',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(' '),
      },
      {
        label: 'WEIGHT (KG)',
        value: (row: {
          actual_weight_unit: any;
          weight_unit: any;
          actual_weight: any;
          weight: any;
        }) => {
          const unit = row.actual_weight_unit ? row.actual_weight_unit : row.weight_unit;
          const weight = row.actual_weight ? row.actual_weight : row.weight;

          return unit === ENUM.measurements.unit.lb
            ? separateThousands(Number(weight) * 0.453_592_37)
            : separateThousands(weight);
        },
      },
      {
        label: 'INCOTERM',
        value: 'incoterm',
      },
      {
        label: 'Currency',
        value: 'merchant_declared_currency',
        default: currency,
      },
      {
        label: 'Total value',
        value: (row: { item: any[] }) => {
          let total = 0;

          if (Array.isArray(row.item)) {
            total = row.item.reduce(
              (acc: number, item: { total_declared_value: any }) =>
                acc + Number(item.total_declared_value),
              0,
            );
          }

          return total;
        },
      },
    ];
    let totalItems: any = [];
    data.forEach((parcel: { item: string | any[] }) => {
      if (Array.isArray(parcel.item)) {
        totalItems.push(parcel.item.length);
      }
    });
    totalItems = Math.max(...totalItems);

    for (let i = 0; i < totalItems; i++) {
      const itemFields: any = [
        {
          label: `item ${i + 1} description`,
          value: (row: { item: { description: any }[] }) => row.item[i] && row.item[i].description,
        },
        {
          label: `item ${i + 1} SKU`,
          value: (row: { item: { SKU: any }[] }) => row.item[i] && row.item[i].SKU,
        },
        {
          label: `item ${i + 1} Org Country`,
          value: (row: { item: { origin_country: any }[] }) =>
            row.item[i] && row.item[i].origin_country,
        },
        {
          label: `item ${i + 1} HS Code`,
          value: (row: { item: { hs_code: any }[] }) => row.item[i] && row.item[i].hs_code,
        },
        {
          label: `item ${i + 1} quantity`,
          value: (row: { item: { quantity: any }[] }) => row.item[i] && row.item[i].quantity,
        },
        {
          label: `item ${i + 1} unit price`,
          value: (row: { item: any }) =>
            row.item[i]
              ? separateThousands(
                  Number(row.item[i].total_declared_value) / Number(row.item[i].quantity),
                )
              : '',
        },
      ];
      fields = fields.concat(itemFields);
    }

    return ExcelUtils.createBuffer('Manifest', data, fields);
  },

  export_custom_broker_manifest_JP_Template(
    data: any[],
    allConversionRates: any[],
    currency: string,
  ) {
    data.forEach((parcel: { merchant_declared_currency: string; item: any[] }) => {
      if (
        parcel.merchant_declared_currency &&
        parcel.merchant_declared_currency.toUpperCase() !== currency
      ) {
        const rate: any = CurrencyConversionService.convertCurrency(
          allConversionRates,
          parcel.merchant_declared_currency.toUpperCase(),
          currency,
        );
        parcel.item.forEach(
          (item: { unit_amount: number; total_declared_value: any; quantity: any }) => {
            item.unit_amount = Math.ceil(
              (Number(item.total_declared_value) / Number(item.quantity)) *
                rate.destination_currency,
            );
          },
        );
      } else {
        parcel.item.forEach(
          (item: { unit_amount: number; total_declared_value: any; quantity: any }) => {
            item.unit_amount = Math.ceil(Number(item.total_declared_value) / Number(item.quantity));
          },
        );
      }
    });
    let fields: any = [
      {
        label: 'VESSEL NAME',
        value: (row: { mawb: { flight_no: any } }) => row.mawb.flight_no,
      },
      {
        label: 'ARRIVAL DATE',
        value: (row: { mawb: { STA: any } }) => row.mawb.STA,
      },
      {
        label: 'LOADING PORT',
        value: (row: { op_hub: { airport_code: any } }) => row.op_hub && row.op_hub.airport_code,
      },
      {
        label: 'DISCHARGE PORT',
        value: 'airport_code',
      },
      {
        label: 'MAWB NO',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
      },
      {
        label: 'MAWB DATE',
        value: (row: { mawb: { mawb_date: any } }) => row.mawb && row.mawb.mawb_date,
      },
      {
        label: 'BAG NO',
        value: 'gaylord_no',
      },
      {
        label: 'HAWB NO',
        value: 'tracking_id',
      },
      {
        label: 'SHIPPER NAME',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'SHIPPER ADDRESS',
        value: (row: {
          merchant: {
            street: string;
            city: string;
            state: string;
            postal_code: any;
            country: string;
          };
        }) =>
          row.merchant &&
          `${AesUtils.CrtCounterDecrypt(row.merchant.street)} ${AesUtils.CrtCounterDecrypt(row.merchant.city)} ${AesUtils.CrtCounterDecrypt(row.merchant.state)} ${row.merchant.postal_code} ${AesUtils.CrtCounterDecrypt(row.merchant.country)}`,
      },
      {
        label: 'SHIPPER POST CODE',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'SHIPPER COUNTRY CODE',
        value: (row: { merchant: { country: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.country),
      },
      {
        label: 'SHIPPER PHONE',
        value: (row: { merchant: { phone_number: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.phone_number),
      },
      {
        label: 'CONSIGNEE NAME',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE ADDRESS',
        value: (row: {
          recipient_addressline1: any;
          recipient_addressline2: any;
          recipient_addressline3: any;
          city_suburb: any;
          state: any;
          postcode: any;
        }) =>
          [
            row.recipient_addressline1,
            row.recipient_addressline2,
            row.recipient_addressline3,
            row.city_suburb,
            row.state,
            row.postcode,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'Consignee Zip Code',
        value: 'postcode',
      },
      {
        label: 'CONSIGNEE COUNTRY CODE',
        value: (row: { country: string }) => CountryISOService.getCountryCode2(row.country),
      },
      {
        label: 'CONSIGNEE PHONE',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE EMAIL',
        value: (row) => CustomManifestUtils.getConsigneeEmail(row),
      },
      {
        label: 'WEIGHT (KG)',
        value: (row: {
          actual_weight_unit: any;
          weight_unit: any;
          actual_weight: any;
          weight: any;
        }) => {
          const unit = row.actual_weight_unit ? row.actual_weight_unit : row.weight_unit;
          const weight = row.actual_weight ? row.actual_weight : row.weight;

          return unit === ENUM.measurements.unit.lb
            ? (Number(weight) * 0.453_592_37).toFixed(2)
            : Number(weight).toFixed(2);
        },
      },
      {
        label: 'INCOTERM',
        value: 'incoterm',
      },
      {
        label: 'Currency',
        default: currency,
      },
      {
        label: 'Total value',
        value: (row: { item: any[] }) => {
          let total = 0;

          if (Array.isArray(row.item)) {
            total = row.item.reduce(
              (acc: number, item: { unit_amount: number; quantity: any }) =>
                acc + item.unit_amount * Number(item.quantity),
              0,
            );
          }

          return total;
        },
      },
    ];
    let totalItems: any = [];
    data.forEach((parcel: { item: string | any[] }) => {
      if (Array.isArray(parcel.item)) {
        totalItems.push(parcel.item.length);
      }
    });
    totalItems = Math.max(...totalItems);

    for (let i = 0; i < totalItems; i++) {
      const itemFields: any = [
        {
          label: `item ${i + 1} description`,
          value: (row: { item: { description: any }[] }) => row.item[i] && row.item[i].description,
        },
        {
          label: `item ${i + 1} SKU`,
          value: (row: { item: { SKU: any }[] }) => row.item[i] && row.item[i].SKU,
        },
        {
          label: `item ${i + 1} Org Country`,
          value: (row: { item: { origin_country: any }[] }) =>
            row.item[i] && row.item[i].origin_country,
        },
        {
          label: `item ${i + 1} HS Code`,
          value: (row: { item: { hs_code: any }[] }) => row.item[i] && row.item[i].hs_code,
        },
        {
          label: `item ${i + 1} quantity`,
          value: (row: { item: { quantity: any }[] }) => row.item[i] && row.item[i].quantity,
        },
        {
          label: `item ${i + 1} unit price`,
          value: (row: { item: { unit_amount: any }[] }) =>
            row.item[i] ? row.item[i].unit_amount : '',
        },
      ];
      fields = fields.concat(itemFields);
    }

    return ExcelUtils.createBuffer('Manifest', data, fields);
  },

  async exportCustomBrokerManifestLMGUSTemplate(
    parcels: ManifestItem[],
    mawb: IMawbCreated,
    containers: Container[],
    sta: string,
  ) {
    // eslint-disable-next-line no-underscore-dangle
    const mawbNo = mawb._partitionKey;
    const operationHub = await OperationHubService.getOperationHubByName(mawb.operation_hub);
    type ParcelForManifest = ManifestItem & { merchant: Merchant };
    const containersMap = new Map(containers.map((c) => [c.id, c]));

    const staInDate = new Date(sta);
    const dateOfArrival = DateUtils.format(
      'MMDDYYYY',
      staInDate.getFullYear(),
      staInDate.getMonth() + 1,
      staInDate.getDate(),
    );

    const allExchangeRate = await CurrencyConversionService.getAllExchRates();
    const largestNumberOfParcelItems = Math.max(...parcels.map((parcel) => parcel.item.length));

    const fields = [
      {
        label: 'Waybill Type',
        default: 'HOUSE',
      },
      {
        label: 'AWB Prefix',
        default: mawbNo.split('-')[0],
      },
      {
        label: 'AWB Number',
        default: mawbNo.split('-')[1],
      },
      {
        label: 'HAWB Number',
        value: 'tracking_id',
      },
      {
        label: 'Airport Of Origin',
        default: operationHub.airport_code,
      },
      {
        label: 'Package Track #',
        value: 'id',
      },
      {
        label: 'Shipper Name',
        value: 'merchant_name',
      },
      {
        label: 'Shipper Street Address',
        value: (parcel: ParcelForManifest) => AesUtils.CrtCounterDecrypt(parcel.merchant.street),
      },
      {
        label: 'Shipper City',
        value: (parcel: ParcelForManifest) => AesUtils.CrtCounterDecrypt(parcel.merchant.city),
      },
      {
        label: 'Shipper State or Province',
        value: (parcel: ParcelForManifest) => AesUtils.CrtCounterDecrypt(parcel.merchant.state),
      },
      {
        label: 'Shipper Postal Code',
        value: (parcel: ParcelForManifest) => parcel.merchant.postal_code,
      },
      {
        label: 'Shipper Country',
        value: (parcel: ParcelForManifest) => AesUtils.CrtCounterDecrypt(parcel.merchant.country),
      },
      {
        label: 'Consignee Name',
        value: (parcel: ParcelForManifest) =>
          [parcel.recipient_first_name, parcel.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'Consignee Street Address',
        value: (parcel: ParcelForManifest) =>
          [
            parcel.recipient_addressline1,
            parcel.recipient_addressline2,
            parcel.recipient_addressline3,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'Consignee City',
        value: 'city_suburb',
      },
      {
        label: 'Consignee State or Province',
        value: 'state',
      },
      {
        label: 'Consignee Postal Code',
        value: 'postcode',
      },
      {
        label: 'Consignee Country',
        value: (parcel: ParcelForManifest) => CountryISOService.getCountryCode2(parcel.country),
      },
      {
        label: 'Cargo Piece Count',
        default: '1',
      },
      {
        label: 'Cargo Weight',
        value: 'weight',
      },
      {
        label: 'Cargo Weight UOM',
        value: (parcel: ParcelForManifest) => (parcel.weight_unit === 'kg' ? 'K' : 'L'),
      },
      {
        label: 'Cargo Description',
        value: (parcel: ParcelForManifest) => parcel.item?.[0].description || '',
      },
      {
        label: 'Marks and Numbers',
        value: (parcel: ParcelForManifest) =>
          containersMap.get(parcel.gaylord_no)?.uld_number || parcel.gaylord_no,
      },
      {
        label: 'FDA Indicator',
        default: 'N',
      },
      {
        label: 'IncludeType86',
        default: 'Y',
      },
      {
        label: 'Entry Type',
        default: '86',
      },
      {
        label: 'T86 Date of Arrival',
        default: dateOfArrival,
      },
      {
        label: 'Mode of Transport',
        default: '40',
      },
      {
        label: 'Bond Type',
        default: '0',
      },
      {
        label: 'Port of Entry',
        value: () => {
          const { point_of_discharge } = mawb;

          if (point_of_discharge === 'JFK') {
            return '4701';
          }

          if (point_of_discharge === 'LAX') {
            return '2720';
          }

          return point_of_discharge;
        },
      },
      ...range(largestNumberOfParcelItems).flatMap((i) => [
        {
          label: `HTS Number ${i + 1}`,
          value: (parcel: ParcelForManifest) => parcel.item[i]?.hs_code.padEnd(10, '0') || '',
        },
        {
          label: `Description ${i + 1}`,
          value: (parcel: ParcelForManifest) => parcel.item[i]?.description || '',
        },
        {
          label: `Line Item Value ${i + 1}`,
          value: (parcel: ParcelForManifest) => {
            const exchangeRate = CurrencyConversionService.convertCurrency(
              allExchangeRate,
              parcel.merchant_declared_currency,
              'USD',
            ).destination_currency;

            return +parcel.item[i].total_declared_value * +exchangeRate || '';
          },
        },
        {
          label: `Country of Origin ${i + 1}`,
          value: (parcel: ParcelForManifest) =>
            CountryISOService.getCountryCode2(parcel.item[i]?.origin_country || ''),
        },
      ]),
    ];

    return ExcelUtils.createBuffer('Manifest', parcels, fields);
  },

  custom_broker_manifest_NZ_template(parcels: any[], allConversionRates: any[], currency: any) {
    const listParcels: any[] = [];
    parcels.forEach((p: { merchant_declared_currency: string; item: any[] }) => {
      let conversionRate = 1;

      if (p.merchant_declared_currency !== currency) {
        conversionRate = CurrencyConversionService.convertCurrency(
          allConversionRates,
          p.merchant_declared_currency,
          currency,
        )?.destination_currency;
      }

      p.item.forEach(
        (it: {
          total_declared_value: string;
          quantity: string;
          description: string;
          subtotal_weight: string;
          hs_code: string;
          converted_total_declared_value: string;
        }) => {
          const itemTotalValue = Number(it.total_declared_value) * conversionRate;
          const temp = {
            ...p,
            item_total_declared_value: it.total_declared_value,
            item_quantity: it.quantity,
            item_description: it.description,
            item_total_weight: it.subtotal_weight,
            item_goods_value: it.converted_total_declared_value || itemTotalValue.toFixed(2),
            hs_code: it.hs_code,
          };
          listParcels.push(temp);
        },
      );
    });
    const fields = [
      {
        label: 'MShipmentConsignorAddressID',
        default: 'SINAIRSIN1',
      },
      {
        label: 'Site ID',
        value: '93474',
        default: '93474',
      },
      {
        label: 'MShipmentConsigneeAddressID',
        default: 'NZPOSAKL',
      },
      {
        label: 'MAWB',
        value: (row: { mawb: { _partitionKey: any } }) => row.mawb?._partitionKey,
        default: '',
      },
      {
        label: 'PortOfOrigin',
        value: (row: { op_hub: { country: string; airport_code: any } }) =>
          row.op_hub &&
          `${CountryISOService.getCountryCode2(row.op_hub.country)}${row.op_hub.airport_code}`,
        default: '',
      },
      {
        label: 'PortOfDestination',
        value: (row: { country: string; destination_group: string }) =>
          row.country &&
          row.destination_group &&
          `${CountryISOService.getCountryCode2(row.country)}${row.destination_group.split('_')[2]}`,
        default: '',
      },
      {
        label: 'DutyStatus',
        value: 'incoterm',
        default: '',
      },
      {
        label: 'ParcelValue',
        value: 'item_total_declared_value',
        default: '',
      },
      {
        label: 'ParcelValueCurrency',
        value: (row: { merchant_declared_currency: any }) => row.merchant_declared_currency,
        default: '',
      },
      {
        label: 'Barcode',
        value: 'tracking_id',
        default: '',
      },
      {
        label: 'TPID',
        value: '91992494',
        default: '91992494',
      },
      {
        label: 'PackQty',
        value: 'item_quantity',
        default: '1',
      },
      {
        label: 'PackQtyUnit',
        value: 'PKG',
        default: 'PKG',
      },
      {
        label: 'GoodsDescription',
        value: 'item_description',
        default: '',
      },
      {
        label: 'Weight',
        value: 'item_total_weight',
      },
      {
        label: 'WeightUnit',
        value: (row: { actual_weight_unit: any; weight_unit: any }) => {
          return (row.actual_weight_unit || row.weight_unit || '').toUpperCase();
        },
        default: '',
      },
      {
        label: 'Volumetric Weight',
        value: 'VolumetricWeigh',
        default: '',
      },
      {
        label: 'VolumetricWeightUnit',
        value: (row: { weight_unit: string }) => (row.weight_unit || '').toUpperCase(),
        default: '',
      },
      {
        label: 'DimensionLength',
        value: 'length',
        default: '',
      },
      {
        label: 'DimensionHeight',
        value: 'height',
        default: '',
      },
      {
        label: 'DimensionWidth',
        value: 'width',
        default: '',
      },
      {
        label: 'DimensionUnit',
        value: 'dimensions_unit',
        default: '',
      },
      {
        label: 'GoodsValue',
        value: 'item_goods_value',
        default: '',
      },
      {
        label: 'GoodsCurrency',
        default: currency,
      },
      {
        label: 'ShippingCost',
        value: '',
        default: '',
      },
      {
        label: 'ShippingCostCurrency',
        value: '',
      },
      {
        label: 'CountryOfOrigin',
        value: (row: { op_hub: { country: string } }) =>
          row.op_hub && CountryISOService.getCountryCode2(row.op_hub.country),
        default: '',
      },
      {
        label: 'NZ Full Tariff Code',
        value: (row) => row.hs_code?.slice(0, 6),
        default: '',
      },
      {
        label: 'ProductCode',
        value: 'service_code',
        default: '',
      },
      {
        label: 'Ancillary Code (SubProduct)',
        value: '',
        default: '',
      },
      {
        label: 'ConsigneeName',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
        default: '',
      },
      {
        label: 'ConsigneeAddress1',
        value: (row: { recipient_addressline1: any; recipient_addressline2: any }) =>
          [row.recipient_addressline1, row.recipient_addressline2].filter(Boolean).join(' '),
        default: '',
      },
      {
        label: 'ConsigneeAddress2',
        value: 'recipient_addressline3',
        default: '',
      },
      {
        label: 'ConsigneeCity',
        value: 'city_suburb',
        default: '',
      },
      {
        label: 'ConsigneeState',
        value: 'state',
        default: '',
      },
      {
        label: 'ConsigneeCountry',
        value: '',
        default: 'NZ',
      },
      {
        label: 'ConsigneePostCode',
        value: (row: { postcode: string }) =>
          row.postcode.toString().length >= 4 ? row.postcode : `0${row.postcode}`,
        default: '',
      },
      {
        label: 'ConsigneePhone',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(''),
        default: '',
      },
      {
        label: 'ConsigneeEmail',
        value: 'email',
        default: '',
      },
      {
        label: 'ConsigneeReference',
        value: '',
        default: '',
      },
      {
        label: 'ConsignorName',
        value: 'merchant_name',
        default: '',
      },
      {
        label: 'ConsignorAddress1',
        value: (row: { merchant: { street: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.street),
        default: '',
      },
      {
        label: 'ConsignorAddress2',
        value: '',
        default: '',
      },
      {
        label: 'ConsignorCity',
        value: (row: { merchant: { city: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.city),
        default: '',
      },
      {
        label: 'ConsignorState',
        value: (row: { merchant: { state: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.state),
        default: '',
      },
      {
        label: 'ConsignorCountry',
        value: (row: { merchant: { country: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.country),
        default: '',
      },
      {
        label: 'ConsignorPostCode',
        value: (row: { merchant: { postal_code: any } }) => row.merchant?.postal_code,
        default: '',
      },
      {
        label: 'ConsignorPhone',
        value: '',
        default: '',
      },
      {
        label: 'ConsignorEmail',
        value: '',
        default: '',
      },
      {
        label: 'ReturnAddress1',
        value: '',
        default: '',
      },
      {
        label: 'ReturnAddress2',
        value: '',
        default: '',
      },
      {
        label: 'ReturnCity',
        value: '',
        default: '',
      },
      {
        label: 'ReturnState',
        value: '',
        default: '',
      },
      {
        label: 'ReturnCountry',
        value: '',
        default: '',
      },
      {
        label: 'ReturnPostCode',
        value: '',
        default: '',
      },
      {
        label: 'GST Indicator',
        value: '',
        default: 'N',
      },
      {
        label: 'GST Number',
        value: '',
        default: '',
      },
    ];

    return ExcelUtils.createBuffer('Manifest', listParcels, fields);
  },

  custom_broker_manifest_kerry_TH(parcels: any) {
    const listParcels: any = [];
    parcels.forEach((p: any) => {
      const len = p.item.length;
      p.item.forEach(
        (it: {
          description: any;
          quantity: number;
          hs_code: any;
          converted_total_declared_value: number;
        }) => {
          const temp = {
            ...p,
            more_than_1_item: len > 1,
            item_description: it.description,
            item_quantity: it.quantity,
            item_hscode: it.hs_code,
            item_unit_price: it.converted_total_declared_value / it.quantity,
            item_total_declared_value: it.converted_total_declared_value,
          };
          listParcels.push(temp);
        },
      );
    });

    const fields: any = [
      {
        label: 'Waybill Number',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
      },
      {
        label: 'Shipment Reference No.',
        value: (row: { tracking_no: any }) => row.tracking_no,
      },
      {
        label: 'Shipper Company Name',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'Shipper Contact Name',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'Shipper Tel',
        value: (row: { merchant: { phone_number: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.phone_number),
      },
      {
        label: 'Shipper Address',
        value: (row: {
          merchant: {
            street: string;
            city: string;
            state: string;
            postal_code: any;
            country: string;
          };
        }) =>
          row.merchant &&
          `${AesUtils.CrtCounterDecrypt(row.merchant.street)} ${AesUtils.CrtCounterDecrypt(row.merchant.city)} ${AesUtils.CrtCounterDecrypt(row.merchant.state)} ${row.merchant.postal_code} ${AesUtils.CrtCounterDecrypt(row.merchant.country)}`,
      },
      {
        label: 'Shipper Country',
        value: (row: { merchant: { country: string } }) =>
          row.merchant &&
          CountryISOService.getCountryFullName(AesUtils.CrtCounterDecrypt(row.merchant.country)),
      },
      {
        label: 'Shipper Postal Code',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'Consignee Company Name',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'Consignee Contact Name',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'Consignee Tel',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(' '),
      },
      {
        label: 'Consignee Address',
        value: (row: {
          recipient_addressline1: any;
          recipient_addressline2: any;
          recipient_addressline3: any;
          city_suburb: any;
          state: any;
          postcode: any;
        }) =>
          [
            row.recipient_addressline1,
            row.recipient_addressline2,
            row.recipient_addressline3,
            row.city_suburb,
            row.state,
            row.postcode,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'Consignee Country',
        default: 'Thailand',
      },
      {
        label: 'Consignee Postal Code',
        value: (row: { postcode: any }) => `${row.postcode}`,
      },
      {
        label: 'Currency',
        default: 'THB',
      },
      {
        label: 'Content',
        value: 'item_description',
      },
      {
        label: 'Quantity',
        value: 'item_quantity',
      },
      {
        label: 'Unit Price',
        value: 'item_unit_price',
      },
      {
        label: 'Total Value',
        value: 'item_total_declared_value',
      },
      {
        label: 'More than 1 items',
        value: 'more_than_1_item',
      },
      {
        label: 'Shipment Type',
        default: 'Parcel',
      },
      {
        label: 'Total Package',
        default: '1', // PLS-6555
      },
      {
        label: 'Total Actual Weight',
        value: (row: {
          actual_weight_unit: any;
          weight_unit: any;
          actual_weight: any;
          weight: any;
        }) => {
          const unit = row.actual_weight_unit ? row.actual_weight_unit : row.weight_unit;
          const weight = row.actual_weight ? row.actual_weight : row.weight;

          return unit === ENUM.measurements.unit.lb
            ? separateThousands(UnitConverterUtils.lbtokg(weight))
            : separateThousands(weight);
        },
      },
      {
        label: 'Origin',
        value: 'origin',
      },
      {
        label: 'Destination',
        default: 'BKK', // PLS-6555
      },
      {
        label: 'Creation Date',
        value: 'order_date',
      },
      {
        label: 'Processing Shipment Date',
        default: '',
      },
      {
        label: 'HSCODE',
        value: 'item_hscode',
      },
      {
        label: 'Category',
        default: '',
      },
      {
        label: 'BoxID',
        value: 'gaylord_no',
      },
      {
        label: 'Package Number',
        default: '',
      },
      {
        label: 'Payment Method',
        default: 'Prepaid',
      },
      {
        label: 'COD Value',
        default: '',
      },
      {
        label: 'Product Category ID',
        default: '',
      },
      {
        label: 'Client',
        default: '',
      },
      {
        label: 'Centre',
        default: '',
      },
      {
        label: 'Sort Code',
        default: '',
      },
    ];

    return ExcelUtils.createBuffer('Manifest', listParcels, fields);
  },

  export_custom_broker_manifest_GB_Template(parcels: any) {
    const listParcels: any = [];
    parcels.forEach((p: { item: any[]; merchant: any }) => {
      p.item.forEach(
        (it: {
          description: any;
          quantity: number;
          hs_code: any;
          total_declared_value: number;
        }) => {
          const temp = {
            ...p,
            merchant: AesUtils.decryptFields(
              ['city', 'state', 'country', 'merchant_name', 'street'],
              p.merchant,
            ),
            item_description: it.description,
            item_quantity: it.quantity,
            item_hscode: it.hs_code,
            item_value: it.total_declared_value / it.quantity,
            item_total_declared_value: it.total_declared_value,
          };
          listParcels.push(temp);
        },
      );
    });

    const fields: any = [
      {
        label: 'Tracking Number',
        value: 'tracking_id',
      },
      {
        label: 'reference',
        value: '',
      },
      {
        label: 'Internal Account Number',
        value: '',
      },
      {
        label: 'shipper turn no',
        value: (row: { merchant: { vat_no: any } }) =>
          row.merchant && row.merchant.vat_no ? row.merchant.vat_no : '',
      },
      {
        label: 'shipper name',
        value: (row: { merchant: { merchant_name: any } }) =>
          row.merchant && row.merchant.merchant_name,
      },
      {
        label: 'Ship Add 1',
        value: (row: { merchant: { street: any } }) => row.merchant && row.merchant.street,
      },
      {
        label: 'Ship Add 2',
        value: '',
      },
      {
        label: 'Ship Add 3',
        value: '',
      },
      {
        label: 'Ship City',
        value: (row: { merchant: { city: any } }) => row.merchant && row.merchant.city,
      },
      {
        label: 'Ship State',
        value: (row: { merchant: { state: any; city: any } }) =>
          row.merchant && row.merchant.state ? row.merchant.state : row.merchant.city,
      },
      {
        label: 'Ship Zip',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'Ship Country Code',
        value: (row: { merchant: { country: string } }) =>
          row.merchant && CountryISOService.getCountryCode2(row.merchant.country),
      },
      {
        label: 'consignee',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'address1',
        value: 'recipient_addressline1',
      },
      {
        label: 'address2',
        value: 'recipient_addressline2',
      },
      {
        label: 'address3',
        value: 'recipient_addressline3',
      },
      {
        label: 'city',
        value: 'city_suburb',
      },
      {
        label: 'state',
        value: (row: { state: any; city: any }) => (row.state ? row.state : row.city),
      },
      {
        label: 'zip',
        value: 'postcode',
      },
      {
        label: 'country code',
        value: (row: { country: string }) => CountryISOService.getCountryCode2(row.country),
      },
      {
        label: 'email',
        value: 'email',
      },
      {
        label: 'phone',
        value: 'phone',
      },
      {
        label: 'Pieces',
        default: '1',
      },
      {
        label: 'total weight',
        value: 'weight',
      },
      {
        label: 'Weight UOM',
        value: 'weight_unit',
      },
      {
        label: 'item value',
        value: 'item_value',
      },
      {
        label: 'Currency',
        value: 'merchant_declared_currency',
      },
      {
        label: 'Incoterms',
        value: 'incoterm',
      },
      {
        label: 'item description',
        value: 'item_description',
      },
      {
        label: 'Item HS Code',
        value: 'item_hscode',
      },
      {
        label: 'item quantity',
        value: 'item_quantity',
      },
      {
        label: 'total value',
        value: 'item_total_declared_value',
      },
      {
        label: 'vendor name',
        default: 'EVRi',
      },
    ];

    return ExcelUtils.createBuffer('Manifest', listParcels, fields);
  },

  export_custom_broker_manifest_HK_Template(parcels: any) {
    const listParcels: any = [];
    parcels.forEach(
      (p: {
        actual_weight_unit: any;
        weight_unit: any;
        actual_weight: any;
        weight: any;
        convert_actual_weight: any;
        item: any[];
      }) => {
        const unit = p.actual_weight_unit ? p.actual_weight_unit : p.weight_unit;
        const weight = p.actual_weight ? p.actual_weight : p.weight;
        p.convert_actual_weight =
          unit === ENUM.measurements.unit.lb
            ? separateThousands(UnitConverterUtils.lbtokg(weight))
            : separateThousands(weight);
        p.item.forEach(
          (it: {
            description: any;
            SKU: any;
            quantity: number;
            hs_code: any;
            converted_total_declared_value: number;
          }) => {
            const temp = {
              ...p,
              item_description: it.description,
              item_SKU: it.SKU,
              item_quantity: it.quantity,
              item_hscode: it.hs_code,
              item_unit_price: it.converted_total_declared_value / it.quantity,
              item_total_declared_value: it.converted_total_declared_value,
            };
            listParcels.push(temp);
          },
        );
      },
    );

    const fields: any = [
      {
        label: 'Client Code',
        default: '',
      },
      {
        label: 'Client Name',
        default: 'Parxl',
      },
      {
        label: 'Channel Code',
        default: '',
      },
      {
        label: 'Shipment Reference Number',
        value: 'tracking_no',
      },
      {
        label: 'Tracking Number',
        value: 'tracking_id',
      },
      {
        label: 'MAWB#',
        value: (row: { mawb: { mawb_no: any } }) => row.mawb && row.mawb.mawb_no,
        default: '',
      },
      {
        label: 'Handover Number',
        default: '',
      },
      {
        label: 'Box Number',
        value: 'gaylord_no',
      },
      {
        label: 'Sale Platform Name',
        default: '',
      },
      {
        label: 'Sender Name',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'Sender Company',
        value: (row: { merchant: { merchant_name: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
      },
      {
        label: 'Sender Address',
        value: (row: { merchant: { street: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.street),
      },
      {
        label: 'Sender District',
        default: '',
      },
      {
        label: 'Sender City',
        value: (row: { merchant: { city: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.city),
      },
      {
        label: 'Sender Province',
        value: (row: { merchant: { state: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.state),
      },
      {
        label: 'Sender Country Code',
        value: (row: { merchant: { country: string } }) =>
          row.merchant &&
          CountryISOService.getCountryCode2(AesUtils.CrtCounterDecrypt(row.merchant.country)),
      },
      {
        label: 'Sender Post Code',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'Sender Phone',
        value: (row: { merchant: { phone_number: string } }) =>
          row.merchant && AesUtils.CrtCounterDecrypt(row.merchant.phone_number),
      },
      {
        label: 'Sender Email',
        default: '',
      },
      {
        label: 'Sender ID Number',
        default: '',
      },
      {
        label: 'Receiver Name',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'Receiver Company',
        default: '',
      },
      {
        label: 'Receiver Address',
        value: (row: {
          recipient_addressline1: any;
          recipient_addressline2: any;
          recipient_addressline3: any;
        }) =>
          [row.recipient_addressline1, row.recipient_addressline2, row.recipient_addressline3]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'Receiver District',
        default: '',
      },
      {
        label: 'Receiver City',
        value: 'city_suburb',
      },
      {
        label: 'Receiver Province',
        value: 'state',
        default: '',
      },
      {
        label: 'Receiver Country Code',
        default: 'HK',
      },
      {
        label: 'Receiver Post Code',
        default: '',
      },
      {
        label: 'Receiver Phone',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(''),
      },
      {
        label: 'Receiver Email',
        value: 'email',
        default: '',
      },
      {
        label: 'Receiver ID Number',
        default: '',
      },
      {
        label: 'Declared Value',
        value: 'converted_merchant_declared_value',
        default: '0',
      },
      {
        label: 'Declared Value Currency',
        default: 'HKD',
      },
      {
        label: 'COD Value',
        default: '',
      },
      {
        label: 'COD Value Currency',
        default: '',
      },
      {
        label: 'Number Of Package',
        default: '1',
      },
      {
        label: 'Package Height',
        value: 'height',
      },
      {
        label: 'Package Length',
        value: 'length',
      },
      {
        label: 'Package Width',
        value: 'width',
      },
      {
        label: 'Package Estimate Weight',
        value: 'convert_actual_weight',
      },
      {
        label: 'Package Actual Weight',
        value: 'convert_actual_weight',
      },
      {
        label: 'Shipment Term',
        value: 'incoterm',
      },
      {
        label: 'Package Payment Method',
        default: 'PP',
      },
      {
        label: 'Shipment Type',
        default: '',
      },
      {
        label: 'Product SKU',
        value: 'item_SKU',
      },
      {
        label: 'Product Description In English',
        value: 'item_description',
      },
      {
        label: 'Product Description In Origin Language',
        value: 'item_description',
      },
      {
        label: 'Product Description In Destination Language',
        value: 'item_description',
      },
      {
        label: 'Product Category',
        default: '',
      },
      {
        label: 'Product Unit Price',
        value: 'item_unit_price',
      },
      {
        label: 'Product Unit Price Currency',
        default: 'HKD',
      },
      {
        label: 'Product Quantity',
        value: 'item_quantity',
      },
      {
        label: 'Product HS Code',
        value: 'item_hscode',
      },
      {
        label: 'Product Brand',
        default: '',
      },
      {
        label: 'Product Origin Of The Product',
        default: '',
        value: (row: { origin_country: any }) => row.origin_country || '',
      },
      {
        label: 'Remark',
        default: '',
      },
      {
        label: 'Business Type',
        default: '',
      },
      {
        label: 'Ioss Number',
        default: '',
      },
    ];

    return ExcelUtils.createBuffer('Manifest', listParcels, fields);
  },

  export_custom_broker_manifest_TW_Template(parcels: any) {
    const listParcels: any = [];
    parcels.forEach((parcel: { item: any[]; merchant: any }) => {
      parcel.item.forEach(
        (item: {
          description: any;
          quantity: number;
          hs_code: any;
          total_declared_value: number;
        }) => {
          const temp = {
            ...parcel,
            merchant: AesUtils.decryptFields(
              ['city', 'state', 'country', 'merchant_name', 'street'],
              parcel.merchant,
            ),
            item_description: item.description,
            item_quantity: item.quantity,
            item_hscode: item.hs_code,
            item_value: item.total_declared_value / item.quantity,
            item_total_declared_value: item.total_declared_value,
          };
          listParcels.push(temp);
        },
      );
    });
    const fields: any = [
      {
        label: 'Tracking Number',
        value: 'tracking_id',
      },
      {
        label: 'reference',
        default: '',
      },
      {
        label: 'Taiwan Import Number',
        value: (row: { taiwan_import_number: any }) => row.taiwan_import_number || '',
      },
      {
        label: 'Internal Account Number',
        default: '',
      },
      {
        label: 'shipper turn no',
        value: (row: { merchant: { vat_no: any } }) =>
          row.merchant && row.merchant.vat_no ? row.merchant.vat_no : '',
      },
      {
        label: 'shipper name',
        value: (row: { merchant: { merchant_name: any } }) =>
          row.merchant && row.merchant.merchant_name,
      },
      {
        label: 'Ship Add 1',
        value: (row: { merchant: { street: any } }) => row.merchant && row.merchant.street,
      },
      {
        label: 'Ship Add 2',
        value: '',
      },
      {
        label: 'Ship Add 3',
        value: '',
      },
      {
        label: 'Ship City',
        value: (row: { merchant: { city: any } }) => row.merchant && row.merchant.city,
      },
      {
        label: 'Ship State',
        value: (row: { merchant: { state: any; city: any } }) =>
          row.merchant && row.merchant.state ? row.merchant.state : row.merchant.city,
      },
      {
        label: 'Ship Zip',
        value: (row: { merchant: { postal_code: any } }) =>
          row.merchant && row.merchant.postal_code,
      },
      {
        label: 'Ship Country Code',
        value: (row: { merchant: { country: string } }) =>
          row.merchant && CountryISOService.getCountryCode2(row.merchant.country),
      },
      {
        label: 'consignee',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
      },
      {
        label: 'address1',
        value: 'recipient_addressline1',
      },
      {
        label: 'address2',
        value: 'recipient_addressline2',
      },
      {
        label: 'address3',
        value: 'recipient_addressline3',
      },
      {
        label: 'city',
        value: 'city_suburb',
      },
      {
        label: 'state',
        value: (row: { state: any; city: any }) => (row.state ? row.state : row.city),
      },
      {
        label: 'zip',
        value: 'postcode',
      },
      {
        label: 'country code',
        value: (row: { country: string }) => CountryISOService.getCountryCode2(row.country),
      },
      {
        label: 'email',
        value: 'email',
      },
      {
        label: 'phone',
        value: 'phone',
      },
      {
        label: 'Pieces',
        default: '1',
      },
      {
        label: 'total weight',
        value: 'weight',
      },
      {
        label: 'Weight UOM',
        value: 'weight_unit',
      },
      {
        label: 'item value',
        value: 'item_value',
      },
      {
        label: 'Currency',
        value: 'merchant_declared_currency',
      },
      {
        label: 'Incoterms',
        value: 'incoterm',
      },
      {
        label: 'item description',
        value: 'item_description',
      },
      {
        label: 'Item HS Code',
        value: 'item_hscode',
      },
      {
        label: 'item quantity',
        value: 'item_quantity',
      },
      {
        label: 'total value',
        value: 'item_total_declared_value',
      },
      {
        label: 'vendor name',
        default: 'Pickupp',
      },
    ];

    return ExcelUtils.createBuffer('Manifest', listParcels, fields);
  },

  /**
   PLS-5392: Convert parcel item for Janio Manifest
   @param item item to be converted
   @param rate exchangeRate
   * */
  convertParcelItemForJanio(item: any, rate: any, totalOrderValue: any) {
    const item_price = Number.parseFloat(
      separateThousands(Number(item.total_declared_value) / Number(item.quantity)),
    );

    return {
      item_description: item.description,
      item_quantity: item.quantity,
      item_price,
      total_order_value: Number.parseFloat(separateThousands(totalOrderValue)),
      converted_item_price: Number.parseFloat(separateThousands(item_price * rate)),
      converted_total_order_value:
        Number.parseFloat(separateThousands(totalOrderValue)) * Number.parseFloat(rate),
    };
  },

  export_custom_broker_manifest_ID_template(
    parcels: any,
    allConversionRates: any,
    toCurrency: any,
    opHub: any,
    customBrokerName: any,
  ) {
    const data: any = [];
    const header = [
      ['Delivery Note', '', '', 'Delivery Date:'],
      ['Warehouse:', opHub.address_line_1, '', 'Vehicle No:'],
      ['Ship From:', opHub.country, '', 'Ship To:', customBrokerName],
      [],
    ];

    parcels.forEach(
      (parcel: { merchant_declared_currency: string; item: any[] }, index: number) => {
        const rate: any = CurrencyConversionService.convertCurrency(
          allConversionRates,
          parcel.merchant_declared_currency.toUpperCase(),
          toCurrency.toUpperCase(),
        );
        const totalOrderValue = parcel.item
          .map((item: { total_declared_value: string }) =>
            Number.parseFloat(item.total_declared_value),
          )
          .reduce((prevValue: any, currentValue: any) => prevValue + currentValue);

        if (parcel.item.length === 1) {
          const row = {
            no: index + 1,
            ...parcel,
            ...this.convertParcelItemForJanio(
              parcel.item[0],
              rate.destination_currency,
              totalOrderValue,
            ),
          };
          data.push(row);
        } else {
          for (let i = 0; i < parcel.item.length; i++) {
            let row;

            row =
              i === 0
                ? {
                    no: index + 1,
                    ...parcel,
                    ...this.convertParcelItemForJanio(
                      parcel.item[i],
                      rate.destination_currency,
                      totalOrderValue,
                    ),
                  }
                : {
                    merchant_declared_currency: parcel.merchant_declared_currency,
                    ...this.convertParcelItemForJanio(
                      parcel.item[i],
                      rate.destination_currency,
                      totalOrderValue,
                    ),
                  };

            data.push(row);
          }
        }
      },
    );

    const fields: any = [
      {
        label: 'No',
        value: 'no',
        default: '',
      },
      {
        label: 'Tracking Number',
        value: 'tracking_id',
        default: '',
      },
      {
        label: 'Consignee Name',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
        default: '',
      },
      {
        label: 'Consignee Address',
        value: (row: {
          recipient_addressline1: any;
          recipient_addressline2: any;
          recipient_addressline3: any;
        }) =>
          [row.recipient_addressline1, row.recipient_addressline2, row.recipient_addressline3]
            .filter(Boolean)
            .join(' '),
        default: '',
      },
      {
        label: 'Consignee Postal Code',
        value: 'postcode',
        default: '',
      },
      {
        label: 'Consignee Country',
        value: 'country',
        default: '',
      },
      {
        label: 'Consignee Tel',
        value: (row: { phone_country_code: any; phone: any }) =>
          [row.phone_country_code, row.phone].filter(Boolean).join(''),
        default: '',
      },
      {
        label: 'Consignee Email',
        value: 'email',
        default: '',
      },
      {
        label: 'Description',
        default: '',
      },
      {
        label: 'Insured Value',
        default: '',
      },
      {
        label: 'Is COD?',
        value: (row: { no: any }) => (row.no ? 'No' : ''),
        default: '',
      },
      {
        label: 'Is DDU?',
        value: (row: { incoterm: string }) =>
          (row.incoterm || '') && (row.incoterm === 'DDU' ? 'Yes' : 'No'),
        default: '',
      },
      {
        label: 'Cargo Number',
        default: '',
      },
      {
        label: 'Length (cm)',
        value: (row: { dimensions_unit: string; length: any }) =>
          (row.dimensions_unit || '') &&
          (row.dimensions_unit.toLowerCase() === ENUM.measurements.unit.cm
            ? row.length
            : Number.parseFloat(separateThousands(Number(row.length) * 2.54))),
        default: '',
      },
      {
        label: 'Width (cm)',
        value: (row: { dimensions_unit: string; width: any; length: any }) =>
          (row.dimensions_unit || '') &&
          (row.dimensions_unit.toLowerCase() === ENUM.measurements.unit.cm
            ? row.width
            : Number.parseFloat(separateThousands(Number(row.length) * 2.54))),
        default: '',
      },
      {
        label: 'Height (cm)',
        value: (row: { dimensions_unit: string; height: any }) =>
          (row.dimensions_unit || '') &&
          (row.dimensions_unit.toLowerCase() === ENUM.measurements.unit.cm
            ? row.height
            : Number.parseFloat(separateThousands(Number(row.height) * 2.54))),
        default: '',
      },
      {
        label: 'Weight (kg)',
        value: (row: { dimensions_unit: any; weight_unit: string; weight: any }) =>
          (row.dimensions_unit || '') &&
          (row.weight_unit.toLowerCase() === ENUM.measurements.unit.kg
            ? row.weight
            : Number.parseFloat(separateThousands(Number(row.weight) * 0.453_592_37))),
        default: '',
      },
      {
        label: 'Item Description',
        value: 'item_description',
        default: '',
      },
      {
        label: 'Item Qty',
        value: 'item_quantity',
        default: '',
      },
      {
        label: 'Item Price',
        value: 'item_price',
        default: '',
      },
      {
        label: 'Total Order Value',
        value: 'total_order_value',
        default: '',
      },
      {
        label: 'Currency',
        value: 'merchant_declared_currency',
        default: '',
      },
      {
        label: 'Item Price (Consignee Country Currency)',
        value: 'converted_item_price',
        default: '',
      },
      {
        label: 'Total Order Value (Consignee Country Currency)',
        value: 'converted_total_order_value',
        default: '',
      },
      {
        label: 'Consignee Country Currency',
        default: 'IDR',
      },
      {
        label: 'Identification Document Name',
        value: (row: { recipient_document_type: string }) =>
          (row.recipient_document_type || '') && row.recipient_document_type.toUpperCase(),
        default: '',
      },
      {
        label: 'Consignee Identification Number',
        value: (row: { recipient_id: string }) =>
          (row.recipient_id || '') && row.recipient_id.replaceAll(/[^\dA-Za-z]/g, ''),
        default: '',
      },
      {
        label: 'Shipper Name',
        value: (row: { merchant: { merchant_name: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
        default: '',
      },
      {
        label: 'Shipper Address',
        value: (row: { merchant: { street: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.street),
        default: '',
      },
      {
        label: 'Shipper Country',
        value: (row: { merchant: { country: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.country),
        default: '',
      },
      {
        label: 'Shipper Telephone',
        value: (row: { merchant: { phone_number: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.phone_number),
        default: '',
      },
      {
        label: 'Shipper Email',
        value: (row: { merchant: { email: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.email),
        default: '',
      },
    ];

    return ExcelUtils.createBuffer('Manifest', data, fields, header);
  },

  export_custom_broker_manifest_CPL_AU_template(parcels: any, opHub: any) {
    const fields: any = [
      {
        label: 'Invoice_number',
        value: (row: { tracking_id: string }) => `${row.tracking_id}01`,
        default: '',
      },
      {
        label: 'Flight_id',
        default: '',
      },
      {
        label: 'Sort_code',
        default: '',
      },
      {
        label: 'Value',
        value: (row: { item: any[] }) =>
          row.item.reduce(
            (prev: number, curr: { total_declared_value: any }) =>
              prev + Number(curr.total_declared_value || 0),
            0,
          ),
        default: '',
      },
      {
        label: 'Shipped Quantity',
        default: '1',
      },
      {
        label: 'Shipped Date',
        default: '',
      },
      {
        label: 'Delname',
        value: (row: { recipient_first_name: any; recipient_last_name: any }) =>
          [row.recipient_first_name, row.recipient_last_name].filter(Boolean).join(' '),
        default: '',
      },
      {
        label: 'Deladdr1',
        value: 'recipient_addressline1',
        default: '',
      },
      {
        label: 'Deladdr2',
        value: (row: { recipient_addressline2: any; recipient_addressline3: any }) =>
          [row.recipient_addressline2, row.recipient_addressline3].filter(Boolean).join(' '),
        default: '',
      },
      {
        label: 'Deladdr3',
        value: 'city_suburb',
        default: '',
      },
      {
        label: 'Deladdr4',
        value: 'state',
        default: '',
      },
      {
        label: 'Postcode',
        value: 'postcode',
        default: '',
      },
      {
        label: 'Product Description',
        value: (row: { item: any[] }) =>
          row.item
            .map((it: { description: any }) => it.description)
            .filter(Boolean)
            .join(' ,'),
        default: '',
      },
      {
        label: 'Origin',
        default: opHub.country_iso2,
      },
      {
        label: 'value_flag',
        default: '',
      },
      {
        label: 'tariff_id',
        default: '',
      },
      {
        label: 'duty_per_item',
        default: '',
      },
      {
        label: 'Weight',
        value: 'weight',
        default: '',
      },
      {
        label: 'ShipperName1',
        value: (row: { merchant: { merchant_name: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.merchant_name),
        default: '',
      },
      {
        label: 'ShipperName2',
        default: '',
      },
      {
        label: 'ShipperAddr1',
        value: (row: { merchant: { street: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.street),
        default: '',
      },
      {
        label: 'ShipperAddr2',
        default: '',
      },
      {
        label: 'ShipperAddr3',
        default: '',
      },
      {
        label: 'Shipper City',
        value: (row: { merchant: { city: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.city),
        default: '',
      },
      {
        label: 'Shipper State',
        value: (row: { merchant: { state: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.state),
        default: '',
      },
      {
        label: 'Shipper Postcode',
        value: (row: { merchant: { postal_code: any } }) =>
          (row.merchant || '') && row.merchant.postal_code,
        default: '',
      },
      {
        label: 'Shipper country',
        value: (row: { merchant: { country: string } }) =>
          (row.merchant || '') && AesUtils.CrtCounterDecrypt(row.merchant.country),
        default: '',
      },
    ];

    return ExcelUtils.createBuffer('Manifest', parcels, fields);
  },

  getFieldsForMY(
    flightNo: string,
    STA: string,
    STD: string,
    currency: string,
    allConversionRates,
    customBroker,
    mawbInfo,
    cat1MerchantList,
  ): IField[] {
    if (customBroker.custom_broker_code === CONSTANTS.CUSTOM_BROKER_CODES.CB_NJV_KUL) {
      return [
        {
          label: 'Client Name',
          default: 'OTHERS',
        },
        {
          label: 'MAWB No.',
          value: (row: { parcel: { mawb: IMawbCreated } }) => row.parcel.mawb?._partitionKey,
        },
        {
          label: 'Flight No.',
          value: () => flightNo,
        },
        {
          label: 'Origin Port',
          value: (row: { parcel: ManifestItem & { op_hub: IOperationHub } }) =>
            `${row.parcel.origin_country}${row.parcel.op_hub?.airport_code}`,
        },
        {
          label: 'Destination Port',
          value: (row: { parcel: ManifestItem }) =>
            `${CountryISOService.getCountryCode2(row.parcel.country)}${row.parcel.destination_group.split('_')[2]}`,
        },
        {
          label: 'STA',
          value: () => format(parseISO(STA), 'dd/MM/yyy pp'),
        },
        {
          label: 'STD',
          value: () => format(parseISO(STD), 'dd/MM/yyy pp'),
        },
        {
          label: 'Parcel No',
          value: (row: { parcel: ManifestItem }) => row.parcel.tracking_id,
        },
        {
          label: 'Parcel Bag ID',
          value: (row: { parcel: ManifestItem }) => row.parcel.gaylord_no,
        },
        {
          label: 'Parcel Origin',
          value: (row: { parcel: ManifestItem }) => row.parcel.origin_country,
        },
        {
          label: 'Parcel Destination',
          value: (row: { parcel: ManifestItem }) =>
            CountryISOService.getCountryCode2(row.parcel.country),
        },
        {
          label: 'Parcel Currency',
          default: 'MYR',
        },
        {
          label: 'Parcel Total Freight Charges',
        },
        {
          label: 'Parcel Total Insurance Charges',
        },
        {
          label: 'Parcel Total Gross Weight',
          value: (row: { parcel: ManifestItem }) => {
            const unit = row.parcel.weight_unit;
            const weight = Number.parseFloat(row.parcel.weight);

            return unit === ENUM.measurements.unit.lb
              ? separateThousands(Number(weight) * 0.453_592_37)
              : separateThousands(weight);
          },
        },
        {
          label: 'LVG Registration No.',
        },
        {
          label: 'Consignor Name',
          value: (row: { parcel: ManifestItem & { merchant: IMerchant } }) =>
            AesUtils.CrtCounterDecrypt(row.parcel.merchant?.merchant_name),
        },
        {
          label: 'Consignor Address 1',
          value: (row: { parcel: ManifestItem & { merchant: IMerchant } }) =>
            AesUtils.CrtCounterDecrypt(row.parcel.merchant?.street),
        },
        {
          label: 'Consignor Address 2',
        },
        {
          label: 'Consignor Postcode',
          value: (row: { parcel: ManifestItem & { merchant: IMerchant } }) =>
            row.parcel.merchant?.postal_code,
        },
        {
          label: 'Consignor City',
          value: (row: { parcel: ManifestItem & { merchant: IMerchant } }) =>
            AesUtils.CrtCounterDecrypt(row.parcel.merchant?.city),
        },
        {
          label: 'Consignor State',
          value: (row: { parcel: ManifestItem & { merchant: IMerchant } }) =>
            AesUtils.CrtCounterDecrypt(row.parcel.merchant?.state),
        },
        {
          label: 'Consignor Country',
          value: (row: { parcel: ManifestItem & { merchant: IMerchant } }) =>
            AesUtils.CrtCounterDecrypt(row.parcel.merchant?.country),
        },
        {
          label: 'Consignee Name',
          value: (row: { parcel: ManifestItem }) =>
            [row.parcel.recipient_first_name, row.parcel.recipient_last_name]
              .filter(Boolean)
              .join(' '),
        },
        {
          label: 'Consignee Address 1',
          value: (row: { parcel: ManifestItem }) =>
            [
              row.parcel.recipient_addressline1,
              row.parcel.recipient_addressline2,
              row.parcel.recipient_addressline3,
            ]
              .filter(Boolean)
              .join(' '),
        },
        {
          label: 'Consignee Address 2',
        },
        {
          label: 'Consignee Postcode',
          value: (row: { parcel: ManifestItem }) => row.parcel.postcode,
        },
        {
          label: 'Consignee City',
          value: (row: { parcel: ManifestItem }) => row.parcel.city_suburb?.slice(0, 12),
        },
        {
          label: 'Consignee State',
          value: (row: { parcel: ManifestItem }) => row.parcel.state?.slice(0, 11),
        },
        {
          label: 'Consignee Country',
          value: (row: { parcel: ManifestItem }) =>
            CountryISOService.getCountryCode2(row.parcel.country),
        },
        {
          label: 'Consignment Note',
          value: (row: { parcel: ManifestItem }) => row.parcel.tracking_id,
        },
        {
          label: 'Goods SKU',
          value: (row: { SKU: string }) => {
            return row.SKU;
          },
        },
        {
          label: 'Description of Goods',
          value: (row: { description: string }) => {
            return row.description;
          },
        },
        {
          label: 'Quantity',
          default: '1',
        },
        {
          label: 'Item Value Per Unit',
          value: (row: { total_declared_value: string }) => row.total_declared_value,
        },
        {
          label: 'HS Code',
          value: (row: { hs_code: string }) => row.hs_code,
        },
      ];
    }

    return [
      {
        label: 'LOADING PORT',
        value: (row: { parcel: { op_hub: { airport_code: any } } }) =>
          row.parcel.op_hub?.airport_code,
      },
      {
        label: 'MAWB NO',
        value: (row: { parcel: { mawb: { mawb_no: any } } }) => row.parcel.mawb?.mawb_no,
      },
      {
        label: 'VESSEL NAME',
        value: () => {
          return flightNo ? `${flightNo}/${STD}` : '';
        },
      },
      {
        label: 'ARRIVAL DATE',
        value: () => STA,
      },
      {
        label: 'DISCHARGE PORT',
      },
      {
        label: 'HAWB NO',
        value: (row: { parcel: { tracking_id: any } }) => row.parcel.tracking_id,
      },
      {
        label: 'MAWB NO',
        value: (row: { parcel: { mawb: { mawb_no: any } } }) => row.parcel.mawb?.mawb_no,
      },
      {
        label: 'MAWB DATE',
        value: (row: { parcel: { mawb: { tracking_status: any } } }) => {
          if (row.parcel.mawb && row.parcel.mawb.tracking_status) {
            return new Date(mawbInfo.mawb_date).toISOString().split('.')[0];
          }
        },
      },
      {
        label: 'SHIPPER COUNTRY CODE',
        value: (row: { parcel: { merchant: { country: string } } }) =>
          row.parcel.merchant && AesUtils.CrtCounterDecrypt(row.parcel.merchant.country),
      },
      {
        label: 'SHIPPER NAME',
        value: (row: { parcel: { merchant: { merchant_name: string } } }) =>
          row.parcel.merchant && AesUtils.CrtCounterDecrypt(row.parcel.merchant.merchant_name),
      },
      {
        label: 'SHIPPER ADDRESS',
        value: (row: {
          parcel: { merchant: { street: string; city: string; state: string; postal_code: any } };
        }) =>
          row.parcel.merchant &&
          // eslint-disable-next-line max-len
          `${AesUtils.CrtCounterDecrypt(row.parcel.merchant.street)} ${AesUtils.CrtCounterDecrypt(row.parcel.merchant.city)} ${AesUtils.CrtCounterDecrypt(row.parcel.merchant.state)} ${row.parcel.merchant.postal_code}`,
      },
      {
        label: 'SHIPPER PHONE',
        value: (row: { parcel: { merchant: { phone_number: string } } }) =>
          row.parcel.merchant && AesUtils.CrtCounterDecrypt(row.parcel.merchant.phone_number),
      },
      {
        label: 'CONSIGNEE COUNTRY CODE',
        value: (row: { parcel: { country: string } }) =>
          CountryISOService.getCountryCode2(row.parcel.country),
      },
      {
        label: 'CONSIGNEE NAME',
        value: (row: { parcel: { recipient_first_name: any; recipient_last_name: any } }) =>
          [row.parcel.recipient_first_name, row.parcel.recipient_last_name]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'CONSIGNEE ADDRESS',
        value: (row: {
          parcel: {
            recipient_addressline1: any;
            recipient_addressline2: any;
            recipient_addressline3: any;
            city_suburb: any;
            state: any;
            postcode: any;
          };
        }) =>
          [
            row.parcel.recipient_addressline1,
            row.parcel.recipient_addressline2,
            row.parcel.recipient_addressline3,
            row.parcel.city_suburb,
            row.parcel.state,
            row.parcel.postcode,
          ]
            .filter(Boolean)
            .join(' '),
      },
      {
        label: 'CONSIGNEE PHONE',
        value: (row: { parcel: { phone_country_code: any; phone: any } }) =>
          [row.parcel.phone_country_code, row.parcel.phone].filter(Boolean).join(' '),
      },
      {
        label: 'CONSIGNEE EMAIL',
        value: (row: any) => CustomManifestUtils.getConsigneeEmail(row.parcel, cat1MerchantList),
      },
      {
        label: 'INSURANCE (USD)',
      },
      {
        label: 'FREIGHT (USD)',
      },
      {
        label: 'WEIGHT (KG)',
        value: (row: { parcel: { weight_unit: any; weight: string } }) => {
          const unit = row.parcel.weight_unit;
          const weight = Number.parseFloat(row.parcel.weight);

          return unit === ENUM.measurements.unit.lb
            ? separateThousands(Number(weight) * 0.453_592_37)
            : separateThousands(weight);
        },
      },
      {
        label: 'BAG NO',
        value: (row: { parcel: { gaylord_no: any } }) => row.parcel.gaylord_no,
      },
      {
        label: 'REMARK',
        value: (row: { parcel: { incoterm: any } }) => row.parcel.incoterm,
      },
      {
        label: 'SHIPPER POST CODE',
        value: (row: { parcel: { merchant: { postal_code: any } } }) =>
          row.parcel.merchant?.postal_code,
      },
      {
        label: 'DESCRIPTION OF GOODS',
        value: (row: { description: any }) => {
          return row.description;
        },
      },
      {
        label: 'ORIGIN COUNTRY CODE',
        value: (row: { parcel: { op_hub: { country: string } } }) =>
          row.parcel.op_hub && CountryISOService.getCountryCode2(row.parcel.op_hub.country),
      },
      {
        label: 'PACKAGING QTY.',
        default: '1',
      },
      {
        label: 'PACKAGING TYPE CODE',
        default: 'PK',
      },
      {
        label: 'Currency',
        value: (row: { parcel: { merchant_declared_currency: any } }) =>
          row.parcel.merchant_declared_currency,
        default: currency,
      },
      {
        label: 'ITEM VALUE',
        value: (row: { total_declared_value: any }) => row.total_declared_value,
      },
      {
        label: 'Currency (Local)',
        default: 'MYR',
      },
      {
        label: 'ITEM VALUE (Local)',
        value: (row: {
          total_declared_value: any;
          parcel: { merchant_declared_currency: any };
        }) => {
          const exchangeRate = CurrencyConversionService.convertCurrency(
            allConversionRates,
            row.parcel.merchant_declared_currency || currency,
            'MYR',
          ).destination_currency;

          return Number((row.total_declared_value * exchangeRate).toFixed(2));
        },
      },
      {
        label: 'UOM',
      },
      {
        label: 'QTY',
        default: '1',
      },
      {
        label: 'Product Code',
        value: (row: { hs_code: any }) => {
          return row.hs_code;
        },
      },
      {
        label: 'Consignee Zip Code',
        value: (row: { parcel: { postcode: any } }) => row.parcel.postcode,
      },
      {
        label: 'IS DANGEROUS GOOD',
        default: 'FALSE',
      },
      {
        label: 'LENGTH',
        value: (row: { parcel: { length: string; dimensions_unit: string } }) => {
          let value = Number.parseFloat(row.parcel.length);

          if (row.parcel.dimensions_unit === ENUM.measurements.unit.in) {
            value = DimensionUtils.getCMSide(value, ENUM.measurements.unit.in);
          }

          return value || '';
        },
      },
      {
        label: 'WIDTH',
        value: (row: { parcel: { width: string; dimensions_unit: string } }) => {
          let value = Number.parseFloat(row.parcel.width);

          if (row.parcel.dimensions_unit === ENUM.measurements.unit.in) {
            value = DimensionUtils.getCMSide(value, ENUM.measurements.unit.in);
          }

          return value || '';
        },
      },
      {
        label: 'HEIGHT',
        value: (row: { parcel: { height: string; dimensions_unit: string } }) => {
          let value = Number.parseFloat(row.parcel.height);

          if (row.parcel.dimensions_unit === ENUM.measurements.unit.in) {
            value = DimensionUtils.getCMSide(value, ENUM.measurements.unit.in);
          }

          return value || '';
        },
      },
    ];
  },
};
