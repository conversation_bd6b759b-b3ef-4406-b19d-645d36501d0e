import { Parser as Json2csvParser } from 'json2csv';
import sortBy from 'lodash/sortBy.js';
import sumBy from 'lodash/sumBy.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import CountryISOService from '~/services/countryISO-service.js';
import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { DestinationService } from '~/services/destination-services.js';
import StatusMappingService from '~/services/statusMappingService.js';

import { QueryUtils } from '~/utilities/queryUtils.js';

import { CONSTANTS } from '~/constants/index.js';
import { Daos } from '~/daos/index.js';
import { ManifestItemStatus } from '~/types/manifest-item.type.js';

import { AppLogger } from './logUtils.js';
import { UnitConverterUtils } from './unitConverterUtils.js';

const logger = new AppLogger({ name: 'PlsReportUtils' });
interface IRemarks {
  message: string;
  date: string;
  updatedBy?: string;
}

interface IInvestigationItem {
  id: string;
  remarks: IRemarks[];
}

export const PlsReportUtils = {
  exportParcelsToCSV(fields: any[], data: any, isNewReport = false) {
    if (isNewReport && Array.isArray(data) && data.length === 0) {
      return null;
    }

    try {
      const newReportConfig = {
        eol: '*\n*',
        delimiter: ';;',
        quote: '',
        escapedQuote: '',
      };
      const csvParseConfig = {
        fields,
        withBOM: true,
        ...(isNewReport ? newReportConfig : {}),
      };

      const csvData = new Json2csvParser(csvParseConfig).parse(data);

      if (!isNewReport) {
        return csvData;
      }

      const lines = csvData.trim().split(newReportConfig.eol);

      // Extract the headers from the first line
      const headers = lines[0].split(newReportConfig.delimiter);

      // Remove the first line (headers) from the lines array
      lines.shift();

      // Convert each line to JSON object
      const jsonData = lines.map((line) => {
        const values = line.split(newReportConfig.delimiter);
        const obj = {};

        for (const [index, header] of headers.entries()) {
          // eslint-disable-next-line quotes
          obj[header] = values[index].replaceAll('"', '');
        }

        return obj;
      });

      return jsonData;
    } catch (error) {
      logger.error({ error });
    }
  },

  exportGeneralReport(data: any, isNewReport: boolean = false) {
    let fields = [
      {
        label: 'PLS_shipment_booking_reference',
        value: 'tracking_no',
      },
      {
        label: 'shipment_tracking_id',
        value: (row: any) => (row.tracking_id ? `="${row.tracking_id}"` : ''),
      },
      {
        label: 'Alternate_TID_1',
        value: (row: any) =>
          row.tracking_id_history && row.tracking_id_history.length > 0
            ? `="${row.tracking_id_history[0].tracking_id}"`
            : '',
      },
      {
        label: 'Alternate_TID_2',
        value: (row: any) =>
          row.tracking_id_history && row.tracking_id_history.length >= 2
            ? `="${row.tracking_id_history[1].tracking_id}"`
            : '',
      },
      {
        label: 'latest_tracking_status',
        value: (row: any) => row.tracking_status.at(-1).status,
      },
      {
        label: 'PLS_batch_no',
        value: 'PLS_batch_no',
      },
      {
        label: 'MP_booking_no',
        value: 'MP_booking_no',
      },
      {
        label: 'Logistics Type',
        value: 'logistics_type',
      },
      {
        label: 'Assigned Date/Time (LCL)',
        value: 'assigned_datetime',
      },
      'AU_post_article_id',
      'AU_post_shipment_id',
      'internal_batch_order_datetime',
      'shortage_of_shipment_from_batch',
      'gaylord_no',
      {
        label: 'overpack_no',
        value: (row: any) => (row.gaylord && row.gaylord.overpack_id) || '',
      },
      {
        label: 'merchant_batch_no',
        value: 'merchant_batch_no',
      },
      'invoice_no',
      'invoice_generation_datetime',
      'invoice_remarks',
      {
        label: 'merchant_order_no',
        value: 'order_number',
      },
      'order_date',
      'recipient_first_name',
      'recipient_last_name',
      'recipient_company',
      'recipient_addressline1',
      'recipient_addressline2',
      'recipient_addressline3',
      'city_suburb',
      'state',
      'postcode',
      'country',
      'phone',
      'lmd_zone',
      'destination_group',
      'phone_country_code',
      'email',
      'goods_description',
      'hs_code',
      {
        label: 'declared_weight_unit',
        value: 'origin_weight_unit',
      },
      {
        label: 'declared_weight',
        value: 'origin_weight',
      },
      {
        label: 'declared_dimensions_unit',
        value: 'origin_dimensions_unit',
      },
      {
        label: 'declared_length',
        value: 'origin_length',
      },
      {
        label: 'declared_width',
        value: 'origin_width',
      },
      {
        label: 'declared_height',
        value: 'origin_height',
      },
      {
        label: 'actual_weight_unit',
        value: 'weight_unit',
      },
      {
        label: 'actual_weight',
        value: 'weight',
      },
      {
        label: 'actual_dimensions_unit',
        value: 'dimensions_unit',
      },
      {
        label: 'actual_length',
        value: 'length',
      },
      {
        label: 'actual_width',
        value: 'width',
      },
      {
        label: 'actual_height',
        value: 'height',
      },
      {
        label: 'calculated_volumetric_weight(lb)',
        value: (row: any) => UnitConverterUtils.getVolumetricWeightLb(row),
        default: 0,
      },
      {
        label: 'calculated_volumetric_weight(kg)',
        value: (row: any) => UnitConverterUtils.getVolumetricWeightKg(row),
        default: 0,
      },
      'de_minimis_tier',
      'merchant_declared_currency',
      {
        label: 'total_merchant_declared_value',
        value: (row: any) => PlsReportUtils.getTotalMerchantDeclaredCifValue(row),
        default: 0,
      },
      'converted_merchant_declared_currency',
      'converted_merchant_declared_value',
      'calculated_tax_currency',
      'calculated_tax_amount',
      'reason_for_duties_incurred',
      'merchant_drop_off',
      'incoterm',
      {
        label: 'contains_battery',
        value: (row: any) => row.contains_battery || row.battery_type,
      },
      {
        label: 'battery_pack',
        value: (row: any) => row.battery_pack || row.battery_packing,
      },
      {
        label: 'contains_dg',
        value: (row: any) => row.contains_dg || row.contains_other_dg,
      },
      'shipping_instruction',
      'service_option',
      'merchant_drop_off',
      'operation_hub',
      'merchant_account_no',
      {
        label: 'merchant_name',
        value: (row: any) => row.merchant_name,
        default: '',
      },
      'promotion_type',
      'insure',
      {
        label: 'insurance_opt_in_cost',
        value: (row: any) => row.insurance_amount,
      },
      'insurance_currency',
      'insurance_percentage',
      'max_insured_value',
      'cod',
      'product_sku',
      'mawb_no',
      {
        label: 'hawb_id',
        value: (row: any) => row.hawb_id || '',
      },
      'warehouse_location',
      'not_in_manifest',
      {
        label: 'booked',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.booked),
          ),
      },
      {
        label: ENUM.parcelStatus.pending_booking_updates,
        value: (row) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.pending_booking_updates),
          ),
      },
      'rejected_at_booking',
      'assigned_to_warehouse',
      {
        label: 'received_at_warehouse',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
          ),
      },
      'accepted_at_warehouse',
      'captured_actual_dimension_and_weight',
      {
        label: 'sorted',
        value: (row: { tracking_status: any[]; sorted: any }) => {
          if (!row.tracking_status || row.tracking_status.length === 0) {
            return;
          }

          const sorted = row.tracking_status.find((item) => {
            return item.status === StatusMappingService.getManifestStatus(ENUM.parcelStatus.sorted);
          });

          if (sorted) {
            row.sorted = sorted.date;
          }

          return row.sorted;
        },
      },
      ...CONSTANTS.PARCEL_EXCEPTION_STATUS.map((st) => ({
        label: st,
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(st),
          ),
      })),
      {
        label: 'packed_to_gaylord',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.packed_to_gaylord),
          ),
      },
      'gaylord_ready_to_close',
      'close_gaylord',
      'awb_booking_updated',
      {
        label: 'fwb_received',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.fwb_received),
          ),
      },
      {
        label: 'rcs_received_by_airline',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.rcs_received_by_airline),
          ),
      },
      'notified_customs_lmd',
      {
        label: 'dep_from_origin_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
            false,
          ),
      },
      {
        label: 'dep_from_transit_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
            true,
          ),
      },
      {
        label: 'arr_at_transit_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
            true,
          ),
      },
      {
        label: 'arr_at_destination_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
            false,
          ),
      },
      {
        label: 'rcf_hipment_received_from_given_flight',
        value: (row: any) => PlsReportUtils.getParcelTrackingStatusDate(row, 'RCF'),
      },
      {
        label: 'nfd_consignee_informed_that_shipment_has_arrived',
        value: (row: any) => PlsReportUtils.getParcelTrackingStatusDate(row, 'NFD'),
      },
      {
        label: 'dlv_shipment_handed_over_to_consignee',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
            ),
          ),
      },
      {
        label: 'arrived_processed_at_sorting_hub',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.arrived_and_processing_at_sorting_hub,
            ),
          ),
      },
      {
        label: 'on_vehicle_for_delivery',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            0,
          ),
      },
      {
        label: 'successful_delivery',
        value: (row: { service_option: string }) => {
          if (!row.service_option || row.service_option.toLowerCase() !== 'self-collect') {
            return PlsReportUtils.getParcelTrackingStatusDate(
              row,
              StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
              0,
            );
          }
        },
      },
      {
        label: 'attempted delivery 1',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            0,
          ),
      },
      {
        label: 'on_vehicle_for_delivery2',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            1,
          ),
      },
      {
        label: 'attempted delivery 2',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            1,
          ),
      },
      {
        label: 'on_vehicle_for_delivery3',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            2,
          ),
      },
      {
        label: 'attempted delivery 3',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            2,
          ),
      },
      {
        label: 'return_to_sq_triggered',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.return_to_sq_triggered),
          ),
      },
      {
        label: 'returned_to_merchant',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.returned_to_merchant),
          ),
      },
      {
        label: 'parcel_collected',
        value: (row: any) => PlsReportUtils.getParcelCollected(row),
      },
      {
        label: 'pending_pickup',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.pending_pickup),
          ),
      },
      {
        label: 'self collection point name',
        value: (row: any) => row.collection_point_name,
      },
      ...[
        ENUM.parcelStatus.lmd_reject_booking,
        ENUM.parcelStatus.lmd_receive_booking,
        ENUM.parcelStatus.update_of_recipient_details,
        ENUM.parcelStatus.picked_up,
        ENUM.parcelStatus.cancelled,
        ENUM.parcelStatus.fmd_pickup_scheduled,
        ENUM.parcelStatus.dropoff_scheduled,
      ].map((status) => ({
        label: status,
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(status),
          ),
      })),
      {
        label: 'cancellation_attempt',
        value: (row: any) =>
          row.cancellation_attempt && row.cancellation_attempt.length > 0
            ? row.cancellation_attempt[0].date
            : '',
      },
      {
        label: 'expired',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.expired),
          ),
      },
      {
        label: 'Derived Service Description',
        value: (row: any) => PlsReportUtils.getDerivedServiceDescription(row),
      },
      {
        label: 'Derived Tax Code',
        value: 'taxCode',
      },
      {
        label: 'PD_ready_to_invoice_datetime',
        value: (row: any) =>
          PlsReportUtils.getReadyToInvoiceTimestamp(row, ENUM.invoiceStatus.PD_ready_to_invoice),
      },
      {
        label: 'PD_invoiced_datetime',
        value: (row: any) =>
          PlsReportUtils.getInvoicedTimestamp(row, ENUM.invoiceStatus.PD_invoiced),
      },
      {
        label: 'PD_invoice_generation_datetime',
        value: 'PD_invoice_generation_datetime',
      },
      {
        label: 'PD_invoice_no',
        value: 'PD_invoice_no',
      },
      {
        label: 'DT_ready_to_invoice_datetime',
        value: (row: any) =>
          PlsReportUtils.getReadyToInvoiceTimestamp(row, ENUM.invoiceStatus.DT_ready_to_invoice),
      },
      {
        label: 'DT_invoiced_datetime',
        value: (row: any) =>
          PlsReportUtils.getInvoicedTimestamp(row, ENUM.invoiceStatus.DT_invoiced),
      },
      {
        label: 'DT_invoice_generation_datetime',
        value: 'DT_invoice_generation_datetime',
      },
      {
        label: 'DT_invoice_no',
        value: 'DT_invoice_no',
      },
      {
        label: 'CB_import_duties',
        value: (row: any) => row.CB_import_duties || 'N/A',
      },
      {
        label: 'CB_import_taxes',
        value: (row: any) => row.CB_import_taxes || 'N/A',
      },
      {
        label: 'CB_other_charges',
        value: (row: any) => row.CB_other_charges || 'N/A',
      },
      {
        label: 'in_transit',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.in_transit),
          ),
      },
      {
        label: 'possible_delay_from_last_mile',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.possible_delay_from_last_mile),
          ),
      },
      {
        label: 'awaiting_collection',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.awaiting_collection),
          ),
      },
      {
        label: 'Lost',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lost),
          ),
      },
      {
        label: 'Lost Reason',
        value: (row: any) =>
          PlsReportUtils.getLostDamagedReason(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lost),
          ),
      },
      {
        label: 'Damaged',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged),
          ),
      },
      {
        label: 'Damaged Reason',
        value: (row: any) =>
          PlsReportUtils.getLostDamagedReason(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged),
          ),
      },
      {
        label: 'MAWB_currency',
        value: (row: any) => row.mawb_currency,
      },
      {
        label: 'MAWB_amount',
        value: (row: any) => row.mawb_amount,
      },
      {
        label: 'MAWB_amount_updated',
        value: (row: any) => row.mawb_amount_updated,
      },
      'PLS_batch_status',
      {
        label: 'AWB issue date',
        value: (row: any) => row.mawb_issue_date,
      },
      {
        label: 'LMD_receiving_shortage',
        value: (row: any) => row.LMD_receiving_shortage,
      },
      {
        label: 'AR_Posted',
        value: (row: any) => row.PD_AR_posted || row.AR_Posted,
      },
      {
        label: 'Customs_Cleared',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.custom_cleared),
          ),
      },
      {
        label: 'En-route_to_destination_sorting_hub',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.enroute_to_destination_sorting_hub,
            ),
          ),
      },
      {
        label: 'Arrived at Destination Airport Facility',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.arrived_at_destination_custom_facility,
            ),
          ),
      },
      {
        label: 'PD_GL_posted',
        value: (row: any) => row.PD_GL_posted,
      },
      {
        label: 'PD_GL_filename',
        value: (row: any) => row.PD_GL_filename,
      },
      {
        label: 'disposed',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.disposed),
          ),
      },
      {
        label: 'AU Post order_id',
        value: 'order_id',
      },
      'order_creation_date',
      {
        label: 'Unit_number',
        value: (row: any) => PlsReportUtils.getGaylordUnitNumber(row.gaylord) || '',
      },
      {
        label: 'Exception Resolved',
        value: (row: any) => PlsReportUtils.getResolvedStatus(row) || '',
        default: null,
      },
      {
        label: 'LMD Reason',
        value: (row: any) => row.error_booking_msg,
        default: '',
      },
      {
        label: 'Pickup Required',
        value: 'pickup_required',
        default: '',
      },
      {
        label: 'TML Code',
        value: 'TML_Code',
        default: '',
      },
      {
        label: 'Korea Clearance Type',
        value: 'korea_clearance_type',
        default: '',
      },
      {
        label: 'Job Completed',
        value: (row: any) => (row.job_completed ? row.job_completed.status : false),
      },
      {
        label: 'booking platform',
        value: (row: any) => row.booking_platform,
        default: '',
      },
      {
        label: 'origin airport',
        value: (row: any) => row.origin,
        default: '',
      },
      {
        label: 'destination airport',
        value: (row: any) => row.airport_code,
        default: '',
      },
      {
        label: 'recipient_document_type',
        value: (row: any) =>
          row.recipient_document_type && row.recipient_document_type.toUpperCase(),
        default: '',
      },
      {
        label: 'recipient_id',
        value: (row: any) => row.recipient_id,
        default: '',
      },
      {
        label: 'recipient_nationality',
        value: (row: any) => row.recipient_nationality,
        default: '',
      },
      {
        label: 'lmd',
        value: (row: any) => row.lmd,
        default: '',
      },
      {
        label: 'shipment_type',
        value: (row: any) => row.shipment_type,
        default: '',
      },
      {
        label: 'shipment_notification',
        value: (row: any) => row.shipment_notification,
        default: '',
      },
      {
        label: 'BoxC Manifest Triggered',
        value: (row: any) => row.boxc_manifest_triggered,
        default: '',
      },
      {
        label: 'manifest_id',
        value: (row: any) => row.manifest_id,
        default: '',
      },
      {
        label: 'final_unsuccessful_delivery',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.final_unsuccessful_delivery),
          ),
        default: '',
      },
      {
        label: 'Relation Type',
        value: (row: any) => `="${this.getParcelRelationType(row)}"`,
        default: '',
      },
      {
        label: 'Parent ID',
        value: 'parent_id',
        default: '',
      },
    ];

    fields = fields.concat(this.getItemsFromData(data), this.getShipperFields());

    return this.exportParcelsToCSV(fields, data, isNewReport);
  },

  exportNewReport(data: any, isNewReport = false) {
    let fields = [
      {
        label: 'PLS_shipment_booking_reference',
        value: 'tracking_no',
      },
      {
        label: 'shipment_tracking_id',
        value: (row: any) => (row.tracking_id ? `"${row.tracking_id}"` : ''),
      },
      {
        label: 'latest_tracking_status',
        value: (row: any) => row.tracking_status.at(-1).status,
      },
      'gaylord_no',
      {
        label: 'overpack_no',
        value: (row: any) => (row.gaylord && row.gaylord.overpack_id) || '',
      },
      {
        label: 'merchant_order_no',
        value: 'order_number',
      },
      'recipient_addressline1',
      'recipient_addressline2',
      'recipient_addressline3',
      'city_suburb',
      'state',
      'postcode',
      'country',
      'destination_group',
      {
        label: 'declared_weight_unit',
        value: 'origin_weight_unit',
      },
      {
        label: 'declared_weight',
        value: 'origin_weight',
      },
      {
        label: 'declared_dimensions_unit',
        value: 'origin_dimensions_unit',
      },
      {
        label: 'declared_length',
        value: 'origin_length',
      },
      {
        label: 'declared_width',
        value: 'origin_width',
      },
      {
        label: 'declared_height',
        value: 'origin_height',
      },
      {
        label: 'actual_weight_unit',
        value: 'weight_unit',
      },
      {
        label: 'actual_weight',
        value: 'weight',
      },
      {
        label: 'actual_dimensions_unit',
        value: 'dimensions_unit',
      },
      {
        label: 'actual_length',
        value: 'length',
      },
      {
        label: 'actual_width',
        value: 'width',
      },
      {
        label: 'actual_height',
        value: 'height',
      },
      {
        label: 'calculated_volumetric_weight(kg)',
        value: (row: any) => UnitConverterUtils.getVolumetricWeightKg(row),
        default: 0,
      },
      'merchant_declared_currency',
      'incoterm',
      'operation_hub',
      'merchant_name',
      'mawb_no',
      {
        label: 'booked',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.booked),
          ),
      },
      {
        label: 'received_at_warehouse',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
          ),
      },
      {
        label: 'sorted',
        value: (row: any) => {
          if (!row.tracking_status || row.tracking_status.length === 0) {
            return;
          }

          const sorted = row.tracking_status.find((item: { status: any }) => {
            return item.status === StatusMappingService.getManifestStatus(ENUM.parcelStatus.sorted);
          });

          if (sorted) {
            row.sorted = sorted.date;
          }

          return row.sorted;
        },
      },
      {
        label: 'packed_to_gaylord',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.packed_to_gaylord),
          ),
      },
      'gaylord_ready_to_close',
      'close_gaylord',
      {
        label: 'fwb_received',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.fwb_received),
          ),
      },
      {
        label: 'rcs_received_by_airline',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.rcs_received_by_airline),
          ),
      },
      {
        label: 'dep_from_origin_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
            false,
          ),
      },
      {
        label: 'dep_from_transit_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
            true,
          ),
      },
      {
        label: 'arr_at_transit_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
            true,
          ),
      },
      {
        label: 'arr_at_destination_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
            false,
          ),
      },
      {
        label: 'dlv_shipment_handed_over_to_consignee',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
            ),
          ),
      },
      {
        label: 'arrived_processed_at_sorting_hub',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.arrived_and_processing_at_sorting_hub,
            ),
          ),
      },
      {
        label: 'on_vehicle_for_delivery',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            0,
          ),
      },
      {
        label: 'successful_delivery',
        value: (row: any) => {
          if (!row.service_option || row.service_option.toLowerCase() !== 'self-collect') {
            return PlsReportUtils.getParcelTrackingStatusDate(
              row,
              StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
              0,
            );
          }
        },
      },
      {
        label: 'attempted delivery 1',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            0,
          ),
      },
      {
        label: 'on_vehicle_for_delivery2',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            1,
          ),
      },
      {
        label: 'attempted delivery 2',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            1,
          ),
      },
      {
        label: 'on_vehicle_for_delivery3',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            2,
          ),
      },
      {
        label: 'attempted delivery 3',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            2,
          ),
      },
      {
        label: 'lmd_reject_booking',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lmd_reject_booking),
          ),
      },
      {
        label: 'lmd_receive_booking',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lmd_receive_booking),
          ),
      },
      {
        label: 'picked_up',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.picked_up),
          ),
      },
      {
        label: 'cancelled',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.cancelled),
          ),
      },
      {
        label: 'cancellation_attempt',
        value: (row: any) =>
          row.cancellation_attempt && row.cancellation_attempt.length > 0
            ? row.cancellation_attempt[0].date
            : '',
      },
      {
        label: 'expired',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.expired),
          ),
      },
      {
        label: 'damaged_at_destination',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged_at_destination),
          ),
      },
      {
        label: 'in_transit',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.in_transit),
          ),
      },
      {
        label: 'possible_delay_from_last_mile',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.possible_delay_from_last_mile),
          ),
      },
      {
        label: 'awaiting_collection',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.awaiting_collection),
          ),
      },
      {
        label: 'Lost',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lost),
          ),
      },
      {
        label: 'Lost Reason',
        value: (row: any) =>
          PlsReportUtils.getLostDamagedReason(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lost),
          ),
      },
      {
        label: 'Damaged',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged),
          ),
      },
      {
        label: 'Damaged Reason',
        value: (row: any) =>
          PlsReportUtils.getLostDamagedReason(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged),
          ),
      },
      {
        label: 'LMD_receiving_shortage',
        value: (row: any) => row.LMD_receiving_shortage,
      },

      {
        label: 'Customs_Cleared',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.custom_cleared),
          ),
      },
      {
        label: 'En-route_to_destination_sorting_hub',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.enroute_to_destination_sorting_hub,
            ),
          ),
      },
      {
        label: 'Arrived at Destination Airport Facility',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.arrived_at_destination_custom_facility,
            ),
          ),
      },
      {
        label: 'LMD Reason',
        value: (row: any) => row.error_booking_msg,
        default: '',
      },
      {
        label: 'origin airport',
        value: (row: any) => row.origin,
        default: '',
      },
      {
        label: 'destination airport',
        value: (row: any) => row.airport_code,
        default: '',
      },
      {
        label: 'lmd',
        value: (row: any) => row.lmd,
        default: '',
      },
      {
        label: 'received_at_warehouse_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      {
        label: 'parcel_age',
        value: (row: any) => PlsReportUtils.getParcelAge(row),
      },
      {
        label: 'successful_delivery_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      {
        label: 'dlv_shipment_handed_over_to_consignee_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
            ),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      {
        label: 'in_transit_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.in_transit),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      {
        label: 'awaiting_delivery_instructions',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.awaiting_delivery_instructions,
            ),
          ),
      },
    ];

    fields = fields.concat(this.getItemsFromData(data), this.getShipperFields());

    return this.exportParcelsToCSV(fields, data, isNewReport);
  },

  exportBusinessReport(data: any, isNewReport = false) {
    const fields = [
      {
        label: 'PLS_shipment_booking_reference',
        value: 'tracking_no',
      },
      {
        label: 'shipment_tracking_id',
        value: (row: any) => (row.tracking_id ? `="${row.tracking_id}"` : ''),
      },
      {
        label: 'Alternate_TID_1',
        value: (row: any) =>
          row.tracking_id_history && row.tracking_id_history.length > 0
            ? `="${row.tracking_id_history[0].tracking_id}"`
            : '',
      },
      {
        label: 'Alternate_TID_2',
        value: (row: any) =>
          row.tracking_id_history && row.tracking_id_history.length >= 2
            ? `="${row.tracking_id_history[1].tracking_id}"`
            : '',
      },
      {
        label: 'latest_tracking_status',
        value: (row: any) => row.tracking_status.at(-1).status,
      },
      'mawb_no',
      {
        label: 'PD_ready_to_invoice_datetime',
        value: (row: any) =>
          PlsReportUtils.getReadyToInvoiceTimestamp(row, ENUM.invoiceStatus.PD_ready_to_invoice),
      },
      {
        label: 'PD_invoiced_datetime',
        value: (row: any) =>
          PlsReportUtils.getInvoicedTimestamp(row, ENUM.invoiceStatus.PD_invoiced),
      },
      'invoice_no',
      {
        label: 'DT_manual_upload_datetime',
        value: (row: any) => row.DT_manual_upload_datetime,
        default: '',
      },
      {
        label: 'DT_ready_to_invoice_datetime',
        value: (row: any) =>
          PlsReportUtils.getReadyToInvoiceTimestamp(row, ENUM.invoiceStatus.DT_ready_to_invoice),
      },
      {
        label: 'DT_invoice_datetime',
        value: (row: any) =>
          PlsReportUtils.getInvoicedTimestamp(row, ENUM.invoiceStatus.DT_invoiced),
      },
      {
        label: 'DT_invoice_no',
        value: 'DT_invoice_no',
        default: '',
      },
      'merchant_name',
      'operation_hub',
      'state',
      'country',
      {
        label: 'actual_weight_unit',
        value: 'weight_unit',
      },
      {
        label: 'actual_weight',
        value: 'weight',
      },
      {
        label: 'calculated_volumetric_weight(kg)',
        value: (row: any) => UnitConverterUtils.getVolumetricWeightKg(row),
        default: 0,
      },
      {
        label: 'chargeable_weight (kg)',
        value: (row: any) => row.chargeableWeight,
        default: 0,
      },
      'merchant_declared_currency',
      {
        label: 'total_merchant_declared_value',
        value: (row: any) => PlsReportUtils.getTotalMerchantDeclaredCifValue(row),
        default: 0,
      },
      'converted_merchant_declared_currency',
      'converted_merchant_declared_value',
      'calculated_tax_currency',
      'calculated_tax_amount',
      'incoterm',
      'service_option',
      'insure',
      {
        label: 'insurance_opt_in_cost',
        value: (row: any) => row.insurance_amount,
      },
      'insurance_currency',
      {
        label: 'received_at_warehouse',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
          ),
      },
      {
        label: 'successful_delivery',
        value: (row: any) => {
          if (!row.service_option || row.service_option.toLowerCase() !== 'self-collect') {
            return PlsReportUtils.getParcelTrackingStatusDate(
              row,
              StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
              0,
            );
          }
        },
      },
      {
        label: 'attempted delivery 1',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            0,
          ),
      },
      {
        label: 'attempted delivery 2',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            1,
          ),
      },
      {
        label: 'attempted delivery 3',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            2,
          ),
      },
      {
        label: 'Relation Type',
        value: (row: any) => `="${this.getParcelRelationType(row)}"`,
        default: '',
      },
      {
        label: 'Parent ID',
        value: 'parent_id',
        default: '',
      },
    ];

    return this.exportParcelsToCSV(fields, data, isNewReport);
  },

  exportInvoicingReport(data: any, isNewReport = false) {
    const fields = [
      {
        label: 'PXL ID',
        value: (row: any) => `="${row.id}"`,
      },
      'merchant_name',
      {
        label: 'PD READY',
        value: (row) =>
          `="${this.changeInvoiceFormatDateTime(row, ENUM.invoiceStatus.PD_ready_to_invoice)}"`,
      },
      {
        label: 'PD INVOICED',
        value: (row) =>
          `="${this.changeInvoiceFormatDateTime(row, ENUM.invoiceStatus.PD_invoiced)}"`,
      },
      {
        label: 'DT READY',
        value: (row) =>
          `="${this.changeInvoiceFormatDateTime(row, ENUM.invoiceStatus.DT_ready_to_invoice)}"`,
      },
      {
        label: 'DT INVOICED',
        value: (row) =>
          `="${this.changeInvoiceFormatDateTime(row, ENUM.invoiceStatus.DT_invoiced)}"`,
      },
      'operation_hub',
      'country',
      'lmd_zone',
      'incoterm',
      {
        label: 'converted_currency',
        value: (row: any) =>
          row.country ? `="${CountryISOService.getCurrencyByCountry(row.country)}"` : '',
      },
      {
        label: 'invoice_currency',
        value: (row: any) => (row.PD_rate_currency ? `="${row.PD_rate_currency}"` : ''),
      },
      {
        label: 'dt_payable_converted_value',
        value: (row: any) => `="${this.calculateDtPayableConvertedValue(row)}"`,
      },
      {
        label: 'pd_payable_converted_value',
        value: 'PD_rate',
        default: 'N/A',
      },
      {
        label: 'chargeable_weight (kg)',
        value: (row: any) => (row.chargeableWeight ? `="${row.chargeableWeight}"` : ''),
      },
    ];

    return this.exportParcelsToCSV(fields, data, isNewReport);
  },

  getOperationFields(data: any) {
    let fields = [
      {
        label: 'PLS_shipment_booking_reference',
        value: 'tracking_no',
      },
      {
        label: 'shipment_tracking_id',
        value: (row: any) => (row.tracking_id ? `="${row.tracking_id}"` : ''),
      },
      {
        label: 'Alternate_TID_1',
        value: (row: any) =>
          row.tracking_id_history && row.tracking_id_history.length > 0
            ? `="${row.tracking_id_history[0].tracking_id}"`
            : '',
      },
      {
        label: 'Alternate_TID_2',
        value: (row: any) =>
          row.tracking_id_history && row.tracking_id_history.length >= 2
            ? `="${row.tracking_id_history[1].tracking_id}"`
            : '',
      },
      {
        label: 'AU_post_article_id',
        value: (row: any) => (row.AU_post_article_id ? row.AU_post_article_id : ''),
      },
      'mawb_no',
      {
        label: 'hawb_id',
        value: (row: any) => row.hawb_id || '',
      },
      {
        label: 'latest_tracking_status',
        value: (row: any) => row.tracking_status.at(-1).status,
      },
      // add new fields PLS-5270
      {
        label: 'parcel_age',
        value: (row: any) => PlsReportUtils.getParcelAge(row),
      },
      {
        label: 'received_at_warehouse_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      {
        label: 'successful_delivery_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      {
        label: 'dlv_shipment_handed_over_to_consignee_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
            ),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      {
        label: 'in_transit_date',
        value: (row: any) => {
          const date = PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.in_transit),
          );

          return date ? date.slice(0, 10) : null;
        },
      },
      // end add new fields
      'operation_hub',
      {
        label: 'origin airport',
        value: (row: any) => row.origin,
        default: '',
      },
      {
        label: 'merchant_name',
        value: (row: any) => row.merchant_name,
        default: '',
      },
      'destination_group',
      {
        label: 'lmd',
        value: (row: any) => row.lmd,
        default: '',
      },
      {
        label: 'Point of Discharge',
        value: (row: any) => row.point_of_discharge,
        default: '',
      },
      'recipient_first_name',
      'recipient_last_name',
      'recipient_addressline1',
      'recipient_addressline2',
      'recipient_addressline3',
      'city_suburb',
      'state',
      'postcode',
      'country',
      'email',
      'phone_country_code',
      'phone',
      {
        label: 'declaration',
        value: 'declaration',
        default: '',
      },
      ...[
        ENUM.parcelStatus.booked,
        ENUM.parcelStatus.pending_booking_updates,
        ENUM.parcelStatus.lmd_receive_booking,
        ENUM.parcelStatus.update_of_recipient_details,
        ENUM.parcelStatus.picked_up,
        ENUM.parcelStatus.received_at_warehouse,
        ENUM.parcelStatus.sorted,
        ENUM.parcelStatus.rcs_received_by_airline,
        ENUM.parcelStatus.fwb_received,
      ].map((status) => ({
        label: status,
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(status),
          ),
      })),
      {
        label: 'dep_from_origin_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
            false,
          ),
        default: null,
      },
      {
        label: 'arr_at_transit_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
            true,
          ),
        default: null,
      },
      {
        label: 'dep_from_transit_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
            true,
          ),
        default: null,
      },
      {
        label: 'arr_at_destination_airport',
        value: (row: any) =>
          PlsReportUtils.getMawbSttAtAirportDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
            false,
          ),
        default: null,
      },
      {
        label: 'Arrived at Destination Airport Facility',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.arrived_at_destination_custom_facility,
            ),
          ),
      },
      {
        label: 'dlv_shipment_handed_over_to_consignee',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
            ),
          ),
        default: null,
      },
      {
        label: 'Customs_Cleared',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.custom_cleared),
          ),
      },
      {
        label: 'En-route_to_destination_sorting_hub',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.enroute_to_destination_sorting_hub,
            ),
          ),
        default: null,
      },
      {
        label: 'arrived_processed_at_sorting_hub',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(
              ENUM.parcelStatus.arrived_and_processing_at_sorting_hub,
            ),
          ),
        default: null,
      },
      {
        label: 'in_transit',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.in_transit),
          ),
        default: null,
      },
      {
        label: 'on_vehicle_for_delivery',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            0,
          ),
        default: null,
      },
      {
        label: 'successful_delivery',
        value: (row: any) => {
          if (!row.service_option || row.service_option.toLowerCase() !== 'self-collect') {
            return PlsReportUtils.getParcelTrackingStatusDate(
              row,
              StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
              0,
            );
          }
        },
        default: null,
      },
      {
        label: 'awaiting_collection',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.awaiting_collection),
          ),
        default: null,
      },
      {
        label: 'attempted delivery 1',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            0,
          ),
        default: null,
      },
      {
        label: 'on_vehicle_for_delivery2',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            1,
          ),
        default: null,
      },
      {
        label: 'attempted delivery 2',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            1,
          ),
        default: null,
      },
      {
        label: 'on_vehicle_for_delivery3',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
            2,
          ),
        default: null,
      },
      {
        label: 'attempted delivery 3',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
            2,
          ),
        default: null,
      },
      {
        label: 'returned_to_merchant',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.returned_to_merchant),
          ),
        default: null,
      },
      {
        label: 'Lost',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lost),
          ),
        default: null,
      },
      {
        label: 'Lost Reason',
        value: (row: any) =>
          PlsReportUtils.getLostDamagedReason(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lost),
          ),
        default: null,
      },
      {
        label: 'Damaged',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged),
          ),
        default: null,
      },
      {
        label: 'Damaged Reason',
        value: (row: any) =>
          PlsReportUtils.getLostDamagedReason(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged),
          ),
        default: null,
      },
      {
        label: 'LMD Reason',
        value: (row: any) => row.error_booking_msg,
        default: '',
      },
      {
        label: 'Pickup Required',
        value: 'pickup_required',
        default: '',
      },
      {
        label: 'declared_weight_unit',
        value: 'origin_weight_unit',
      },
      {
        label: 'declared_weight',
        value: 'origin_weight',
      },
      {
        label: 'declared_dimensions_unit',
        value: 'origin_dimensions_unit',
      },
      {
        label: 'declared_length',
        value: 'origin_length',
      },
      {
        label: 'declared_width',
        value: 'origin_width',
      },
      {
        label: 'declared_height',
        value: 'origin_height',
      },
      {
        label: 'actual_weight_unit',
        value: 'weight_unit',
      },
      {
        label: 'actual_weight',
        value: 'weight',
      },
      {
        label: 'actual_dimensions_unit',
        value: 'dimensions_unit',
      },
      {
        label: 'actual_length',
        value: 'length',
      },
      {
        label: 'actual_width',
        value: 'width',
      },
      {
        label: 'actual_height',
        value: 'height',
      },
      {
        label: 'calculated_volumetric_weight(kg)',
        value: (row: any) => UnitConverterUtils.getVolumetricWeightKg(row),
        default: 0,
      },
      'de_minimis_tier',
      'merchant_declared_currency',
      {
        label: 'total_merchant_declared_value',
        value: (row: any) => PlsReportUtils.getTotalMerchantDeclaredCifValue(row),
        default: 0,
      },
      'incoterm',
      {
        label: 'PLS_batch_no',
        value: 'PLS_batch_no',
      },
      {
        label: 'MP_booking_no',
        value: 'MP_booking_no',
      },
      'gaylord_no',
      {
        label: 'overpack_no',
        value: (row: any) => (row.gaylord && row.gaylord.overpack_id) || '',
      },
      {
        label: 'merchant_order_no',
        value: 'order_number',
      },
      'recipient_company',
      'converted_merchant_declared_currency',
      'converted_merchant_declared_value',
      {
        label: 'converted_local_merchant_declared_currency',
        value: (row: any) => CountryISOService.getCurrencyByCountry(row.origin_country),
      },
      {
        label: 'converted_local_merchant_declared_value',
        value: (row: any) => this.convertToLocalMerchantDeclaredValue(row),
      },
      'calculated_tax_currency',
      'calculated_tax_amount',
      'service_option',
      'insure',
      'insurance_percentage',
      'max_insured_value',
      {
        label: 'insurance_opt_in_cost',
        value: (row: any) => row.insurance_amount,
      },
      'insurance_currency',
      {
        label: 'contains_battery',
        value: (row: any) => row.contains_battery || row.battery_type,
      },
      {
        label: 'battery_pack',
        value: (row: any) => row.battery_pack || row.battery_packing,
      },
      {
        label: 'contains_dg',
        value: (row: any) => row.contains_dg || row.contains_other_dg,
      },
      {
        label: 'packed_to_gaylord',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.packed_to_gaylord),
          ),
      },
      'gaylord_ready_to_close',
      'close_gaylord',
      ...CONSTANTS.PARCEL_EXCEPTION_STATUS.map((st) => ({
        label: st,
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(st),
          ),
      })),
      {
        label: 'parcel_collected',
        value: (row: any) => PlsReportUtils.getParcelCollected(row),
      },
      {
        label: 'pending_pickup',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.pending_pickup),
          ),
      },
      {
        label: 'self collection point name',
        value: (row: any) => row.collection_point_name,
      },
      {
        label: 'lmd_reject_booking',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.lmd_reject_booking),
          ),
      },

      {
        label: 'cancelled',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.cancelled),
          ),
      },
      {
        label: 'fmd_pickup_scheduled',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.fmd_pickup_scheduled),
          ),
      },
      {
        label: 'dropoff_scheduled',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.dropoff_scheduled),
          ),
      },
      {
        label: 'cancellation_attempt',
        value: (row: any) =>
          row.cancellation_attempt && row.cancellation_attempt.length > 0
            ? row.cancellation_attempt[0].date
            : '',
      },
      {
        label: 'expired',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.expired),
          ),
      },
      {
        label: 'Derived Service Description',
        value: (row: any) => PlsReportUtils.getDerivedServiceDescription(row),
      },
      {
        label: 'possible_delay_from_last_mile',
        value: (row: any) =>
          PlsReportUtils.getParcelTrackingStatusDate(
            row,
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.possible_delay_from_last_mile),
          ),
      },
      {
        label: 'Korea Clearance Type',
        value: 'korea_clearance_type',
        default: '',
      },
      {
        label: 'Job Completed',
        value: (row: any) => (row.job_completed ? row.job_completed.status : false),
      },
      {
        label: 'booking platform',
        value: (row: any) => row.booking_platform,
        default: '',
      },
      {
        label: 'destination airport',
        value: (row: any) => row.airport_code || row.destination_airport,
        default: '',
      },
      {
        label: 'recipient_document_type',
        value: (row: any) =>
          row.recipient_document_type && row.recipient_document_type.toUpperCase(),
        default: '',
      },
      {
        label: 'recipient_id',
        value: (row: any) => row.recipient_id,
        default: '',
      },
      {
        label: 'recipient_nationality',
        value: (row: any) => row.recipient_nationality,
        default: '',
      },
      {
        label: 'shipment_type',
        value: (row: any) => row.shipment_type,
        default: '',
      },
      {
        label: 'BoxC Manifest Triggered',
        value: (row: any) => row.boxc_manifest_triggered,
        default: '',
      },
      {
        label: 'Relation Type',
        value: (row: any) => `="${this.getParcelRelationType(row)}"`,
        default: '',
      },
      {
        label: 'Parent ID',
        value: 'parent_id',
        default: '',
      },
      {
        label: 'IM Remarks',
        value: (row) => row.remark,
        default: '',
      },
    ];

    const shipperFields = [
      'shipper_name',
      'shipper_tax_id',
      'shipper_address',
      'shipper_country',
      'shipper_contact',
      'shipper_trademark',
    ];
    fields = fields.concat(this.getItemsFromData(data)).concat(shipperFields);

    return fields;
  },

  convertToLocalMerchantDeclaredValue(data: any) {
    let convertedLocalValue = 0;
    const localCurrency = CountryISOService.getCurrencyByCountry(data.origin_country);
    const {
      id,
      merchant_declared_currency: merchantDeclaredCurrency,
      converted_merchant_declared_value: convertedMerchantDeclaredValue,
    } = data;

    if (convertedMerchantDeclaredValue) {
      if (merchantDeclaredCurrency === localCurrency) {
        return convertedMerchantDeclaredValue;
      }

      const rate = CurrencyConversionService.getExchangeRateFromCache(
        merchantDeclaredCurrency,
        localCurrency,
      );

      if (rate.exchangeRate) {
        convertedLocalValue = Number(convertedMerchantDeclaredValue) * rate.exchangeRate;
      } else {
        logger.error({
          'PXL ID': id,
          message: `Can not find exchange rate from ${merchantDeclaredCurrency} to ${localCurrency}`,
        });
      }
    } else {
      logger.error({
        'PXL ID': id,
        message: `Can not get the value of converted_merchant_declare_value`,
      });
    }

    return convertedLocalValue.toFixed(2);
  },

  exportOperationsReport(data: any, isNewReport = false) {
    const fields = this.getOperationFields(data);

    return this.exportParcelsToCSV(fields, data, isNewReport);
  },

  mapMawbStatus(mawb: any[], arrMawbStatusMapping: any[]) {
    return mawb.map((data) => {
      const { merchantReportStatus } = arrMawbStatusMapping.find((d) => d.status === data.status);

      return {
        status: merchantReportStatus,
        date: data.timestamp || data.date || data.event_datetime,
        receiveStatusDate: data.date || data.timestamp || data.event_datetime,
        raw_message: data.raw_message,
      };
    });
  },

  getItemValue(parcel: any, index: number, itemField: any) {
    if (!parcel || isNaN(index) || !parcel.item || !parcel.item[index]) {
      return null;
    }

    if (itemField !== '') {
      return parcel.item[index][itemField];
    }

    return null;
  },

  getParcelCollected(parcel: any) {
    if (
      parcel.service_option &&
      parcel.service_option.toLowerCase() === 'self-collect' && // NOSONAR
      (!parcel.collection_point_name ||
        (parcel.latest_tracking_status &&
          parcel.latest_tracking_status.toLowerCase() === 'parcel collected'))
    ) {
      return this.getParcelTrackingStatusDate(
        parcel,
        StatusMappingService.getManifestStatus(ENUM.parcelStatus.parcel_collected),
      );
    }

    return null;
  },

  getReadyToInvoiceTimestamp(
    parcel: { PD_invoicing_status: any; DT_invoicing_status: any },
    status: string,
  ) {
    const { PD_invoicing_status, DT_invoicing_status } = parcel;
    let listReadyToInvoiceStatus = [];

    if (status === ENUM.invoiceStatus.DT_ready_to_invoice && DT_invoicing_status) {
      listReadyToInvoiceStatus = DT_invoicing_status.filter(
        (st: { status: string }) => st.status === status,
      );
    }

    if (status === ENUM.invoiceStatus.PD_ready_to_invoice && PD_invoicing_status) {
      listReadyToInvoiceStatus = PD_invoicing_status.filter(
        (st: { status: string }) => st.status === status,
      );
    }

    const { length } = listReadyToInvoiceStatus;

    return length ? listReadyToInvoiceStatus[length - 1].timestamp : '';
  },

  getInvoicedTimestamp(parcel: any, status: any) {
    const { PD_invoicing_status, DT_invoicing_status } = parcel;
    let listInvoicedStatus = [];

    if (status === ENUM.invoiceStatus.DT_invoiced && DT_invoicing_status) {
      listInvoicedStatus = DT_invoicing_status.filter((st: any) => st.status === status);
    }

    if (status === ENUM.invoiceStatus.PD_invoiced && PD_invoicing_status) {
      listInvoicedStatus = PD_invoicing_status.filter((st: any) => st.status === status);
    }

    const { length } = listInvoicedStatus;

    return length ? listInvoicedStatus[length - 1].timestamp : '';
  },

  getDerivedServiceDescription(parcel: {
    operation_hub: string;
    country: string;
    taxZone: string;
  }) {
    const { operation_hub, country, taxZone } = parcel;

    if (!taxZone || taxZone.toLowerCase() === 'hong kong') {
      return 'Cross-Border Delivery';
    }

    if (!taxZone || taxZone.toLowerCase() !== 'singapore') {
      return '';
    }

    const isSing = operation_hub && operation_hub.slice(0, 3) === 'SIN';
    let derived_service_description;

    if (isSing && country && country.toLowerCase() === 'singapore') {
      derived_service_description = 'Domestic Delivery';
    }

    if (
      (isSing && (!country || country.toLowerCase() !== 'singapore')) ||
      (!isSing && operation_hub && country)
    ) {
      // NOSONAR
      derived_service_description = 'Cross-Border Delivery';
    }

    return derived_service_description;
  },

  setRemark(parcel: { id: string; remark?: string }, investigationObj: { [key: string]: string }) {
    parcel.remark = investigationObj[parcel.id] ? investigationObj[parcel.id] : '';
  },

  setGaylord(parcel: { gaylord_no: string; gaylord: any }, gaylords: any[]) {
    if (!parcel.gaylord_no || parcel.gaylord_no === '') {
      return;
    }

    const gaylord = gaylords.find((item) => {
      return parcel.gaylord_no === item.id;
    });
    parcel.gaylord = gaylord;
  },

  setMawbNo(parcel: { gaylord_no: string; gaylord: any; mawb_no: any; mawb: any }, mawbs: any[]) {
    if (!parcel.gaylord_no || parcel.gaylord_no === '') {
      return;
    }

    const { gaylord } = parcel;

    if (gaylord && gaylord.mawb_no) {
      parcel.mawb_no = gaylord.mawb_no;
    }

    if (parcel.mawb_no) {
      const mawb = mawbs.filter((item) => {
        return item._partitionKey === parcel.mawb_no;
      });
      parcel.mawb = mawb;
    }
  },

  setBoxCStatusToParcel(parcel: any) {
    try {
      const { mawb } = parcel;

      if (!Array.isArray(mawb)) {
        return;
      }

      const mawbTriggerd = mawb.find((item) => item.status === ENUM.mawbStatus.boxCTriggered);
      parcel.boxc_manifest_triggered = mawbTriggerd ? mawbTriggerd.date : '';
      parcel.manifest_id = mawbTriggerd ? mawbTriggerd.manifest_id : '';
    } catch (error: any) {
      logger.error({ error: error.message });
    }
  },

  getPointOfDischarge(parcel): string {
    const { mawb } = parcel;

    const mawbNewStatus = mawb?.find((item) => item.tranx_type === ENUM.mawbTransaction.created);

    return mawbNewStatus?.point_of_discharge;
  },

  setGaylordReadyToCloseDatetime(parcel: {
    gaylord: { tracking_status: any[] };
    gaylord_ready_to_close: any;
  }) {
    if (!parcel || !parcel.gaylord || !parcel.gaylord.tracking_status) {
      return;
    }

    const gaylord_ready_to_closes = parcel.gaylord.tracking_status.filter((item) => {
      return item.status === ENUM.gaylordStatus.readyToClosed;
    });

    if (gaylord_ready_to_closes && gaylord_ready_to_closes.length > 0) {
      parcel.gaylord_ready_to_close = gaylord_ready_to_closes.at(-1).date;
    }
  },

  setCloseGaylordDatetime(parcel: { gaylord: { tracking_status: any[] }; close_gaylord: any }) {
    if (!parcel || !parcel.gaylord || !parcel.gaylord.tracking_status) {
      return;
    }

    const close_gaylords = parcel.gaylord.tracking_status.filter((item) => {
      return item.status === ENUM.gaylordStatus.closed;
    });

    if (close_gaylords && close_gaylords.length > 0) {
      parcel.close_gaylord = close_gaylords.at(-1).date;
    }
  },

  getTrackingStatus(tracking_status: any[], status: string) {
    return tracking_status.filter((item) => {
      return (
        (item.status && status && item.status.toLowerCase() === status.toLowerCase()) || // NOSONAR
        (item.external_status &&
          status &&
          item.external_status.toLowerCase() === status.toLowerCase())
      );
    });
  },

  getResolvedStatus(parcel: { isReturned: any; latest_tracking_status: any }) {
    if (parcel.isReturned) {
      return 'True';
    }

    if (
      parcel.latest_tracking_status ===
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.rejected_at_warehouse)
    ) {
      return 'False';
    }

    return 'N/A';
  },

  getParcelTrackingStatusDate(parcel: any, status: string, i?: number) {
    if (!parcel.tracking_status || parcel.tracking_status.length === 0) {
      return;
    }

    const lstStatus = this.getTrackingStatus(parcel.tracking_status, status);

    if (typeof i === 'number') {
      return lstStatus[i] ? lstStatus[i].date : null;
    }

    return lstStatus.at(-1) ? lstStatus.at(-1).date : null;
  },

  getMawbSttAtAirportDate(parcel: { tracking_status: any[] }, mawbStt: string, isTransit: boolean) {
    const depFromOrigin = StatusMappingService.getManifestStatus(
      ENUM.parcelStatus.dep_from_origin_airport,
    );
    const depFromTransit = StatusMappingService.getManifestStatus(
      ENUM.parcelStatus.dep_from_transit_airport,
    );
    const arrAtTransit = StatusMappingService.getManifestStatus(
      ENUM.parcelStatus.arr_at_transit_airport,
    );
    const arrAtDestination = StatusMappingService.getManifestStatus(
      ENUM.parcelStatus.arr_at_destination_airport,
    );
    const mapTransitStatus = {
      [depFromOrigin]: depFromTransit,
      [arrAtDestination]: arrAtTransit,
    };

    try {
      if (!parcel.tracking_status || parcel.tracking_status.length === 0) {
        return;
      }

      const lstStatus = this.getTrackingStatus(parcel.tracking_status, mawbStt);

      if (!lstStatus || lstStatus.length === 0) {
        return;
      }

      let rgx1: RegExp;
      let rgx2: RegExp;

      switch (mawbStt) {
        case StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport): {
          rgx1 = /^\d{3}-\d{8}([A-Z]{3})([A-Z]{3})\/.*/m;
          rgx2 = /^DEP(?:\/[\dA-Z]*){2}\/([A-Z]{3})([A-Z]{3})\/.*/m;
          break;
        }

        case StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport): {
          rgx1 = /^\d{3}-\d{8}[A-Z]{3}([A-Z]{3})\/.*/m;
          rgx2 = /^AR{2}(?:\/[\dA-Z]*){2}\/([A-Z]{3})\/.*/m;
          break;
        }

        default: {
          break;
        }
      }

      const status = lstStatus.filter((s) => {
        const lst1 = rgx1.exec(s.raw_message);
        const lst2 = rgx2.exec(s.raw_message);

        if (!lst1 || lst1.length === 0 || !lst2 || lst2.length === 0) {
          return false;
        }

        return lst1 && lst2 && isTransit ? lst1[1] !== lst2[1] : lst1[1] === lst2[1];
      });

      if (status.length > 0) {
        return status.at(-1)?.date ?? null;
      }

      // Find transit statuses time.
      if (isTransit) {
        const transitStatuses = this.getTrackingStatus(
          parcel.tracking_status,
          mapTransitStatus[mawbStt],
        );

        transitStatuses.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

        return transitStatuses.at(-1)?.date ?? null;
      }
    } catch (error: any) {
      logger.error({
        parcel,
        mawbStt,
        isTransit,
        error,
      });
    }
  },

  getTotalMerchantDeclaredCifValue(parcel: any) {
    let total_merchant_declared_CIF_value = 0;

    if (!parcel.item || !Array.isArray(parcel.item)) {
      return total_merchant_declared_CIF_value;
    }

    for (const _item of parcel.item) {
      const subtotal_declared_value = isNaN(_item.total_declared_value)
        ? 0
        : Number(_item.total_declared_value);
      total_merchant_declared_CIF_value += subtotal_declared_value;
    }

    return total_merchant_declared_CIF_value;
  },

  getGaylordUnitNumber(gaylord: any) {
    return gaylord && gaylord.unitNumber ? gaylord.unitNumber : '';
  },

  formatBatchNo(batchNo: string) {
    if (!batchNo) return null;
    const [batchDate, shipmentType, , merchantAccount] = batchNo.split('-');

    return `${AesUtils.CrtCounterDecrypt(merchantAccount)}${batchDate.slice(2)}${shipmentType[0]}`;
  },

  getItemsFromData(parcels: any[]) {
    const maxLength = this.getMaxItemLength(parcels);
    const items = [];

    for (let i = 0; i < maxLength; i++) {
      const itemNo = i + 1;
      items.push(
        {
          label: `item${itemNo}_description`,
          value: (row: any) => this.getItemValue(row, i, 'description'),
          default: '',
        },
        {
          label: `item${itemNo}_quantity`,
          value: (row: any) => this.getItemValue(row, i, 'quantity'),
          default: '',
        },
        {
          label: `item${itemNo}_sku`,
          value: (row: any) => this.getItemValue(row, i, 'SKU'),
          default: '',
        },
        {
          label: `item${itemNo}_hs_code`,
          value: (row: any) => this.getItemValue(row, i, 'hs_code'),
          default: '',
        },
        {
          label: `item${itemNo}_origin_country`,
          value: (row: any) => this.getItemValue(row, i, 'origin_country'),
          default: '',
        },
        {
          label: `item${itemNo}_category`,
          value: (row: any) => this.getItemValue(row, i, 'category'),
          default: '',
        },
        {
          label: `item${itemNo}_subtotal_declared_value`,
          value: (row: any) => this.getItemValue(row, i, 'total_declared_value'),
          default: '',
        },
        {
          label: `item${itemNo}_converted_declared_value`,
          value: (row: any) => this.getItemValue(row, i, 'converted_subtotal_declared_value'),
          default: '',
        },
        {
          label: `item${itemNo}_subtotal_weight`,
          value: (row: any) => this.getItemValue(row, i, 'subtotal_weight'),
          default: '',
        },
      );
    }

    return items;
  },

  getMaxItemLength(parcels: any[]) {
    let maxLength = 0;

    if (parcels) {
      for (const parcel of parcels) {
        if (parcel.item && parcel.item.length > maxLength) {
          maxLength = parcel.item.length;
        }
      }
    }

    return maxLength;
  },

  getLostDamagedReason(parcel: any, status: string) {
    if (!parcel.tracking_status || parcel.tracking_status.length === 0) {
      return;
    }

    const lstStatus = this.getTrackingStatus(parcel.tracking_status, status);

    return lstStatus.at(-1) ? lstStatus.at(-1).lost_damaged_reason || '' : null;
  },

  async setInvoiceData2Parcels(parcels: any[]) {
    try {
      const invoiceNos = [...new Set(parcels.map((parcel) => parcel.invoice_no).filter(Boolean))];
      const invoices = await Daos.invoice.find(QueryUtils.getInvoicesByInvoiceNosQuery(invoiceNos));

      if (invoices && invoices.length > 0) {
        for (const parcel of parcels) {
          const foundInvoice = invoices.find(
            (invoice: any) => invoice.invoiceNumber === parcel.invoice_no,
          );

          if (foundInvoice) {
            parcel.AR_Posted = foundInvoice.AR_Posted;
          }
        }
      }
    } catch (error: any) {
      logger.error({ error });
    }
  },

  /**
   * @author: HaiNN27
   * @description: get date received at warehouse till today
   * @param {*} row: datetime reach status received_at_warehouse
   * @returns number or null
   */
  getParcelAge(row: any) {
    try {
      let today = new Date();
      today = new Date(today);
      // received at warehouse date
      let rawDate = this.getParcelTrackingStatusDate(
        row,
        StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
      );

      if (!rawDate) {
        return null;
      }

      rawDate = new Date(rawDate.split('T')[0]);
      const diff = Math.abs(today.getTime() - rawDate.getTime());
      const parcelAge = Math.ceil(diff / (1000 * 60 * 60 * 24)) - 1;

      if (parcelAge === 0 || parcelAge < 0) {
        return null;
      }

      return parcelAge;
    } catch (error: any) {
      logger.error({ message: `Cannot get parcel age: ${error.message}` });
    }
  },

  async mapDestinationAirportToParcel(parcels: any[]) {
    const destinationGroupIds = [
      ...new Set(parcels.map((item) => item.destination_group).filter(Boolean)),
    ];
    const destinationAirport = await DestinationService.getDestinationAirport(destinationGroupIds);

    for (const parcel of parcels)
      parcel.destination_airport = destinationAirport[parcel.destination_group] || '';
  },

  mapMawbStatuses(trackingStatus: ManifestItemStatus[], mawbStt: any[]) {
    const arrMawbStatusMapping = [
      {
        status: 'FWB',
        merchantReportStatus: StatusMappingService.getManifestStatus(
          ENUM.parcelStatus.fwb_received,
        ),
      },
      {
        status: 'RCS',
        merchantReportStatus: StatusMappingService.getManifestStatus(
          ENUM.parcelStatus.rcs_received_by_airline,
        ),
      },
      {
        status: 'DEP',
        merchantReportStatus: StatusMappingService.getManifestStatus(
          ENUM.parcelStatus.dep_from_origin_airport,
        ),
      },
      {
        status: 'ARR_TRANSIT',
        merchantReportStatus: StatusMappingService.getManifestStatus(
          ENUM.parcelStatus.arr_at_transit_airport,
        ),
      },
      {
        status: 'DEP_TRANSIT',
        merchantReportStatus: StatusMappingService.getManifestStatus(
          ENUM.parcelStatus.dep_from_transit_airport,
        ),
      },
      {
        status: 'ARR',
        merchantReportStatus: StatusMappingService.getManifestStatus(
          ENUM.parcelStatus.arr_at_destination_airport,
        ),
      },
      {
        status: 'DLV',
        merchantReportStatus: StatusMappingService.getManifestStatus(
          ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
        ),
      },
    ];
    let mawb = mawbStt.filter((mawbStatus) =>
      arrMawbStatusMapping.some((item) => item.status === mawbStatus.status),
    );
    mawb = this.mapMawbStatus(mawb, arrMawbStatusMapping);
    trackingStatus.push(...mawb);

    return trackingStatus;
  },

  /**
   * Combine parcel statuses and mawb statuses and sort by date
   * @param parcelStatus
   * @param mawbStatus
   * @returns list of statuses filtered
   */
  combineParcelStatusesAndMawbStatuses(parcelTrackingStatus: any[], mawbStt: any[]) {
    // Map tracking status
    let trackingStatus = parcelTrackingStatus.map((item) => {
      return { status: item.status, date: item.timestamp || item.event_datetime || item.date };
    });

    if (Array.isArray(mawbStt)) {
      trackingStatus = this.mapMawbStatuses(trackingStatus, mawbStt);
    }

    return sortBy(trackingStatus, ['date']);
  },

  combineStatusesForPLSSearch(parcelTrackingStatus: ManifestItemStatus[], mawbStt: any[]) {
    // Map tracking status
    let trackingStatus: ManifestItemStatus[] = parcelTrackingStatus.map((item) => {
      if (item.status === ENUM.gaylordStatus.closed) {
        return {
          status: StatusMappingService.getManifestStatus(ENUM.parcelStatus.close_gaylord),
          date: item.timestamp || item.event_datetime || item.date,
          receiveStatusDate: item.date || item.timestamp || item.event_datetime,
          description: item.description,
          statusLocation: item.statusLocation,
        };
      }

      return {
        status: item.status,
        date: item.timestamp || item.event_datetime || item.date,
        receiveStatusDate: item.date || item.timestamp || item.event_datetime,
        description: item.description,
        statusLocation: item.statusLocation,
      };
    });

    if (Array.isArray(mawbStt)) {
      trackingStatus = this.mapMawbStatuses(trackingStatus, mawbStt);
    }

    return sortBy(trackingStatus, ['date']);
  },

  /**
   * Filter statuses by sequence number and combine parcel statuses and mawb statuses
   * @param parcel
   * @param sequenceList
   * @returns list of statuses filtered
   */
  filterStatuses(parcel: any, sequenceList: any) {
    try {
      if (!parcel?.tracking_status?.length) {
        return;
      }

      const result = [];
      const statuses = this.combineParcelStatusesAndMawbStatuses(
        parcel.tracking_status,
        parcel.mawb,
      );
      const receiveAtWarehouseStt = StatusMappingService.getManifestStatus(
        ENUM.parcelStatus.received_at_warehouse,
      );
      let isReceivedAtWarehouseSttPushed = false;
      let maxItemSequence = 0;

      for (const item of statuses) {
        if (result.length === 0) {
          result.push(item);
        } else {
          const currentItemSequence = sequenceList.find(
            (ob: any) => ob.manifestStt === item.status,
          );

          if (
            !currentItemSequence ||
            (isReceivedAtWarehouseSttPushed && item.status === receiveAtWarehouseStt)
          )
            continue;

          if (item.status === receiveAtWarehouseStt) {
            result.push(item);
            maxItemSequence = Math.max(maxItemSequence, currentItemSequence.sequence);
            isReceivedAtWarehouseSttPushed = true;
            continue;
          }

          if (maxItemSequence <= currentItemSequence.sequence) {
            maxItemSequence = Math.max(maxItemSequence, currentItemSequence.sequence);
            result.push(item);
          }
        }
      }

      parcel.tracking_status = result;
    } catch (error: any) {
      logger.error({ error, parcelId: parcel.id });
    }
  },

  filterShipmentsBasedOnMilestone({ milestoneField, shipmentsParam, milestone, mapMawbStts }) {
    let shipments = shipmentsParam;
    shipments = shipments.filter((shipment: any) => {
      let unsuccessfulDeliveryTimes = 0;

      if (Array.isArray(shipment.tracking_status)) {
        for (const statusObj of shipment.tracking_status) {
          if (
            statusObj.status !==
              StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful) &&
            milestone[milestoneField].includes(statusObj.status)
          ) {
            return true;
          }

          if (
            statusObj.status ===
            StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful)
          ) {
            unsuccessfulDeliveryTimes++;
          }
        }
      }

      if (unsuccessfulDeliveryTimes >= 1 && unsuccessfulDeliveryTimes <= 3) return true;

      if (shipment.mawb && shipment.mawb.length > 0) {
        for (const shipmentMawb of shipment.mawb) {
          if (
            milestone[milestoneField].includes(shipmentMawb.status) ||
            milestone[milestoneField].includes(mapMawbStts[shipmentMawb.status])
          ) {
            return true;
          }
        }
      }

      return false;
    });

    return shipments;
  },

  filterExceptionStatusOnly(shipments: any) {
    const exceptionStatuses = new Set([
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.hold_rejected_by_lmd),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.hold_hs_missing),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.hold_dims_missing),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.hold_hs_dims_missing),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.hold_cancelled),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.hold_split_parcel),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.lost),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.damaged),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.damage_internal),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.dg_internal),
    ]);

    return shipments.filter((shipment) =>
      shipment.tracking_status.some((tracking) => exceptionStatuses.has(tracking.status)),
    );
  },

  validateShipments(
    originShipments: any,
    selectedDestinationCountry = null,
    selectedMilestone = null,
    mawb = null,
    isExceptionOnly = false,
  ) {
    const milestone = {} as any;
    let shipments = originShipments;

    if (mawb) {
      shipments = shipments.filter((shipment: { mawb_no: any }) => {
        return mawb?.[shipment.mawb_no];
      });
    }

    if (selectedDestinationCountry) {
      shipments = shipments.filter((shipment: { country: any }) => {
        return shipment.country === selectedDestinationCountry;
      });
    }

    const mapMawbStts = {
      FWB: StatusMappingService.getManifestStatus(ENUM.parcelStatus.fwb_received),
      RCS: StatusMappingService.getManifestStatus(ENUM.parcelStatus.rcs_received_by_airline),
      DEP: StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
      DEP_TRANSIT: StatusMappingService.getManifestStatus(
        ENUM.parcelStatus.dep_from_transit_airport,
      ),
      ARR_TRANSIT: StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_transit_airport),
      ARR: StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
    };
    milestone['Customs Cleared'] = [
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.custom_cleared),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.enroute_to_destination_sorting_hub),
      StatusMappingService.getManifestStatus(
        ENUM.parcelStatus.arrived_and_processing_at_sorting_hub,
      ),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.in_transit),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.awaiting_collection),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
    ];
    milestone['DLV Shipment handed over to Consignee'] = [
      StatusMappingService.getManifestStatus(
        ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
      ),
      ...milestone['Customs Cleared'],
    ];
    milestone['Arrived at Destination Airport'] = [
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_transit_airport),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
      ...milestone['DLV Shipment handed over to Consignee'],
    ];
    milestone['Received at Warehouse'] = [
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.rcs_received_by_airline),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.fwb_received),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_origin_airport),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.dep_from_transit_airport),
      StatusMappingService.getManifestStatus(
        ENUM.parcelStatus.arrived_at_destination_custom_facility,
      ),
      ...milestone['Arrived at Destination Airport'],
    ];
    milestone['First Attempt'] = [
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.awaiting_collection),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.delivery_unsuccessful),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.on_vehicle_for_delivery),
    ];

    const canChooseMilestones = [
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.arr_at_destination_airport),
      StatusMappingService.getManifestStatus(
        ENUM.parcelStatus.dlv_shipment_handed_over_to_consignee,
      ),
      StatusMappingService.getManifestStatus(ENUM.parcelStatus.custom_cleared),
      'First Attempt',
    ];

    if (canChooseMilestones.includes(selectedMilestone)) {
      shipments = this.filterShipmentsBasedOnMilestone({
        milestoneField: selectedMilestone,
        shipmentsParam: shipments,
        milestone,
        mapMawbStts,
      });
    }

    if (isExceptionOnly) {
      shipments = this.filterExceptionStatusOnly(shipments);
    }

    return shipments;
  },

  exportRebookingReport(data: any, isNewReport = false) {
    const fields = [
      {
        label: 'PXL ID',
        value: (row: any) => (row.id ? `="${row.id}"` : ''),
      },
      {
        label: 'Tracking ID',
        value: (row: any) => (row.trackingId ? `="${row.trackingId}"` : ''),
      },
      {
        label: 'New Tracking ID',
        value: (row: any) => (row.newTrackingId ? `="${row.newTrackingId}"` : ''),
      },
      {
        label: 'Destination Group',
        value: (row: any) => (row.destinationGroup ? `="${row.destinationGroup}"` : ''),
      },
      {
        label: 'New Destination Group',
        value: (row: any) => (row.newDestinationGroup ? `="${row.newDestinationGroup}"` : ''),
      },
      {
        label: 'Error message',
        value: (row: any) => (row.message ? `="${row.message}"` : ''),
      },
    ];

    return this.exportParcelsToCSV(fields, data, isNewReport);
  },

  changeInvoiceFormatDateTime(parcel: any, status: string) {
    let invoiceTime: string;

    if (
      status === ENUM.invoiceStatus.DT_ready_to_invoice ||
      status === ENUM.invoiceStatus.PD_ready_to_invoice
    ) {
      invoiceTime = this.getReadyToInvoiceTimestamp(parcel, status);
    }

    if (status === ENUM.invoiceStatus.DT_invoiced || status === ENUM.invoiceStatus.PD_invoiced) {
      invoiceTime = this.getInvoicedTimestamp(parcel, status);
    }

    const convertedTime = invoiceTime ? invoiceTime.split('-') : [];

    return convertedTime.length > 0 ? `${convertedTime[1]}-${convertedTime[0]}` : 'N/A';
  },

  calculateDtPayableConvertedValue(parcel: any) {
    const destinationCurrency = CountryISOService.getCurrencyByCountry(parcel.country);
    // rate for convert for DT_manual_tax to destination currency
    const exchangeRateForDTManualTax = parcel.DT_manual_tax?.currency
      ? CurrencyConversionService.getExchangeRateFromCache(
          parcel.DT_manual_tax?.currency,
          destinationCurrency,
        )
      : CurrencyConversionService.getExchangeRateFromCache(
          parcel.merchant_declared_currency,
          destinationCurrency,
        );

    const manualUploadTaxOfParcel = parcel.DT_manual_tax
      ? (+parcel.DT_manual_tax.tax_value ||
          (sumBy(parcel.item, (parcelItem: any) => +parcelItem.total_declared_value) *
            +parcel.DT_manual_tax.rate_percent) /
            100 ||
          0) * exchangeRateForDTManualTax.exchangeRate
      : 0;
    // rate for convert from invoice currency to destination currency
    const rate = CurrencyConversionService.getExchangeRateFromCache(
      parcel.invoiceRateCurrency,
      destinationCurrency,
    );
    // rate for convert from CAD currency to destination currency
    const dutySalesTaxRate = CurrencyConversionService.getExchangeRateFromCache(
      'CAD',
      destinationCurrency,
    );
    const totalTaxFromItemOfParcel = parcel.item.reduce(
      (total, item) =>
        total +
        (Number(item.GST_value || 0) + Number(item.others_tax || 0)) * rate.exchangeRate +
        (Number(item.sales_tax || 0) + Number(item.duty_tax || 0)) * dutySalesTaxRate.exchangeRate,
      0,
    );

    return Number(manualUploadTaxOfParcel + totalTaxFromItemOfParcel).toFixed(2);
  },

  getShipperFields() {
    return [
      {
        label: 'shipper_name',
        value: (row) => row.shipper_name || '',
      },
      {
        label: 'shipper_address',
        value: (row) =>
          [row.shipper_address, row.shipper_country, row.shipper_contact]
            .filter(Boolean)
            .join(', '),
      },
      {
        label: 'declaration',
        value: (row) => row.declaration || '',
      },
    ];
  },

  getParcelRelationType(parcel: any): string {
    if (!parcel.parent_id) return ENUM.PARCEL_RELATION_TYPE.INDIVIDUAL;

    if (parcel.id === parcel.parent_id) return ENUM.PARCEL_RELATION_TYPE.PARENT;

    return ENUM.PARCEL_RELATION_TYPE.CHILD;
  },

  formatInvestigationParcels(investigationList: IInvestigationItem[]): {
    [key: string]: string;
  } {
    const result = {};

    for (const item of investigationList) {
      const latestRemark = item.remarks.length > 0 ? item.remarks.at(-1).message : '';
      result[item.id] = latestRemark;
    }

    return result;
  },
};
