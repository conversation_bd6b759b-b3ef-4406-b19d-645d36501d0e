export function separateThousands(input: any): string {
  return Number(input).toLocaleString('en-US', { maximumFractionDigits: 2 });
}

const numberFormatter = Intl.NumberFormat('en-US', {
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

export function separateThousandsWith2DecimalPlace(input: number): string {
  return numberFormatter.format(input);
}

export function roundNumberBaseOnCurrency(input: string | number, currency: string) {
  if (!input || isNaN(Number(input))) {
    return 0;
  }

  const numInput = Number(input);

  // Helper function to count the number of decimals in a number
  const countDecimals = (num) => {
    const str = num.toString();
    const decimalIndex = str.indexOf('.');

    return decimalIndex === -1 ? 0 : str.length - decimalIndex - 1;
  };

  if (['SGD', 'GBP', 'AUD', 'MYR'].includes(currency)) {
    // For SGD, GBP, AUD, and MYR, round up to the nearest 0.01
    if (countDecimals(numInput) <= 2) {
      return numInput;
    }

    return Math.ceil(numInput * 100) / 100;
  }

  if (currency === 'HKD') {
    // For HKD currency, round to the nearest 0.01
    if (countDecimals(numInput) <= 2) {
      return numInput;
    }

    return Math.round(numInput * 100) / 100;
  }

  if (currency === 'KRW') {
    // For KRW currency, round up to the nearest 1
    return Math.ceil(numInput);
  }

  if (currency === 'TWD' || currency === 'JPY') {
    // For TWD and JPY currency, round to nearest 1
    return Math.round(numInput);
  }

  // For other currencies, round up to the nearest 0.05
  const roundedValue = Math.ceil(numInput * 20) / 20;

  return +roundedValue.toFixed(2);
}

export function separateThousandsBaseOnCurrency(input: number | string, currency?: string) {
  if (currency === 'KRW' || currency === 'TWD' || currency === 'JPY') {
    return Number(input).toLocaleString('en-US', { maximumFractionDigits: 0 });
  }

  return separateThousandsWith2DecimalPlace(Number(input));
}
