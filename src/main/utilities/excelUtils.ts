import XLSX from 'xlsx';

export interface IField {
  label: string;
  value?: string | any;
  default?: string;
}

export class ExcelUtils {
  static generateSheetData(items: any, fields: IField[]): string[][] {
    const wsData = [];

    // Header row
    wsData.push(fields.map((field: any) => field.label));

    // Data rows
    if (Array.isArray(items) && items.length > 0) {
      for (const item of items) {
        const rowData = fields.map((field: any) => {
          const { value, default: defaultValue = '' } = field;

          if (!value) {
            return defaultValue;
          }

          if (typeof value !== 'function') {
            return item[value] === undefined ? defaultValue : item[value];
          }

          return value(item);
        });
        wsData.push(rowData);
      }
    }

    return wsData;
  }

  /**
   * Create Excel file as buffer
   * @param sheetName
   * @param items
   * @param fields
   * @return Buffer
   */
  static createBuffer(
    sheetName: string,
    items: any,
    fields: IField[],
    header?: Array<string[]>,
    merge = undefined,
  ): Buffer {
    const sheetData = this.generateSheetData(items, fields);
    const wsData = header ? header.concat(sheetData) : sheetData;
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    if (merge) {
      ws['!merges'] = merge;
    }

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, sheetName);

    return XLSX.write(wb, { type: 'buffer' });
  }

  static createBufferXLSX(sheetName: string, wsData: unknown[][]) {
    const ws = XLSX.utils.aoa_to_sheet(wsData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, sheetName);

    return XLSX.write(wb, { type: 'buffer' });
  }

  static getJsonFromFilepath(filePath: string) {
    const workbook = XLSX.readFile(filePath);
    const first_sheet_name = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[first_sheet_name];

    return XLSX.utils.sheet_to_json(worksheet, { raw: false });
  }

  static getJsonFromExcelFileBuffer(buffer: Buffer): unknown[] {
    const workbook = XLSX.read(buffer);
    const first_sheet_name = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[first_sheet_name];

    return XLSX.utils.sheet_to_json(worksheet, { raw: false });
  }

  static showValueByCurrency(value: number, currency: string) {
    if (currency === 'KRW' || currency === 'TWD' || currency === 'JPY') {
      return { v: value, z: '#,##0', t: 'n' };
    }

    return { v: value, z: '#,##0.00', t: 'n' };
  }
}
