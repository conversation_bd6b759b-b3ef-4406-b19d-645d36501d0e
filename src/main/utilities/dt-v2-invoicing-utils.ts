import { format, subDays } from 'date-fns';
import filter from 'lodash/filter.js';
import groupBy from 'lodash/groupBy.js';
import sumBy from 'lodash/sumBy.js';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';
import { AzureStorageQueue } from '~/common/azure/azure-storage-queue.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';
import { ParcelUtils } from '~/models/parcelUtils.js';
import { ReportUtils } from '~/models/reportUtils.js';

import CountryISOService from '~/services/countryISO-service.js';
import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { ManifestItemService } from '~/services/manifest-item.service.js';
import { RateServices } from '~/services/rate-services.js';

import { config } from '~/configs/index.js';
import { CONSTANTS } from '~/constants/index.js';
import { Daos } from '~/daos/index.js';

import { CommonUtils } from './commonUtils.js';
import { DateUtils } from './dateUtils.js';
import { DtPdfInvoiceUtils } from './DtPdfInvoiceUtils.js';
import { EmailUtils } from './emailUtils.js';
import { InvoiceUtils } from './invoiceUtils.js';
import { AppLogger } from './logUtils.js';
import { roundNumberBaseOnCurrency } from './numberUtils.js';
import { JSZipUtils } from './zipUtils.js';

const logger = new AppLogger({
  name: 'DtV2InvoicingUtils',
  business: ENUM.FunctionName.INVOICE_DT_V2,
});

export const DtV2InvoicingUtils = {
  /**
   *
   * @param {manifestItemsController} manifestItemsController
   * @param {*} timeToInvoice
   * @param {*} bankDetails
   * @param {*} pdfTaxNameDisplay
   * @param {*} isWebJob
   * @param {*} merchantName
   */
  async handleDTInvoicing(
    timeToInvoice,
    bankDetails,
    taxCountryManagementInfo,
    isWebJob = false,
    merchants = [],
  ) {
    const neededParcelFields = [
      'id',
      'invoiceRateCurrency',
      'DT_manual_tax',
      'origin_country',
      'country',
      'merchant_order_no',
      'postcode',
      'state',
      'city_suburb',
      'tracking_id',
      'item',
      'shipment_type',
      'merchant_declared_currency',
      'sales_tax_currency',
      'duty_tax_currency',
      'DT_invoicing_status',
      'incoterm',
      'service_option',
    ];

    if (isWebJob) {
      merchants =
        timeToInvoice.getDate() === 1
          ? merchants.filter(
              (item) =>
                item.auto_DT_invoice !== false &&
                (!item.invoice_run_date ||
                  item.invoice_run_date === CONSTANTS.RUN_DATE.FIRST_OF_MONTH),
            )
          : merchants.filter(
              (item) =>
                item.auto_DT_invoice !== false &&
                item.invoice_run_date === CONSTANTS.RUN_DATE.END_OF_MONTH,
            );
    }

    if (merchants.length === 0) {
      logger.info({ message: 'Not found merchants' });

      return;
    }

    for (const merchant of merchants) {
      try {
        const y = timeToInvoice.getFullYear();
        const m = timeToInvoice.getMonth();
        const d = timeToInvoice.getDate();
        const startDayCountInvoice =
          d === 1 ? new Date(Date.UTC(y, m - 1, 0)) : new Date(Date.UTC(y, m, 0));

        // eslint-disable-next-line no-await-in-loop
        const [invoiceNumber, invoiceCurrency] = await Promise.all([
          ManifestItemService.getInvoiceNumber(
            merchant.merchant_account_number,
            config.invoice_type.dt,
          ),
          RateServices.getMerchantRateCurrency(merchant.merchant_account_number),
        ]);

        if (!invoiceCurrency) {
          logger.info({
            message: 'Get merchant invoice currency',
            merchantName: merchant.merchant_name,
            invoiceNumber,
          });
          continue;
        }

        // eslint-disable-next-line no-await-in-loop
        const parcelsDTReadyToInvoice = await ManifestItemService.getDutiesAndTaxesParcels(
          timeToInvoice,
          startDayCountInvoice,
          merchant,
          neededParcelFields,
        );

        // eslint-disable-next-line no-await-in-loop
        const invoiceData: any = await this.getMerchantInvoiceByRoutes(
          parcelsDTReadyToInvoice,
          merchant,
          invoiceCurrency,
          ENUM.invoiceType.DT,
          subDays(new Date(timeToInvoice), 1),
        );
        logger.info({
          message: `Calculate done with ${invoiceData.parcels?.length || 0} success, ${invoiceData.errorParcels} error`,
          merchantName: merchant.merchant_name,
          invoiceNumber,
        });

        if (!invoiceData.routes?.length) {
          logger.info({
            message: 'Merchant does not have any parcels to invoice',
            merchantName: merchant.merchant_name,
            invoiceNumber,
          });
          continue;
        }

        invoiceData.invoiceCurrency = invoiceCurrency;

        const merchantInvoiceData = this.getMerchantInvoiceData(merchant);
        merchantInvoiceData.invoiceNumber = invoiceNumber;

        const invoiceDate = this.generateInvoiceDate(timeToInvoice, startDayCountInvoice);
        const bankDetail = InvoiceUtils.getMerchantBankDetail(merchant, bankDetails);

        logger.info({
          message: 'Generate PDF',
          merchantName: merchant.merchant_name,
          invoiceNumber,
        });
        const pdfBuffer = await DtPdfInvoiceUtils.generateDtInvoiceUsingPDFKit(
          merchantInvoiceData,
          invoiceData,
          invoiceDate,
          bankDetail,
          taxCountryManagementInfo,
        );

        await this.injectParcelRateZone(invoiceData.parcels);

        logger.info({
          message: 'Generate CSV',
          merchantName: merchant.merchant_name,
          invoiceNumber,
        });
        const xlsxBuffer = ReportUtils.createDTExcelBuffer(
          invoiceData,
          merchantInvoiceData,
          invoiceNumber,
        );

        logger.info({
          message: 'Send merchant email',
          merchantName: merchant.merchant_name,
          invoiceNumber,
        });
        await this.sendMerchantEmail(xlsxBuffer, pdfBuffer, merchant, invoiceNumber);

        logger.info({
          message: 'Queue parcel invoice statuses update',
          merchantName: merchant.merchant_name,
          invoiceNumber,
        });
        this.updateParcelInvoiceStatus(
          invoiceData.parcels,
          invoiceNumber,
          invoiceCurrency,
          merchant.merchant_account_number,
        );

        this.saveInvoiceToDB(invoiceData, merchantInvoiceData, invoiceDate).catch((error) => {
          logger.error({
            caller: 'handleDTInvoicing',
            message: 'Save invoice to DB',
            invoiceNumber,
            error,
          });
        });
      } catch (error) {
        logger.error({
          message: 'Unexpected error while invoicing merchant',
          merchantId: merchant.id,
          error,
        });
      }
    }
  },

  async updateParcelInvoiceStatus(
    successParcels: any,
    invoiceNumber: string,
    invoiceCurrency: string,
    merchantAccountNumber: string,
  ) {
    for (const parcels of CommonUtils.chunkArrayGenerator(
      successParcels,
      CONSTANTS.NUMBER_OF_PARCEL_MESSAGES_TO_QUEUE,
    )) {
      const result = await Promise.allSettled(
        parcels.map((parcel) => {
          const updatePayload = {
            id: parcel.id,
            DT_invoice_no: invoiceNumber,
            DT_invoicing_status: [
              {
                status: ENUM.invoiceStatus.DT_invoiced,
                timestamp: new Date(),
              },
            ],
            DT_invoice_currency: invoiceCurrency,
            DT_invoice_amount: parcel.totalActualTax,
          };

          return AzureStorageQueue.sendBase64Message(
            CONSTANTS.AZURE_STORAGE_QUEUE_NAME.PARCELS_UPDATE_AFTER_INVOICING,
            {
              updatePayload,
              invoiceType: ENUM.invoiceType.DTv2,
              merchantAccountNumber,
            },
          );
        }),
      );

      for (const [idx, item] of result.entries()) {
        if (item.status === 'rejected') {
          logger.error({
            message: 'Failed queue update parcels status after invoicing',
            parcelId: parcels[idx].id,
            invoiceNumber,
            reason: item.reason,
          });
        }
      }
    }
  },

  saveInvoiceToDB(invoiceData, merchantInvoiceData, invoiceDate) {
    const timeToSaveInvoice = new Date();
    const { invoicingInfo } = merchantInvoiceData;
    const dutyAndTaxRoutes = filter(invoiceData.routes, [
      'chargeType',
      ENUM.invoiceChargeType.dutyAndTax,
    ]);

    const groupedRoutes = groupBy(dutyAndTaxRoutes, 'route');
    const routings = Object.entries(groupedRoutes).map(([key, value]) => ({
      route: key,
      service_options: value.map((item) => ({
        service_option: item.serviceOption,
        local_total: item.totalActualTax,
      })),
      taxCode: value[0].taxCode,
      totalTax: value.reduce((total, item) => total + (item.totalActualTax || 0), 0),
      totalTaxInSGD: value.reduce((total, item) => total + (item.totalActualTaxInSGD || 0), 0),
    }));

    const surcharges = invoiceData.routes.reduce((result, route) => {
      if (route.chargeType === ENUM.invoiceChargeTypeDBValue.SURCHARGE) {
        delete route.chargeType;
        result.push(route);
      }

      return result;
    }, []);

    const info = {
      merchant_no: AesUtils.CrtCounterEncrypt(merchantInvoiceData.merchantAccountNo),
      merchant_name: AesUtils.CrtCounterEncrypt(merchantInvoiceData.merchant_name),
      merchant_invoice_name: AesUtils.CrtCounterEncrypt(merchantInvoiceData.merchant_invoice_name),
      merchant_address_line1: AesUtils.CrtCounterEncrypt(merchantInvoiceData.address_line_1),
      merchant_address_line2: AesUtils.CrtCounterEncrypt(merchantInvoiceData.address_line_2),
      merchant_address_line3: AesUtils.CrtCounterEncrypt(merchantInvoiceData.address_line_3),
      invoiceNumber: merchantInvoiceData.invoiceNumber,
      invoice_year: timeToSaveInvoice.getFullYear(),
      merchant_type: invoicingInfo?.tax?.constParty,
      taxZone: invoicingInfo?.tax?.country,
      invoice_gen_date: timeToSaveInvoice,
      _partitionKey: AesUtils.CrtCounterEncrypt(merchantInvoiceData.merchantAccountNo),
      type: 'dutiesAndTaxesV2',
      routings,
      surcharges,
      start_date: invoiceDate.start_date,
      end_date: invoiceDate.end_date,
      total_due: invoiceData.totalDue,
      currency: invoiceData.invoiceCurrency,
      creditTerm: merchantInvoiceData.creditTerm,
      invoiceMonth: invoiceDate.date_range,
    };

    return Daos.invoice.addItem(info);
  },

  async injectParcelRateZone(parcels: any) {
    const res = await RateServices.getRateZone(parcels);
    const resMap = new Map();

    for (const parcelRateZone of res) {
      resMap.set(parcelRateZone.id, parcelRateZone.name);
    }

    parcels.forEach((parcel: any) => (parcel.rateZone = resMap.get(parcel.id)));
  },

  getMerchantInvoiceData(merchant: any): any {
    const {
      merchant_address_line1: merchantAddressLine1,
      merchant_address_line2: merchantAddressLine2,
      merchant_address_line3: merchantAddressLine3,
    } = InvoiceUtils.getInvoiceAddress(merchant.invoice_address, false);

    const {
      merchant_name: merchantName,
      merchant_invoice_name: merchantInvoiveName,
      merchant_account_number: merchantAccountNumber,
      invoicing_info: invoicingInfo,
      taxDisplay,
      phone_number: phoneNumber,
    } = merchant;

    const creditTerm = invoicingInfo?.tax?.creditTerm || 'Immediate';
    const countryCode = DtPdfInvoiceUtils.getMerchantInvoicingCountryCode(merchant);
    const taxCountry = invoicingInfo?.tax?.country || '';

    return {
      merchant_name: merchantName,
      merchant_invoice_name: merchantInvoiveName,
      address_line_1: merchantAddressLine1,
      address_line_2: merchantAddressLine2,
      address_line_3: merchantAddressLine3,
      creditTerm,
      countryCode,
      invoiceName: merchantInvoiveName || merchantName,
      merchantAccountNo: merchantAccountNumber,
      invoicingInfo,
      taxType: taxDisplay?.taxType,
      standardRated: taxDisplay?.SR,
      taxCountry,
      phoneNumber,
    };
  },

  sumTotalActualTax(routes: any[]) {
    let totalTaxInMerchantRateCurrency = 0;
    let totalSurcharge = 0;
    let totalSurchargeTax = 0;

    for (const route of routes) {
      totalTaxInMerchantRateCurrency += route.totalActualTax || 0;
      totalSurchargeTax += route.tax || 0;
      totalSurcharge += route.rate || 0;
    }

    const totalDue = totalTaxInMerchantRateCurrency + totalSurcharge + totalSurchargeTax;

    return {
      totalTaxInMerchantRateCurrency,
      totalSurcharge,
      totalSurchargeTax,
      totalDue,
    };
  },

  /**
   *
   * @param {manifestItemsController} manifestItemsController
   * @param {*} merchant
   * @param {*} timeToInvoice
   * @returns
   */
  async getMerchantInvoiceByRoutes(
    parcelsReadyToInvoice,
    merchant,
    invoiceCurrency,
    invoiceType,
    invoiceMonth: Date,
  ) {
    const {
      merchant_name: merchantName,
      invoicing_info: invoicingInfo,
      taxCodes: merchantTaxCodes,
      taxType: merchantTaxType,
      tax_registered_id_list: taxRegisteredIdList,
    } = merchant;
    const isCalculateDDPForMerchant = invoicingInfo?.surcharges?.hasAdminCharge;
    const ddpSurchageRate = Number(invoicingInfo?.surcharges?.adminCharge || 0);

    if (parcelsReadyToInvoice.length === 0) {
      logger.error({
        message: `No parcel found for merchant when gen ${invoiceType}`,
        merchantName,
      });

      return {};
    }

    // Get list of unique exchange rates
    const currencyPairUnique = [
      ...new Set(
        parcelsReadyToInvoice.flatMap((parcel: any) => {
          const pairs = [`${parcel.invoiceRateCurrency}-SGD`];

          if (parcel.sales_tax_currency) {
            pairs.push(`${parcel.sales_tax_currency}-${parcel.invoiceRateCurrency}`);
          }

          if (parcel.duty_tax_currency) {
            pairs.push(`${parcel.duty_tax_currency}-${parcel.invoiceRateCurrency}`);
          }

          if (parcel.DT_manual_tax?.currency) {
            pairs.push(`${parcel.DT_manual_tax?.currency}-${parcel.invoiceRateCurrency}`);
          }

          if (parcel.DT_manual_tax?.rate_percent) {
            pairs.push(`${parcel.merchant_declared_currency}-${parcel.invoiceRateCurrency}`);
          }

          return pairs;
        }),
      ),
    ] as string[];

    const exchangeRateList = new Map();

    for (const pair of currencyPairUnique) {
      const fromCurrency = pair.slice(0, pair.indexOf('-'));
      const toCurrency = pair.slice(pair.indexOf('-') + 1);
      const res = await CurrencyConversionService.getExchangeRate(
        fromCurrency,
        toCurrency,
        invoiceMonth,
      );
      exchangeRateList.set(pair, res?.exchangeRate);
    }

    // Create routes
    const routes = {} as any;
    let errorParcels = 0;
    const successParcels = [];

    for (const parcel of parcelsReadyToInvoice) {
      try {
        // Calculate gst value for parcel
        if (
          parcel.country === 'Singapore' &&
          parcel.shipment_type === ENUM.shipmentType.international &&
          parcel.item?.length &&
          parcel.incoterm?.toLowerCase() === 'ddp'
        ) {
          if (!parcel.item[0].converted_subtotal_declared_value) {
            // eslint-disable-next-line no-await-in-loop
            await ParcelUtils.calculateConvertedCurrency(parcel, invoiceMonth);
          }

          // eslint-disable-next-line no-await-in-loop
          await ParcelUtils.calculateGSTValue(parcel, taxRegisteredIdList, invoiceMonth);
        }

        // invoice only parcel that has tax
        if (
          Array.isArray(parcel.item) &&
          parcel.item.length > 0 &&
          parcel.item.every((i) => !i.sales_tax && !i.duty_tax && !i.others_tax && !i.GST_value) &&
          !parcel.DT_manual_tax
        ) {
          parcel.error = `There is no tax to calculate for ${parcel.id}`;
          continue;
        }

        parcel.country = CountryISOService.getCountryCode2(parcel.country);

        // Get exchange rate
        const exchangeRateForSalesTax = exchangeRateList.get(
          `${parcel.sales_tax_currency}-${parcel.invoiceRateCurrency}`,
        );
        const exchangeRateForDutyTax = exchangeRateList.get(
          `${parcel.duty_tax_currency}-${parcel.invoiceRateCurrency}`,
        );
        const exchangeRateForDTManualTax = parcel.DT_manual_tax?.currency
          ? exchangeRateList.get(`${parcel.DT_manual_tax?.currency}-${parcel.invoiceRateCurrency}`)
          : exchangeRateList.get(
              `${parcel.merchant_declared_currency}-${parcel.invoiceRateCurrency}`,
            );
        const exchangeRateToSGD = exchangeRateList.get(`${parcel.invoiceRateCurrency}-SGD`);

        if (!exchangeRateToSGD) {
          parcel.error = `Cannot get exchange rate from ${parcel.invoiceRateCurrency} to SGD for parcel ${parcel.id}`;
          continue;
        }

        // Calculate tax for each parcel
        const manualUploadTaxOfParcel = parcel.DT_manual_tax
          ? (+parcel.DT_manual_tax.tax_value ||
              (sumBy(parcel.item, (parcelItem: any) => +parcelItem.total_declared_value) *
                +parcel.DT_manual_tax.rate_percent) /
                100 ||
              0) * exchangeRateForDTManualTax
          : 0;

        const totalTaxFromItemOfParcel = parcel.item.reduce(
          (total: number, item: any) =>
            total +
            Number(item.GST_value || 0) +
            Number(item.others_tax || 0) +
            (exchangeRateForSalesTax
              ? Number(item.sales_tax || 0) * exchangeRateForSalesTax
              : Number(item.sales_tax || 0)) +
            (exchangeRateForDutyTax
              ? Number(item.duty_tax || 0) * exchangeRateForDutyTax
              : Number(item.duty_tax || 0)),
          0,
        );

        const totalActualTaxOfParcel = roundNumberBaseOnCurrency(
          manualUploadTaxOfParcel + totalTaxFromItemOfParcel,
          invoiceCurrency,
        );

        parcel.totalActualTax = totalActualTaxOfParcel;

        // Create route by route_chargeType_taxCode
        if (invoiceType === ENUM.invoiceType.DT) {
          parcel.taxCode =
            merchantTaxCodes?.find(
              (taxCode: any) =>
                taxCode.isDomestic ===
                  (parcel.shipment_type.toLowerCase() === ENUM.shipmentType.domestic) &&
                taxCode.invoiceType === invoiceType &&
                taxCode.chargeType === ENUM.invoiceChargeType.dutyAndTax,
            )?.taxCode || '';

          const route = `${parcel.origin_country}-${parcel.country}_${ENUM.invoiceChargeType.dutyAndTax}_${parcel.taxCode}_${parcel.service_option}`;

          if (routes[route]) {
            routes[route].totalActualTax += totalActualTaxOfParcel;
            routes[route].totalActualTaxInSGD += totalActualTaxOfParcel * exchangeRateToSGD;
          } else {
            routes[route] = {
              taxCode: parcel.taxCode,
              taxRate: 0,
              chargeType: ENUM.invoiceChargeType.dutyAndTax,
              route: route.split('_')[0],
              totalActualTax: totalActualTaxOfParcel,
              totalActualTaxInSGD: totalActualTaxOfParcel * exchangeRateToSGD,
              serviceOption: parcel.service_option,
            };
          }
        }

        if (isCalculateDDPForMerchant) {
          const merchantTaxCodeData = merchantTaxCodes?.find(
            (mTaxCode: any) =>
              mTaxCode.isDomestic ===
                (parcel.shipment_type.toLowerCase() === ENUM.shipmentType.domestic) &&
              mTaxCode.invoiceType === invoiceType &&
              mTaxCode.chargeType === ENUM.invoiceChargeType.surcharge &&
              mTaxCode.chargeSubType === ENUM.invoiceChargeSubType.DDP,
          );

          if (!merchantTaxCodeData) {
            continue;
          }

          const { taxCode, taxRate } = merchantTaxCodeData;

          parcel.ddp = {
            rate: roundNumberBaseOnCurrency(
              (totalActualTaxOfParcel * ddpSurchageRate) / 100,
              invoiceCurrency,
            ),
            taxCode,
            type: ENUM.invoiceChargeSubType.DDP,
          };
          parcel.ddp.tax = (+taxRate * parcel.ddp.rate) / 100;

          const route = `${parcel.origin_country}-${parcel.country}_${ENUM.invoiceChargeType.surcharge}_${parcel.ddp.taxCode}`;

          if (routes[route]) {
            routes[route].rate += parcel.ddp.rate;
            routes[route].tax += parcel.ddp.tax;
            routes[route].totalRateInSGD += parcel.ddp.rate * exchangeRateToSGD;
            routes[route].totalTaxInSGD += parcel.ddp.tax * exchangeRateToSGD;
          } else {
            routes[route] = {
              route: route.split('_')[0],
              type: ENUM.invoiceChargeSubType.DDP,
              chargeType: ENUM.invoiceChargeTypeDBValue.SURCHARGE,
              rate: parcel.ddp.rate,
              taxCode: parcel.ddp.taxCode,
              taxRate: Number(taxRate) || 0,
              tax: parcel.ddp.tax,
              totalRateInSGD: parcel.ddp.rate * exchangeRateToSGD,
              totalTaxInSGD: parcel.ddp.tax * exchangeRateToSGD,
            };
          }
        }
      } catch (error) {
        parcel.error = error;
      } finally {
        if (parcel.error) {
          errorParcels++;
          logger.info({
            message: 'Get merchant invoice by routes',
            merchantName,
            parcelId: parcel.id,
            parcelError: parcel.error,
          });
        } else {
          successParcels.push(parcel);
        }
      }
    }

    const routesArr = Object.values(routes);

    const { totalTaxInMerchantRateCurrency, totalSurcharge, totalSurchargeTax, totalDue } =
      this.sumTotalActualTax(routesArr);

    return {
      totalTaxInMerchantRateCurrency,
      totalSurcharge,
      totalSurchargeTax,
      totalDue,
      routes: routesArr,
      parcels: successParcels,
      errorParcels,
      merchantTaxType,
    };
  },

  generateInvoiceDate(invoiceDate: Date, startDayCountInvoice) {
    const start_date = format(startDayCountInvoice, 'd MMM yyyy');
    const unformatEndDate = new Date(
      Date.UTC(invoiceDate.getFullYear(), invoiceDate.getMonth(), invoiceDate.getDate() - 1),
    );
    const end_date = format(unformatEndDate, 'd MMM yyyy');
    const invoice_gen_date_str = end_date;
    const date_range = DateUtils.utcToStr('MMM YYYY', unformatEndDate);

    return {
      start_date,
      end_date,
      invoice_gen_date_str,
      date_range,
    };
  },

  async sendMerchantEmail(
    xlsxBuffer: Buffer,
    pdfBuffer: Buffer,
    merchant: any,
    invoiceNumber: string,
  ) {
    try {
      let { invoice_emails: invoiceEmails } = merchant;
      invoiceEmails = (invoiceEmails || []).join(';');

      const archive = new JSZipUtils();
      archive.add(`${invoiceNumber} detail.xlsx`, xlsxBuffer);
      archive.add(`${invoiceNumber}.pdf`, pdfBuffer);
      const zipBuffer = await archive.toBuffer();
      const mailOptions = {
        from: config.sendgrid.api_email,
        to: invoiceEmails,
        cc: config.sendgrid.cc_email,
        subject: `Duty and Tax Invoice No. ${invoiceNumber} from Parxl`,
        html: `
                <p>Dear Sir/Mdm,</p>
                <p>Please find attached duty and tax invoice generated for your necessary attention.</p>
                <p>Please remit payment at your earliest convenience.</p>
                <p>This is a system generated email from Parxl. No reply is required.</p>
                `,
        attachments: [
          {
            filename: `${invoiceNumber}_${new Date()}.zip`,
            content: zipBuffer,
          },
        ],
      };

      // Begin upload to blob-storage
      AzureBlobStorage.uploadInvoice(zipBuffer, invoiceNumber);

      // Use sendgrid from FIN
      EmailUtils.sendEmailSendgrid(mailOptions, { fromFunction: 'sendDTInvoice' });
    } catch (error: any) {
      logger.error({
        business: ENUM.FunctionName.EMAIL,
        message: 'Error while sending merchant email',
        merchantName: merchant.merchant_name,
        invoiceNumber,
        error: error.message ?? error,
      });
    }
  },
};
