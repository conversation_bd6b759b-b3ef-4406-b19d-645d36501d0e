import { ENUM } from '~/models/enum.js';

import { ValueOf } from './parcel-cancel.type.js';

type TServiceOption = 'standard' | 'plus' | 'self-collect' | 'freight' | 'postal';
type TChargeableWeight = 'Higher of gross/volumetric weight' | 'Gross weight' | 'Volumetric weight';
type TMerchantRateStatus = 'Approved' | 'Rejected' | 'Created' | 'Pending for Approval';

export type TRateSheetServiceOption = ValueOf<typeof ENUM.RATE_SHEET_SERVICE_OPTION>;

export interface IMerchantRate {
  id: string;
  _partitionKey: string; // encrypted merchant account number
  validity_from: string;
  validity_to: string;
  approver_id: string;
  approver_name: string;
  submiter_id: string;
  submiter_name: string;
  merchant_account_number: string;
  chargeable_weight: TChargeableWeight;
  is_uploading: boolean;
  version: 1 | 2;
  status: TMerchantRateStatus;
  isActive: boolean;
  service_option: TRateSheetServiceOption;
  type: 'merchant_rate';
}

export interface IMerchantRateItem {
  origin: string;
  destination: string;
  service_option: TServiceOption;
  weight: number;
  unit: 'kg' | 'lb';
  rate_currency: string;
  rate: number;
  rate_zone: string;
  rate_table_id: string;
  active: boolean;
  merchant_account_number: string;
}

export interface IMerchantRateItemV2 {
  weight?: number;
  country_code: string;
  service_option?: TServiceOption;
  rate: number;
  rate_table_id: string;
  active: boolean;
  merchant_account_number: string;
  type: 'merchant_rate_item';
}

export interface IMerchantRateV2 extends IMerchantRate {
  currency: string;
  incremental_weight_break: number;
  weight_unit: 'kg' | 'lb';
  incremental: Record<string, Record<TServiceOption, number>>;
  approved_date?: string;
  minimum_charge?: Record<string, number>; // only in B2B
  notes: string;
}

export interface IRateSheetValidationResult {
  success: boolean;
  message?: string;
  data?: {
    rates?: any[];
    emptyRows: number;
    version?: 1 | 2;
    rateChargeType?: 'Flat Rate' | 'Weight Break';
  };
}
