import atob from 'atob';
import { Fields, Files } from 'formidable';
import { Jwt, JwtPayload } from 'jsonwebtoken';
import cloneDeep from 'lodash/cloneDeep.js';
import passport from 'passport';
import getPem from 'rsa-pem-from-mod-exp';

import { AzureUtils } from '~/common/azure/azureUtils.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { B2CAccountService } from '~/services/b2c_account.service.js';
import { GaylordServices } from '~/services/gaylord.service.js';
import { OperationHubService } from '~/services/operation-hub.service.js';
import { UserSessionService } from '~/services/user-session.service.js';
import { UserServices } from '~/services/user.services.js';

import { JWT } from '~/utilities/jwt.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { throttledByIdentifierFunctionGenerator } from '~/utilities/throttle.utils.js';

import { Daos } from '~/daos/index.js';

const logger = new AppLogger({ name: 'middleware' });

const recordLspOpHubUserActivityTimeThrottled = throttledByIdentifierFunctionGenerator(
  B2CAccountService.recordLspOpHubUserActivityTime.bind(B2CAccountService),
  60 * 1000,
);

const recordLspSuperUserActivityTimeThrottled = throttledByIdentifierFunctionGenerator(
  B2CAccountService.recordLspSuperUserActivityTime.bind(B2CAccountService),
  60 * 1000,
);

const findLspUserByOpsHub = async function (email: string) {
  const opHub = await OperationHubService.getOperationHubByLspAccount(email);

  if (!opHub) {
    return null;
  }

  const encryptedEmail = AesUtils.CrtCounterEncrypt(email);
  const user = opHub.list_ops_hub_account.find(
    (acc: any) => acc.email === encryptedEmail && !acc.isDeleted,
  );

  if (user) {
    user.operation_hubs = [opHub.operation_hub];
    user.role = AesUtils.CrtCounterDecrypt(user.role);
    recordLspOpHubUserActivityTimeThrottled(encryptedEmail, [opHub, encryptedEmail]);
  }

  return user;
};

const findLspUser = async function (email: string) {
  email = (email || '').toLowerCase();
  let user = await findLspUserByOpsHub(email);

  if (!user) {
    user = await UserServices.getLspSuperuserByEmail(email);

    if (user) {
      user.accountEnabled = !user.is_deleted && user.b2cAccountEnabled;
      recordLspSuperUserActivityTimeThrottled(user.id, [user.id]);
    }
  }

  if (!user) {
    throw new Error('Cannot verify user within is LSP system');
  }

  if (!user.accountEnabled) {
    throw new Error('User is locked in LSP system');
  }

  return {
    id: user.id,
    name: AesUtils.CrtCounterDecrypt(user.name),
    email,
    phone_number: AesUtils.CrtCounterDecrypt(user.phone_number),
    role: user.role,
    receiveEmail: user.receiveEmail,
    operation_hubs: user.operation_hubs,
  };
};

const verifyLspToken = function (token: string, keysClaims: any) {
  const base64Url = token.split('.')[0];
  const { kid } = JSON.parse(atob(base64Url));
  const { n, e } = keysClaims && keysClaims.find((k: any) => k.kid === kid);

  // @ts-expect-error import error for getPem
  const publicKey = getPem(n, e);

  return JWT.verifyLspToken(token, publicKey);
};

const authenticateLSP = async function (req: any, res: any, next) {
  try {
    const authorization = req.headers['authorization'];

    if (typeof authorization === 'string' && authorization.startsWith('Bearer ')) {
      const { keys } = await AzureUtils.getPublicKeysB2C();
      const token = authorization.slice(7, authorization.length);
      const result: any = verifyLspToken(token, keys);
      const user = await findLspUser(result['signInNames.emailAddress']);
      res.locals.decoded = { user_info: user };
      req.user = user;

      // Validate User Session
      if (req.headers[ENUM.PlsRequestHeaders.CLIENT] !== ENUM.PlsAppName.LSP_PORTAL) {
        // LSP Portal v1
        const session = await UserSessionService.get(result.sub, 'lsp');

        if (session) {
          if (session.session_id !== token) {
            return res.status(401).send({
              message: 'Your account has been logged in on another browser/device.',
            });
          }
        } else {
          return res.status(401).send({
            message: 'Your user session has been expired. Please log in again.',
          });
        }
      }

      req.loggedInUserEmail = user.email;
      next();
    } else {
      logger.error({ message: 'Verify user error no Token', authorization });

      return res.status(401).send({
        message: 'No Token Provided',
      });
    }
  } catch (error) {
    logger.error({ message: 'Verify user error', error });

    return res.status(401).send({
      message: error.message,
    });
  }
};

export function authorizeAAD() {
  return async function (req: any, res: any, next) {
    passport.authenticate(
      'oauth-bearer',
      {
        session: false,
      },
      (err: any, user: any, info: any) => {
        if (err) {
          return res.status(401).json({ error: err.message });
        }

        if (!user) {
          // If no user object found, send a 401 response.
          return res.status(401).json({ error: 'Unauthorized' });
        }

        if (info) {
          req.authInfo = info;

          return next();
        }
      },
    )(req, res, next);
  };
}

const authenticatePLS = function (req: any, res: any, next) {
  // Express headers are auto converted to lowercase
  let token = req.headers['x-access-token'] || req.headers['authorization'];
  let user_token = req.headers['token'];

  if (token !== undefined && user_token !== undefined) {
    if (token.startsWith('Bearer ')) {
      token = token.slice(7, token.length);
    }

    if (user_token.startsWith('Bearer ')) {
      user_token = user_token.slice(7, user_token.length);
    }

    if (token && user_token) {
      try {
        const decoded: string | Jwt | JwtPayload = JWT.verify(user_token);
        res.locals.decoded = decoded;
        const msal_decoded: any = JWT.decode(token);

        if (
          decoded['user_info'].email.toLowerCase() ===
            msal_decoded?.payload?.preferred_username?.toLowerCase() ||
          decoded['user_info'].email.toLowerCase() ===
            msal_decoded?.payload?.user_info?.email?.toLowerCase()
        ) {
          req.loggedInUserEmail = decoded['user_info'].email;
          req.user = decoded['user_info'];
          next();
        } else {
          return res.status(401).json({
            requestSuccessfully: false,
            message: 'Auth token and MSAL token is not matched.',
          });
        }
      } catch (error) {
        return res.status(401).json({
          requestSuccessfully: false,
          message: error.message,
        });
      }
    } else {
      return res.status(401).json({
        success: false,
        message: 'Auth token is not supplied.',
      });
    }
  } else {
    return res.status(401).json({
      success: false,
      message: 'Auth token is not provided.',
    });
  }
};

export function lspUserAuthenticate() {
  return async function (req: any, res: any, next) {
    if (req.headers['pls-name'] === ENUM.PlsAppName.LspService) {
      res.locals.platform = ENUM.PlsAppName.LspService;

      return authenticateLSP(req, res, next);
    }

    res.locals.platform = ENUM.PlsAppName.PLS_UI;
    next();
  };
}

/** Middleware authenticate
 *
 */
export function userAuthenticate() {
  return async function (req: any, res: any, next) {
    if (req.headers['pls-name'] === ENUM.PlsAppName.LspService) {
      return authenticateLSP(req, res, next);
    }

    return authenticatePLS(req, res, next);
  };
}

export function checkRole(arrRole: any[]) {
  return function (req: any, res: any, next: any) {
    if (req.headers['pls-name'] === ENUM.PlsAppName.LspService) {
      if (arrRole.includes(ENUM.USER_ROLES_SERVER.lsp_service_user)) {
        next();
      } else {
        return res.status(403).json({
          requestSuccessfully: false,
          message: "You don't have permission for this",
        });
      }
    } else {
      // Express headers are auto converted to lowercase
      let token = req.headers['x-access-token'] || req.headers['authorization'];
      let user_token = req.headers['token'];

      if (token !== undefined && user_token !== undefined) {
        if (token.startsWith('Bearer ')) {
          token = token.slice(7, token.length);
        }

        if (user_token.startsWith('Bearer ')) {
          user_token = user_token.slice(7, user_token.length);
        }

        if (token && user_token) {
          try {
            const decoded: string | Jwt | JwtPayload = JWT.verify(user_token);
            const msal_decoded: any = JWT.decode(token);

            if (
              decoded['user_info'].email.toLowerCase() !==
                msal_decoded?.payload?.preferred_username?.toLowerCase() &&
              decoded['user_info'].email.toLowerCase() !==
                msal_decoded?.payload?.user_info?.email?.toLowerCase()
            ) {
              return res.status(401).json({
                requestSuccessfully: false,
                message: 'Auth token and MSAL token is not matched.',
              });
            }

            if (arrRole.includes(decoded['user_info'].role)) {
              const querySpec = {
                query: 'SELECT TOP 1 * FROM user d where d.email= @email',
                parameters: [
                  {
                    name: '@email',
                    value: AesUtils.CrtCounterEncrypt(decoded['user_info'].email.toLowerCase()),
                  },
                ],
              };
              Daos.user
                .find(querySpec)
                .then((items: any[]) => {
                  const user = items.find((x) => {
                    return x.is_deleted === false;
                  });

                  if (user === undefined) {
                    return res.status(401).json({
                      requestSuccessfully: false,
                      message: 'Cannot verify user within Parxl system.',
                    });
                  }

                  if (arrRole.includes(user.role)) {
                    next();
                  } else {
                    return res.status(403).json({
                      requestSuccessfully: false,
                      message: "You don't have permission for this",
                    });
                  }
                })
                .catch(() => {
                  return res.status(401).json({
                    requestSuccessfully: false,
                    message: 'Cannot verify user within Parxl system.',
                  });
                });
            } else {
              return res.status(403).json({
                requestSuccessfully: false,
                message: "You don't have permission for this",
              });
            }
          } catch (error) {
            return res.status(401).json({
              requestSuccessfully: false,
              message: error.message,
            });
          }
        } else {
          return res.status(401).json({
            success: false,
            message: 'Auth token is not supplied.',
          });
        }
      } else {
        return res.status(401).json({
          success: false,
          message: 'Auth token is not provided.',
        });
      }
    }
  };
}

export async function incomingForm(req: any, res: any, next: any) {
  try {
    const formidable = (await import('formidable')).default;
    const form = formidable({});

    req.formData = await new Promise((resolve, reject) => {
      form.parse(req, async function (err: any, fields: Fields<string>, files: Files<string>) {
        if (err) {
          return reject('Error while uploading file');
        }

        /** transforming fields in the incoming form data
         * to destructure each field value from being wrapped in an array
         */
        const transformedFields = {};

        for (const [key, value] of Object.entries(fields)) {
          transformedFields[key] = value.length === 1 ? value[0] : value;
        }

        resolve({ files, fields: transformedFields });
      });
    });

    next();
  } catch {
    return res.status(500).json({
      success: false,
      message: 'Form parsing not successfully.',
    });
  }
}

/**
 * @description check ops hub for multiple ops hub
 * @param {*} type: type of payload: BODY, QUERY, FORMDATA, PARAMS,...
 * @returns next function or error
 */
export function checkOpsHub(type: string) {
  return async function (req: any, res: any, next: any) {
    try {
      let operation_hub: string = '';
      let opsHubMatch: string;
      let gaylordId: string;

      if (type === 'FORMDATA') {
        const { fields } = req.formData;
        operation_hub = fields.operationsHub;
        gaylordId = req.params.gaylordId;
      }

      if (type === 'BODY') {
        operation_hub = req.body.operation_hub;
        gaylordId = req.body.gaylord_no;
      }

      if (!operation_hub) {
        return res.json({
          success: false,
          message: 'Missing operations hub',
        });
      }

      const gaylord = await GaylordServices.getGaylordById(gaylordId);

      if (!gaylord) {
        return res.status(400).json({
          success: false,
          message: `Gaylord ${gaylordId} not found`,
        });
      }

      req.gaylord = gaylord;

      if (gaylord.operation_hub) {
        opsHubMatch = gaylord.operation_hub;

        if (opsHubMatch === operation_hub) {
          return next();
        }

        return res.json({
          success: false,
          message: 'Current Ops Hub is not same with gaylord',
          data: {
            operation_hub: opsHubMatch,
          },
        });
      }

      return next();
    } catch (error) {
      logger.error({ caller: 'checkOpsHub', error });

      return res.status(500).json({
        success: false,
        message: 'Something wrong when validate ops hub',
      });
    }
  };
}

export function requestLog(req: any, res: any, next) {
  const startTime = Date.now();

  const originalJsonFunc = res.json;
  let resBody: any;
  res.json = function (body: any) {
    resBody = cloneDeep(body);

    return originalJsonFunc.call(this, body);
  };

  res.on('finish', function () {
    try {
      const { method, originalUrl, headers, body, formData } = req;

      if (method === 'OPTIONS' || originalUrl === '/') {
        return;
      }

      const { statusCode } = res;
      let errorString = '';
      let bodyString = '';

      if (statusCode >= 400) {
        errorString =
          typeof resBody === 'object' ? `error=${JSON.stringify(resBody)}` : `error=${resBody}`;
      }

      const isMethodMatched = ['POST', 'PUT', 'PATCH'].includes(method);
      const isRawJsonBodyMatched =
        headers['content-type']?.includes('application/json') &&
        (Array.isArray(body) || Object.keys(body).length > 0);
      const isFormDataMatched = headers['content-type']?.includes('multipart/form-data');

      if (isMethodMatched) {
        if (isRawJsonBodyMatched) {
          bodyString = `payload=${JSON.stringify(body)}`;
        } else if (isFormDataMatched) {
          bodyString = `payload=${JSON.stringify(formData)}`;
        }
      }

      const userInfo = res.locals?.decoded?.user_info;

      const logMessages = {
        request: `${method} ${originalUrl}`,
        responseStatus: statusCode,
        responseTime: Date.now() - startTime,
        userId: userInfo?.id,
        userEmail: userInfo?.email,
        userRole: userInfo?.role,
        client: headers[ENUM.PlsRequestHeaders.CLIENT],
        errorString,
        bodyString,
      };

      logger.info({ caller: 'requestLog', ...logMessages });
    } catch (error) {
      logger.error({ caller: 'requestLog', message: 'requestLog exception', error });
    }
  });

  next();
}
