/* eslint-disable require-await */
/* eslint-disable camelcase */
import XLSX from 'xlsx';

import { BadRequestError } from '~/common/error_handling/BadRequest.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import StatusMappingService from '~/services/statusMappingService.js';

import { AppLogger } from '~/utilities/logUtils.js';
import { PlsReportUtils } from '~/utilities/plsReportUtils.js';
import { StatusUtils } from '~/utilities/statusUtils.js';

import { Daos } from '~/daos/index.js';

import { GaylordServices } from './gaylord.service.js';
import { ManifestItemService } from './manifest-item.service.js';
import { MawbService } from './mawb-service.js';

type gaylordItem = {
  id: string;
  mawb_no: string;
  tracking_status?: any;
};

type MawbItem = {
  id: string;
  status: string;
  date: string;
  timestamp: string;
  service_option?: string;
};

type trackingStatusArray = {
  status: string;
  date?: string;
  timestamp?: string;
};

type parcelItem = {
  id: string;
  merchant_account_number: string;
  gaylord_no: string;
  tracking_status: trackingStatusArray[];
  mawb_no?: string;
  latest_tracking_status?: string;
  timestamp?: string;
};

const logger = new AppLogger({ name: 'ReportService' });

const monitoringFields = [
  'c.id',
  'c.tracking_id',
  'c.tracking_status',
  'c.merchant_account_number',
  'c.gaylord_no',
].join(', ');

export const ReportService = {
  async getMawbs(mawbNo: string): Promise<Array<MawbItem>> {
    const mawbQuery = {
      query:
        'SELECT c.id, c.status, c.date, c.timestamp, c.service_option, c.destination FROM c where c._partitionKey = @mawbNo',
      parameters: [{ name: '@mawbNo', value: mawbNo }],
    };

    return Daos.mawb.find(mawbQuery);
  },

  async getGaylordsInLateMawb(mawbNo: string) {
    const gaylordsQuery = {
      query: 'SELECT c.id, c.overpack_id FROM c WHERE c.mawb_no = @mawbNo',
      parameters: [{ name: '@mawbNo', value: mawbNo }],
    };

    return Daos.gaylord.find(gaylordsQuery);
  },

  async getRawParcels(gaylordIds: string[]) {
    const parcelsInMawbQuery = {
      query:
        'SELECT c.tracking_no, c.tracking_id, c.tracking_status, c.origin, c.merchant_name, c.destination_group, c.lmd, c.gaylord_no ' +
        'FROM c WHERE ARRAY_CONTAINS(@gaylords, c.gaylord_no)',
      parameters: [{ name: '@gaylords', value: [...new Set(gaylordIds)] }],
    };

    return Daos.manifest_items.find(parcelsInMawbQuery);
  },

  combineMawbStts(rawParcels, mawbs) {
    return rawParcels.map((parcel) => ({
      ...parcel,
      tracking_status: PlsReportUtils.combineParcelStatusesAndMawbStatuses(
        parcel.tracking_status,
        mawbs,
      ),
    }));
  },

  getSanitizedParcels(rawParcels, gaylordsInLateMawb, mawbNo: string, isB2B, mawbs) {
    const parcels = rawParcels.map((rawParcel) => {
      const merchant_name = AesUtils.CrtCounterDecrypt(rawParcel.merchant_name);
      const parcel_age = PlsReportUtils.getParcelAge(rawParcel);
      const latest_tracking_status = StatusUtils.getLatestTrackingStatus(rawParcel.tracking_status);
      const overpack_no =
        gaylordsInLateMawb.find((gaylord) => gaylord.id === rawParcel.gaylord_no)?.overpack_id ||
        '';
      let destination_country;

      if (isB2B) {
        const stastdMawb = mawbs.find((mawb) => mawb.status === ENUM.mawbStatus.stastd);
        destination_country = stastdMawb ? stastdMawb.destination : undefined;
      }

      return {
        ...rawParcel,
        merchant_name,
        parcel_age,
        mawb_no: mawbNo,
        latest_tracking_status,
        overpack_no,
        ...(isB2B && destination_country ? { destination_country } : {}),
      };
    });

    return parcels;
  },

  getXLSXContentForLateMawb(parcels, mapXlsxHeadersToParcelField: Array<Record<string, string>>) {
    const content = parcels.map((parcel) => {
      const obj = {};

      for (const { xlsxHeader, parcelField } of mapXlsxHeadersToParcelField) {
        obj[xlsxHeader] = parcel[parcelField];
      }

      return obj;
    });

    return content;
  },

  generateMawbReport(reportContent: Array<Record<string, string>>, mawbNo: string) {
    const workbook = XLSX.utils.book_new();

    const worksheet = XLSX.utils.json_to_sheet(reportContent);
    XLSX.utils.book_append_sheet(workbook, worksheet, mawbNo);

    return XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
  },

  getB2BreportMappingFields() {
    return [
      { xlsxHeader: 'PLS_shipment_booking_reference', parcelField: 'tracking_no' },
      { xlsxHeader: 'shipment_tracking_id', parcelField: 'tracking_id' },
      { xlsxHeader: 'mawb_no', parcelField: 'mawb_no' },
      { xlsxHeader: 'latest_tracking_status', parcelField: 'latest_tracking_status' },
      { xlsxHeader: 'parcel_age', parcelField: 'parcel_age' },
      { xlsxHeader: 'origin airport', parcelField: 'origin' },
      { xlsxHeader: 'merchant_name', parcelField: 'merchant_name' },
      { xlsxHeader: 'destination_country', parcelField: 'destination_country' },
      { xlsxHeader: 'lmd', parcelField: 'lmd' },
      { xlsxHeader: 'gaylord_no', parcelField: 'gaylord_no' },
      { xlsxHeader: 'overpack_no', parcelField: 'overpack_no' },
    ];
  },

  getB2CreportMappingFields() {
    return [
      { xlsxHeader: 'PLS_shipment_booking_reference', parcelField: 'tracking_no' },
      { xlsxHeader: 'shipment_tracking_id', parcelField: 'tracking_id' },
      { xlsxHeader: 'mawb_no', parcelField: 'mawb_no' },
      { xlsxHeader: 'latest_tracking_status', parcelField: 'latest_tracking_status' },
      { xlsxHeader: 'parcel_age', parcelField: 'parcel_age' },
      { xlsxHeader: 'origin airport', parcelField: 'origin' },
      { xlsxHeader: 'merchant_name', parcelField: 'merchant_name' },
      { xlsxHeader: 'destination_group', parcelField: 'destination_group' },
      { xlsxHeader: 'lmd', parcelField: 'lmd' },
      { xlsxHeader: 'gaylord_no', parcelField: 'gaylord_no' },
      { xlsxHeader: 'overpack_no', parcelField: 'overpack_no' },
    ];
  },

  async getMawbReport(mawbNo): Promise<any> {
    const mawbs = await this.getMawbs(mawbNo);

    if (mawbs?.length < 1) {
      throw new BadRequestError('No Mawb with mawbNo provided found');
    }

    const isB2Bmawb = mawbs.some((mawb) => mawb.service_option === ENUM.parcelOption.b2b);
    const gaylordsInLateMawb = await this.getGaylordsInLateMawb(mawbNo);
    const gaylordIds = gaylordsInLateMawb.map((gaylord) => gaylord.id);

    const rawParcels = await this.getRawParcels(gaylordIds);
    const parcelsWithMawbStts = this.combineMawbStts(rawParcels, mawbs);
    const sanitizedParcels = this.getSanitizedParcels(
      parcelsWithMawbStts,
      gaylordsInLateMawb,
      mawbNo,
      isB2Bmawb,
      mawbs,
    );

    const xlsxContent = this.getXLSXContentForLateMawb(
      sanitizedParcels,
      isB2Bmawb ? this.getB2BreportMappingFields() : this.getB2CreportMappingFields(),
    );
    const workbookBuffer = this.generateMawbReport(xlsxContent, mawbNo);

    return workbookBuffer;
  },

  convertMawbToObject(mawbSearchQuery: string): Record<string, boolean> | undefined {
    if (!mawbSearchQuery) {
      return undefined;
    }

    const mawbObjectList = {};

    for (const mawb of mawbSearchQuery.split(',')) {
      mawbObjectList[mawb.trim()] = true;
    }

    return mawbObjectList;
  },

  async getGaylordListForMonitoring(mawbList: string[]): Promise<gaylordItem[]> {
    const gaylordQuery = {
      query: `SELECT c.mawb_no, c.id, c.tracking_status
              FROM c
              WHERE ARRAY_CONTAINS(@mawbList, c.mawb_no)`,
      parameters: [{ name: '@mawbList', value: [...new Set(mawbList)] }],
    };

    return Daos.gaylord.find(gaylordQuery);
  },

  parcelQueryForMonitoring(gaylordIdList: string[], merchant?: string) {
    const baseQuery: { query: string; parameters: { name: string; value: any }[] } = {
      query: `SELECT ${monitoringFields}
    FROM c
    WHERE ARRAY_CONTAINS(@gaylordIdList, c.gaylord_no)`,
      parameters: [{ name: '@gaylordIdList', value: gaylordIdList }],
    };

    if (merchant) {
      baseQuery.query += ` AND c.merchant_account_number = @merchant`;
      baseQuery.parameters.push({ name: '@merchant', value: merchant });
    }

    logger.info({ baseQuery });

    return baseQuery;
  },

  injectLatestStatusInfo(parcel: parcelItem, gaylord: gaylordItem, mawbs: MawbItem[]) {
    const resultParcel = { ...parcel };
    let combinedTrackingStt = ManifestItemService.groupDateBaseOnStatus(parcel.tracking_status);

    if (parcel.tracking_status && parcel.tracking_status.length > 0) {
      combinedTrackingStt = PlsReportUtils.combineParcelStatusesAndMawbStatuses(
        [...parcel.tracking_status, ...gaylord.tracking_status],
        mawbs,
      );
      const filteredEmptyCombinedTrackingStt = combinedTrackingStt.filter(
        (trackingStatus) => !!trackingStatus.status,
      );

      const mergeTrackingStt: any[] = Object.values(
        filteredEmptyCombinedTrackingStt.reduce((sttGroup: any, { status, date }: any) => {
          sttGroup[status] = sttGroup[status] || { status, date: [] };
          sttGroup[status].date.push(date);

          return sttGroup;
        }, {}),
      );

      resultParcel.latest_tracking_status = mergeTrackingStt.at(-1).status;
      resultParcel.timestamp = mergeTrackingStt.at(-1).date[0];
      delete resultParcel.tracking_status;
    }

    return resultParcel;
  },

  isDateWithinRange(dateString, startDate, endDate) {
    const date = new Date(dateString);

    return date >= new Date(startDate) && date <= new Date(endDate);
  },
  parseDateRange(dateRange?: string): [string, string] | null {
    if (!dateRange) return null;
    const [startDate, endDate] = dateRange.split(',');

    return [startDate, endDate];
  },

  filterParcelsByStatusAndDateRange(
    parcels: parcelItem[],
    statusType: string,
    dateRange: [string, string],
  ): parcelItem[] {
    const [startDate, endDate] = dateRange;

    return parcels.filter((parcel) => {
      const matchingStatuses = parcel.tracking_status.filter(
        (status) => status.status === StatusMappingService.getManifestStatus(statusType),
      );

      return matchingStatuses.some((status) => {
        const statusDate = status.date || status.timestamp;

        if (!statusDate) return false;

        return this.isDateWithinRange(statusDate, startDate, endDate);
      });
    });
  },

  async getRawParcelsForMonitorView(
    gaylordList: gaylordItem[],
    merchant: string,
    dateRangeBooking: string,
    dateRangeWarehouse: string,
  ): Promise<Array<parcelItem>> {
    // Get Parcels based on gaylord list and merchant info
    const gaylordIdList = gaylordList.map((gaylord) => {
      return gaylord.id;
    });
    const parcels: parcelItem[] = await Daos.manifest_items.find(
      ReportService.parcelQueryForMonitoring(gaylordIdList, merchant),
    );

    // Filter Parcels by date range for Booked and Received at Warehouse
    if (dateRangeBooking) {
      return this.filterParcelsByStatusAndDateRange(
        parcels,
        ENUM.parcelStatus.booked,
        this.parseDateRange(dateRangeBooking),
      );
    }

    if (dateRangeWarehouse) {
      return this.filterParcelsByStatusAndDateRange(
        parcels,
        ENUM.parcelStatus.received_at_warehouse,
        this.parseDateRange(dateRangeWarehouse),
      );
    }

    return parcels;
  },

  async getParcelListForMonitoring(
    gaylordList: gaylordItem[],
    mawbList: any[],
    merchant: string,
    dateRangeBooking: string,
    dateRangeWarehouse: string,
  ) {
    const foundParcelList = await ReportService.getRawParcelsForMonitorView(
      gaylordList,
      merchant,
      dateRangeBooking,
      dateRangeWarehouse,
    );

    if (foundParcelList && foundParcelList.length > 0) {
      const parcels = foundParcelList.map((parcel) => {
        const foundGaylord = gaylordList.find((gaylord) => parcel.gaylord_no === gaylord.id);
        const foundMawbs = mawbList?.filter((mawb) => mawb._partitionKey === foundGaylord.mawb_no);
        const modifiedParcel = ReportService.injectLatestStatusInfo(
          parcel,
          foundGaylord,
          foundMawbs,
        );
        const { latest_tracking_status, timestamp } = modifiedParcel;

        return { ...parcel, mawb_no: foundGaylord.mawb_no, latest_tracking_status, timestamp };
      });

      return parcels;
    }

    return foundParcelList;
  },

  /**
   * Get a query spec for retrieving parcels from manifest_items table
   * for monitoring report by merchant and date range parammeters
   * @param merchant - encrypted merchant name
   * @param dateRangeBooking - date range for booked status,  @example '2024-11-01T00:00:00.000Z,2024-11-06T23:59:59.999Z'
   * @param dateRangeWarehouse - date range for received at warehouse status @example '2024-11-01T00:00:00.000Z,2024-11-06T23:59:59.999Z'
   * @returns an object with query and parameters
   */
  getParcelQueryForMonitoringByMerchantAndDateRange(
    merchant: string,
    dateRangeBooking: string,
    dateRangeWarehouse: string,
  ) {
    const [startDateBooking, endDateBooking] = dateRangeBooking.split(',');
    const [startDateWarehouse, endDateWarehouse] = dateRangeWarehouse.split(',');
    const queryManifest = {
      query: dateRangeWarehouse
        ? `SELECT DISTINCT ${monitoringFields} FROM c join t in c.tracking_status WHERE c.tracking_status[0].status = @status AND t.status = @receivedAtWarehouse`
        : `SELECT ${monitoringFields} FROM c WHERE c.tracking_status[0].status = @status`,
      parameters: [
        {
          name: '@status',
          value: StatusMappingService.getManifestStatus(ENUM.parcelStatus.booked),
        },
        {
          name: '@receivedAtWarehouse',
          value: StatusMappingService.getManifestStatus(ENUM.parcelStatus.received_at_warehouse),
        },
      ],
    };

    if (dateRangeBooking) {
      queryManifest.query +=
        ' AND c.tracking_status[0].date >= @startDateBooking AND c.tracking_status[0].date <= @endDateBooking';
      queryManifest.parameters.push(
        {
          name: '@startDateBooking',
          value: startDateBooking,
        },
        {
          name: '@endDateBooking',
          value: endDateBooking,
        },
      );
    }

    if (dateRangeWarehouse) {
      queryManifest.query += ' AND t.date >= @startDateWareHouse AND t.date <= @endDateWarehouse';
      queryManifest.parameters.push(
        {
          name: '@startDateWareHouse',
          value: startDateWarehouse,
        },
        {
          name: '@endDateWarehouse',
          value: endDateWarehouse,
        },
      );
    }

    queryManifest.query += ' AND c.merchant_account_number = @merchant';
    queryManifest.parameters.push({
      name: '@merchant',
      value: merchant,
    });

    return queryManifest;
  },

  combineParcelsWithGaylordAndMawbs(parcelsWithGaylord, gaylords, mawbs) {
    const parcels = parcelsWithGaylord
      .map((parcel) => {
        const foundGaylord = gaylords.find((gaylord) => parcel.gaylord_no === gaylord.id);
        const foundMawbs = mawbs[foundGaylord.mawb_no];

        if (!foundMawbs) return null;

        const modifiedParcel = ReportService.injectLatestStatusInfo(
          parcel,
          foundGaylord,
          foundMawbs,
        );
        const { latest_tracking_status, timestamp } = modifiedParcel;

        return { ...parcel, mawb_no: foundGaylord.mawb_no, latest_tracking_status, timestamp };
      })
      .filter(Boolean);

    return parcels;
  },

  async getParcelsForMonitoringWithoutMawb(
    merchant: string,
    dateRangeBooking: string,
    dateRangeWarehouse: string,
  ) {
    const query = this.getParcelQueryForMonitoringByMerchantAndDateRange(
      merchant,
      dateRangeBooking,
      dateRangeWarehouse,
    );
    const originalParcels = await Daos.manifest_items.find(query);
    const parcelsWithGaylord = originalParcels.filter((parcel) => parcel.gaylord_no);

    const gaylords = await GaylordServices.getGaylordsByIds(
      parcelsWithGaylord.map((parcel) => parcel.gaylord_no),
    );

    const mawbs = await MawbService.getMultipleMawbs(gaylords.map((gaylord) => gaylord.mawb_no));

    return this.combineParcelsWithGaylordAndMawbs(parcelsWithGaylord, gaylords, mawbs);
  },

  getCombinedHeaderForNewReport(newReportConfig: object): string[] {
    return Object.keys(newReportConfig)
      .filter((key) => newReportConfig[key].checked)
      .reduce((acc, key) => acc.concat(newReportConfig[key].headers), [])
      .filter((header) => header[0] !== '_');
  },

  getCombinedDataForNewReport(
    extractedContent: Record<string, string>[],
    newReportConfig: Record<string, Record<string, string[] | boolean>>,
    reportContent: Record<string, string>,
  ): Record<string, string>[] {
    return extractedContent.map((_, index) => {
      return Object.keys(newReportConfig)
        .filter((key) => newReportConfig[key].checked)
        .reduce((acc, key) => {
          const rowData = reportContent[key]?.[index] || {};

          return { ...acc, ...rowData };
        }, {});
    });
  },

  generateExcel(combinedData: Record<string, string>[], combinedHeaders: string[]) {
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(combinedData, { header: combinedHeaders });
    XLSX.utils.book_append_sheet(workbook, worksheet, 'default');

    return XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });
  },
};
