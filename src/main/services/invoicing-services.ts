import { format } from 'date-fns';
import XLSX from 'xlsx';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { CurrencyConversionService } from '~/services/currency-conversion-service.js';
import { MerchantService } from '~/services/merchant-service.js';
import { TaxService } from '~/services/tax-service.js';

import { AppLogger } from '~/utilities/logUtils.js';
import { QueryUtils } from '~/utilities/queryUtils.js';

import { config } from '~/configs/index.js';
import { Daos } from '~/daos/index.js';

const logger = new AppLogger({ name: 'InvoicingServices' });

export const InvoicingServices = {
  async getInvoiceNumbersForDownload(condition: any) {
    // Note: month => index month (0-11)
    const { types, merchantNos, months, year } = condition;

    const invoiceMonths = months.map((month) => format(new Date(year, month), 'MMM yyyy'));

    const querySpec = QueryUtils.getInvoiceNumbersQuery(types, merchantNos, invoiceMonths);
    const result = await Daos.invoice.find(querySpec);

    return result.map((item) => item?.invoiceNumber).filter(Boolean);
  },

  async getMerchantsForInvoicing(merchantName: string, taxCountryManagementInfo: any[]) {
    let merchants = await MerchantService.getAllMerchants(true);

    if (merchantName) {
      merchants = merchants.filter((merchant) => merchant.merchant_name === merchantName);
    }

    const allTaxCodes = await TaxService.getAllTaxCodes();

    for (const merchant of merchants) {
      const taxConfig = merchant.invoicing_info?.tax;

      if (taxConfig) {
        merchant.taxDisplay = TaxService.getTaxDisplay(taxConfig.country, taxCountryManagementInfo);
        merchant.taxCountry = taxConfig.country;
        merchant.taxType = merchant.taxDisplay?.taxType || '';
        merchant.taxCodes = TaxService.filterMerchantTaxCodes(
          allTaxCodes,
          taxConfig.country,
          taxConfig.constParty,
        );
      }
    }

    return merchants;
  },

  /**
   * Add PD invoice data to DB
   * @param {*} invoice
   * @returns DB status
   */
  async insertInvoice(invoice: any) {
    const partitionKey = AesUtils.CrtCounterEncrypt(invoice.merchant_no);

    for (const surcharge of invoice.surcharges) {
      if (surcharge.chargeType) {
        delete surcharge.chargeType;
      }
    }

    const fullInfo = Object.assign(invoice, {
      merchant_no: partitionKey,
      merchant_name: AesUtils.CrtCounterEncrypt(invoice.merchant_name),
      merchant_address_line1: AesUtils.CrtCounterEncrypt(invoice.merchant_address_line1),
      merchant_address_line2: AesUtils.CrtCounterEncrypt(invoice.merchant_address_line2),
      merchant_address_line3: AesUtils.CrtCounterEncrypt(invoice.merchant_address_line3),
      invoice_year: invoice.invoice_generation_datetime.getFullYear(),
      invoice_gen_date: invoice.invoice_generation_datetime,
      _partitionKey: partitionKey,
      type: ENUM.invoiceType.PD,
    });
    await Daos.invoice.addItem(fullInfo);
  },

  async getBankDetails() {
    try {
      const bufferStream = await AzureBlobStorage.downloadBankDetailsSheet();
      const buffer = await this.streamToBuffer(bufferStream);
      const sheet = this.xlsxToJson(buffer) as any[];
      const bankDetails: any[] = [];

      for (const row of sheet) {
        bankDetails.push({
          account_type: row[ENUM.BankDetails.FIELD_ACCOUNT_TYPE],
          title: row[ENUM.BankDetails.FIELD_TITLE],
          bank_detail: row[ENUM.BankDetails.FIELD_BANK_DETAILS],
        });
      }

      if (!bankDetails?.length) {
        logger.error({ message: 'Cannot get blob properties' });

        return null;
      }

      return bankDetails;
    } catch (error: any) {
      logger.error({ error });
    }
  },

  async getTaxCountryManagementInfo() {
    try {
      const taxDisplayBlob = await AzureBlobStorage.getLatestFileByTimestamp(
        ENUM.TaxDisplay.PREFIX,
        ENUM.TaxDisplay.EXTENSION,
        config.azureStorageContainer.CONFIGURATION,
      );

      if (!taxDisplayBlob) {
        logger.error({ message: 'Cannot get blob properties' });

        return [];
      }

      const buffer = await AzureBlobStorage.downloadBlobBuffer(
        config.azureStorageContainer.CONFIGURATION,
        taxDisplayBlob.name,
      );

      const sheet = this.xlsxToJson(buffer);

      if (sheet.length === 0) {
        logger.error({ message: 'Cannot parse xlsx to json' });

        return [];
      }

      const taxCountryManagementInfo = [];

      for (const row of sheet) {
        taxCountryManagementInfo.push({
          taxCountry: row[ENUM.TaxDisplay.FIELD_TAX_COUNTRY],
          taxType: row[ENUM.TaxDisplay.FIELD_TAX_TYPE],
          SR: row[ENUM.TaxDisplay.FIELD_SR_FOOTER],
          ZR: row[ENUM.TaxDisplay.FIELD_ZR_FOOTER],
          OS: row[ENUM.TaxDisplay.FIELD_OS_FOOTER],
          address: row[ENUM.TaxDisplay.FIELD_ADDRESS],
          companyRegNo: row[ENUM.TaxDisplay.FIELD_COMPANY_REG_NO],
          taxRegNo: row[ENUM.TaxDisplay.FIELD_TAX_REG_NO],
          otherInfo1: row[ENUM.TaxDisplay.FIELD_OTHER_INFO_1],
          otherInfo2: row[ENUM.TaxDisplay.FIELD_OTHER_INFO_2],
          otherInfo3: row[ENUM.TaxDisplay.FIELD_OTHER_INFO_3],
        });
      }

      return taxCountryManagementInfo;
    } catch (error) {
      logger.error({ error });

      return [];
    }
  },

  streamToBuffer(readableStream: any): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      readableStream.on('data', (data: any) => {
        chunks.push(data instanceof Buffer ? data : Buffer.from(data));
      });
      readableStream.on('end', () => {
        resolve(Buffer.concat(chunks));
      });
      readableStream.on('error', reject);
    });
  },

  xlsxToJson(buffer: Buffer) {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      return XLSX.utils.sheet_to_json(worksheet);
    } catch (error) {
      logger.error({ error });

      return [];
    }
  },

  async groupExSurchargesByRouteAndType(
    surcharges: any,
    invoiceCurrency: string,
    merchant: any,
    invoiceMonth: Date,
  ) {
    const exchangeRates = await CurrencyConversionService.getAllExchRates(invoiceMonth);
    const surchargesByRouteAndType = {} as any;

    // Grouping surcharges by route and type
    for (const surcharge of surcharges) {
      let exchangeRate = 1;

      if (surcharge.currency !== invoiceCurrency) {
        const rateObj = CurrencyConversionService.convertCurrency(
          exchangeRates,
          surcharge.currency,
          invoiceCurrency,
        );
        exchangeRate = rateObj?.destination_currency;
      }

      const tax = TaxService.calculateTax(
        merchant,
        surcharge?.shipment_type?.toLowerCase() === ENUM.shipmentType.domestic,
        'Surcharge',
        (surcharge.chargeable_amount * exchangeRate).toString(),
        invoiceCurrency,
        surcharge.type,
      );

      Object.assign(surcharge, tax);
      const exchangeRateToSGD = CurrencyConversionService.convertCurrency(
        exchangeRates,
        invoiceCurrency,
        'SGD',
      )?.destination_currency;

      if (surcharge.reference_type === 'merchant') {
        const merchantLevelSurchargeRouteAndType = `N/A_${surcharge.type}`;

        if (surchargesByRouteAndType[merchantLevelSurchargeRouteAndType]) {
          surchargesByRouteAndType[merchantLevelSurchargeRouteAndType].rate += surcharge.rate;
          surchargesByRouteAndType[merchantLevelSurchargeRouteAndType].tax += surcharge.tax;
          surchargesByRouteAndType[merchantLevelSurchargeRouteAndType].totalRateInSGD +=
            surcharge.rate * (exchangeRateToSGD || 0);
          surchargesByRouteAndType[merchantLevelSurchargeRouteAndType].totalTaxInSGD +=
            surcharge.tax * (exchangeRateToSGD || 0);
        } else {
          surchargesByRouteAndType[merchantLevelSurchargeRouteAndType] = {
            route: 'N/A',
            type: surcharge.type,
            rate: surcharge.rate,
            taxCode: surcharge.taxCode,
            taxRate: surcharge.taxRate,
            tax: surcharge.tax,
            totalRateInSGD: surcharge.rate * (exchangeRateToSGD || 0),
            totalTaxInSGD: surcharge.tax * (exchangeRateToSGD || 0),
          };
        }

        continue;
      }

      const routeAndType = `${surcharge.origin}-${surcharge.destination}_${surcharge.type}`;

      if (surchargesByRouteAndType[routeAndType]) {
        surchargesByRouteAndType[routeAndType].rate += surcharge.rate;
        surchargesByRouteAndType[routeAndType].tax += surcharge.tax;
        surchargesByRouteAndType[routeAndType].totalRateInSGD +=
          surcharge.rate * (exchangeRateToSGD || 0);
        surchargesByRouteAndType[routeAndType].totalTaxInSGD +=
          surcharge.tax * (exchangeRateToSGD || 0);
      } else {
        surchargesByRouteAndType[routeAndType] = {
          route: `${surcharge.origin}-${surcharge.destination}`,
          type: surcharge.type,
          rate: surcharge.rate,
          taxCode: surcharge.taxCode,
          taxRate: surcharge.taxRate,
          tax: surcharge.tax,
          totalRateInSGD: surcharge.rate * (exchangeRateToSGD || 0),
          totalTaxInSGD: surcharge.tax * (exchangeRateToSGD || 0),
        };
      }
    }

    return Object.values(surchargesByRouteAndType);
  },

  async generateRevenueTrackerSheet(workbook, invoices, month, year) {
    logger.info({
      message: `Start generate revenue tracker sheet for ${format(new Date(year, month), 'MMMM yyyy')}`,
    });

    const pdInvoiceByRouteMap = new Map();

    for (const invoice of invoices) {
      if (invoice.type === ENUM.invoiceType.PD) {
        const { routings, merchant_name, merchant_no, invoiceRateCurrency } = invoice;

        for (const route of routings) {
          const rateInSGD =
            route.totalRateInSGD ||
            (await CurrencyConversionService.exchangeMoney(
              route.rate,
              invoiceRateCurrency,
              'SGD',
              new Date(year, month),
            ));

          // Group rateInSGD
          const routeId = `${merchant_no}-${route.route}`;
          const merchantRoute = pdInvoiceByRouteMap.get(routeId);

          if (merchantRoute) {
            pdInvoiceByRouteMap.set(routeId, {
              ...merchantRoute,
              rateInSGD: +(merchantRoute.rateInSGD + rateInSGD).toFixed(2),
            });
          } else {
            pdInvoiceByRouteMap.set(routeId, {
              origin_country: route.origin_country,
              destination: route.destination,
              rateInSGD: +rateInSGD.toFixed(2),
              merchant_name: AesUtils.CrtCounterDecrypt(merchant_name),
              merchant_no: AesUtils.CrtCounterDecrypt(merchant_no),
            });
          }
        }
      }
    }

    const pdInvoiceByRoute = [...pdInvoiceByRouteMap.values()];

    logger.info({
      message: `generate revenue tracker sheet for ${format(new Date(year, month), 'MMMM yyyy')}`,
      pdInvoiceNos: invoices
        .filter((invoice) => invoice.type === ENUM.invoiceType.PD)
        .map((invoice) => invoice.invoiceNumber),
    });

    const totalPDRateInSGD = pdInvoiceByRoute
      .reduce((total, route) => total + route.rateInSGD, 0)
      ?.toFixed(2);

    const revenueContent = [
      [
        '',
        '',
        '',
        'Merchant Name',
        ...pdInvoiceByRoute.map((route) => `${route.merchant_name} (${route.merchant_no})`),
      ],
      ['', '', '', 'Export', ...pdInvoiceByRoute.map((route) => route.origin_country)],
      ['', '', '', 'Import', ...pdInvoiceByRoute.map((route) => route.destination)],
      ['Month', 'Year', 'Total Revenue (SGD)'],
      [
        format(new Date(year, month), 'MMMM'),
        year,
        +totalPDRateInSGD,
        '',
        ...pdInvoiceByRoute.map((route) => route.rateInSGD),
      ],
    ];
    const revenueTrackerSheet = XLSX.utils.json_to_sheet(revenueContent, { skipHeader: true });
    XLSX.utils.book_append_sheet(workbook, revenueTrackerSheet, 'Revenue Tracker');
  },

  getNumberOfParcelByPDRoute(pdInvoices) {
    return pdInvoices.flatMap((invoice) =>
      invoice.routings.map((route) => ({
        ...route,
        merchant_name: AesUtils.CrtCounterDecrypt(invoice.merchant_name),
        merchant_no: AesUtils.CrtCounterDecrypt(invoice.merchant_no),
      })),
    );
  },

  async getAndSetNumberOfParcelForPDinvoice(pdInvoices) {
    const numberOfParcelByRoute = [];

    for (const invoice of pdInvoices) {
      for (const route of invoice.routings) {
        const query = QueryUtils.getParcelsByInvoiceRoute(
          invoice.invoiceNumber,
          route.origin_country,
          route.destination,
        );
        const numberOfParcel = await Daos.manifest_items.find(query);
        numberOfParcelByRoute.push({
          numberOfParcel: numberOfParcel[0]?.count,
          merchant_name: AesUtils.CrtCounterDecrypt(invoice.merchant_name),
          merchant_no: AesUtils.CrtCounterDecrypt(invoice.merchant_no),
          destination: route.destination,
          origin_country: route.origin_country,
        });
        route.numberOfParcel = numberOfParcel[0]?.count;
      }

      logger.info({
        message: `Start adding numberOfParcel to invoice: ${invoice.invoiceNumber}`,
      });
      await Daos.invoice.patch({
        id: invoice.id,
        partitionKey: invoice._partitionKey,
        routings___overwrite: invoice.routings,
      });
    }

    return numberOfParcelByRoute;
  },

  async generateVolumeTrackerSheet(workbook, invoices, month, year) {
    logger.info({
      message: `Start generate volume tracker sheet for ${format(new Date(year, month), 'MMMM yyyy')}`,
      pdInvoiceNos: invoices
        .filter((invoice) => invoice.type === ENUM.invoiceType.PD)
        .map((invoice) => invoice.invoiceNumber),
    });

    const pdInvoiceHasNumberOfParcel = invoices.filter(
      (invoice) => invoice.type === ENUM.invoiceType.PD && invoice.routings[0]?.numberOfParcel,
    );
    const pdInvoiceDoesNotHaveNumberOfParcel = invoices.filter(
      (invoice) => invoice.type === ENUM.invoiceType.PD && !invoice.routings[0]?.numberOfParcel,
    );

    // get number of parcel invoiced in invoice,
    // if not, count in db (then save in invoice record so we don't need to count that any more)
    const pdRoutes = this.getNumberOfParcelByPDRoute(pdInvoiceHasNumberOfParcel).concat(
      await this.getAndSetNumberOfParcelForPDinvoice(pdInvoiceDoesNotHaveNumberOfParcel),
    );

    const pdMerchantRouteMap = new Map();

    for (const route of pdRoutes) {
      const routeId = `${route.merchant_no}-${route.route}`;
      const merchantRoute = pdMerchantRouteMap.get(routeId);

      if (merchantRoute) {
        pdMerchantRouteMap.set(routeId, {
          ...route,
          numberOfParcel: merchantRoute.numberOfParcel + route.numberOfParcel,
        });
      } else {
        pdMerchantRouteMap.set(routeId, route);
      }
    }

    const pdMerchantRoutes = [...pdMerchantRouteMap.values()];
    const totalParcel = pdMerchantRoutes.reduce((total, route) => total + route.numberOfParcel, 0);

    const volumeTrackerContent = [
      [
        '',
        '',
        '',
        'Merchant Name',
        ...pdMerchantRoutes.map((route) => `${route.merchant_name} (${route.merchant_no})`),
      ],
      ['', '', '', 'Export', ...pdMerchantRoutes.map((route) => route.origin_country)],
      ['', '', '', 'Import', ...pdMerchantRoutes.map((route) => route.destination)],
      ['Month', 'Year', 'Total Parcels'],
      [
        format(new Date(year, month), 'MMMM'),
        year,
        totalParcel,
        '',
        ...pdMerchantRoutes.map((route) => route.numberOfParcel),
      ],
    ];
    const volumeTrackerSheet = XLSX.utils.json_to_sheet(volumeTrackerContent, { skipHeader: true });
    XLSX.utils.book_append_sheet(workbook, volumeTrackerSheet, 'Volume Tracker');
  },

  async generateDTSheet(workbook, invoices, month, year) {
    logger.info({
      message: `Start generate DT Disbursement sheet for ${format(new Date(year, month), 'MMMM yyyy')}`,
    });

    const dtInvoiceByRoutesMap = new Map();

    for (const invoice of invoices) {
      if (invoice.type === ENUM.invoiceType.DTv2) {
        for (const route of invoice.routings) {
          const dtValue =
            route.local_total ||
            route.service_options.reduce((total, service) => total + service.local_total, 0);
          const dtValueInSGD =
            route.totalTaxInSGD ||
            (await CurrencyConversionService.exchangeMoney(
              dtValue,
              invoice.currency,
              'SGD',
              new Date(year, month),
            ));

          const routeId = `${invoice.merchant_no}-${route.route}`;
          const merchantRoute = dtInvoiceByRoutesMap.get(routeId);

          if (merchantRoute) {
            dtInvoiceByRoutesMap.set(routeId, {
              ...merchantRoute,
              dtValueInSGD: +(merchantRoute.dtValueInSGD + dtValueInSGD).toFixed(2),
            });
          } else {
            dtInvoiceByRoutesMap.set(routeId, {
              merchant_name: AesUtils.CrtCounterDecrypt(invoice.merchant_name),
              merchant_no: AesUtils.CrtCounterDecrypt(invoice.merchant_no),
              origin_country: route.route.split('-')[0],
              destination: route.route.split('-')[1],
              dtValueInSGD: +dtValueInSGD.toFixed(2),
            });
          }
        }
      }
    }

    logger.info({
      message: `generate DT Disbursement sheet for ${format(new Date(year, month), 'MMMM yyyy')}`,
      dtInvoiceNos: invoices
        .filter((invoice) => invoice.type === ENUM.invoiceType.PD)
        .map((invoice) => invoice.invoiceNumber),
    });

    const dtInvoiceByRoutes = [...dtInvoiceByRoutesMap.values()];

    const totalDTInSGD = dtInvoiceByRoutes
      .reduce((total, route) => total + route.dtValueInSGD, 0)
      ?.toFixed(2);
    const dtContent = [
      [
        '',
        '',
        '',
        'Merchant Name',
        ...dtInvoiceByRoutes.map((route) => `${route.merchant_name} (${route.merchant_no})`),
      ],
      ['', '', '', 'Export', ...dtInvoiceByRoutes.map((route) => route.origin_country)],
      ['', '', '', 'Import', ...dtInvoiceByRoutes.map((route) => route.destination)],
      ['Month', 'Year', 'Total Disbursed (SGD)'],
      [
        format(new Date(year, month), 'MMMM'),
        year,
        +totalDTInSGD,
        '',
        ...dtInvoiceByRoutes.map((route) => route.dtValueInSGD),
      ],
    ];
    const dtDisbursementSheet = XLSX.utils.json_to_sheet(dtContent, { skipHeader: true });
    XLSX.utils.book_append_sheet(workbook, dtDisbursementSheet, 'DT Disbursement');
  },

  async generateSurchargesAndTaxesSheet(workbook, invoices, month, year) {
    logger.info({
      message: `Start generate Surcharge and tax sheet for ${format(new Date(year, month), 'MMMM yyyy')}`,
    });

    const surchargesByRouteMap = new Map();
    const uniqSurchargeTypeSet = new Set();

    // group and calculate total surcharges by merchant and route
    for (const invoice of invoices) {
      if (invoice.surcharges?.length) {
        for (const surcharge of invoice.surcharges) {
          uniqSurchargeTypeSet.add(surcharge.type);
          const rateInSGD =
            surcharge.totalRateInSGD ||
            (await CurrencyConversionService.exchangeMoney(
              surcharge.rate,
              invoice.invoiceRateCurrency,
              'SGD',
              new Date(year, month),
            ));
          const taxInSGD =
            surcharge.totalTaxInSGD ||
            (await CurrencyConversionService.exchangeMoney(
              surcharge.tax,
              invoice.invoiceRateCurrency,
              'SGD',
              new Date(year, month),
            ));
          const surchargeRateAndTax = {
            rateInSGD: surcharge.totalRateInSGD
              ? +surcharge.totalRateInSGD.toFixed(2)
              : +rateInSGD.toFixed(2),
            taxInSGD: surcharge.totalTaxInSGD
              ? +surcharge.totalTaxInSGD.toFixed(2)
              : +taxInSGD.toFixed(2),
          };
          const routeId = `${invoice.merchant_no}-${surcharge.route}`;
          const route = surchargesByRouteMap.get(routeId);

          if (route) {
            surchargesByRouteMap.set(routeId, {
              ...route,
              totalRateInSGD: route.totalRateInSGD + surchargeRateAndTax.rateInSGD,
              totalTaxInSGD: route.totalTaxInSGD + surchargeRateAndTax.taxInSGD,
              [surcharge.type]: route[surcharge.type]
                ? {
                    rateInSGD: route[surcharge.type].rateInSGD + surchargeRateAndTax.rateInSGD,
                    taxInSGD: route[surcharge.type].taxInSGD + surchargeRateAndTax.taxInSGD,
                  }
                : surchargeRateAndTax,
            });
          } else {
            surchargesByRouteMap.set(routeId, {
              merchant_name: AesUtils.CrtCounterDecrypt(invoice.merchant_name),
              merchant_no: AesUtils.CrtCounterDecrypt(invoice.merchant_no),
              origin_country: surcharge.route === 'N/A' ? 'N/A' : surcharge.route.split('-')[0],
              destination: surcharge.route === 'N/A' ? 'N/A' : surcharge.route.split('-')[1],
              [surcharge.type]: surchargeRateAndTax,
              totalRateInSGD: surchargeRateAndTax.rateInSGD,
              totalTaxInSGD: surchargeRateAndTax.taxInSGD,
            });
          }
        }
      }
    }

    const surchargesByRoute = [...surchargesByRouteMap.values()];
    // get list all uniq surcharge type to gen vertical header
    const uniqSurchargeType = [...uniqSurchargeTypeSet];

    logger.info({
      message: `generate DT Disbursement sheet for ${format(new Date(year, month), 'MMMM yyyy')}`,
      invoiceNos: invoices
        .filter((invoice) => invoice.surcharges?.length)
        .map((invoice) => invoice.invoiceNumber),
    });

    const totalSurchargeRateInSGD = surchargesByRoute.reduce(
      (total, route) => total + route.totalRateInSGD,
      0,
    );
    const totalSurchargeTaxInSGD = surchargesByRoute.reduce(
      (total, route) => total + route.totalTaxInSGD,
      0,
    );
    // 1 type has 1 line rate 1 line tax, minus 1 line already have start by total value and 3 line for total for each route
    const numberOfLinesStartEmpty = uniqSurchargeType.length * 2 - 1 + 3;

    // total part in left side
    const surchargeTotalContent = [
      ['', '', '', '', ''],
      ['', '', '', '', ''],
      ['', '', '', '', ''],
      ['Month', 'Year', 'Total Amount (SGD)', 'Total Excl Tax (SGD)', 'Total Tax (SGD)'],
      [
        format(new Date(year, month), 'MMMM'),
        year,
        +(totalSurchargeRateInSGD + totalSurchargeTaxInSGD).toFixed(2),
        +totalSurchargeRateInSGD.toFixed(2),
        +totalSurchargeTaxInSGD.toFixed(2),
      ],
      ...Array.from({ length: numberOfLinesStartEmpty }, () => ['', '', '', '', '']),
    ];

    // detail part in right side
    const surchargeDetailContent = [
      [
        'Merchant Name',
        ...surchargesByRoute.map((route) => `${route.merchant_name} (${route.merchant_no})`),
      ],
      ['Export', ...surchargesByRoute.map((route) => route.origin_country)],
      ['Import', ...surchargesByRoute.map((route) => route.destination)],
      [],
      ...uniqSurchargeType.flatMap((surchargeType: string) => [
        [
          surchargeType,
          ...surchargesByRoute.map((route) =>
            route[surchargeType]?.rateInSGD ? +route[surchargeType].rateInSGD.toFixed(2) : '-',
          ),
        ],
        [
          '',
          ...surchargesByRoute.map((route) =>
            route[surchargeType]?.taxInSGD ? +route[surchargeType].taxInSGD.toFixed(2) : '-',
          ),
        ],
      ]),
      [
        'Total Amount (SGD)',
        ...surchargesByRoute.map((route) =>
          route.totalRateInSGD + route.totalTaxInSGD
            ? +(route.totalRateInSGD + route.totalTaxInSGD).toFixed(2)
            : '-',
        ),
      ],
      [
        'Total Excl Tax (SGD)',
        ...surchargesByRoute.map((route) =>
          route.totalRateInSGD ? +route.totalRateInSGD.toFixed(2) : '-',
        ),
      ],
      [
        'Total Tax (SGD)',
        ...surchargesByRoute.map((route) =>
          route.totalTaxInSGD ? +route.totalTaxInSGD.toFixed(2) : '-',
        ),
      ],
    ];

    // merge line in left side with right side
    const surchargeContent = surchargeTotalContent.map((line, index) =>
      line.concat(surchargeDetailContent[index]),
    );
    const surchargeAndTaxSheet = XLSX.utils.json_to_sheet(surchargeContent, { skipHeader: true });
    XLSX.utils.book_append_sheet(workbook, surchargeAndTaxSheet, 'Surcharges and Taxes');
  },

  async generateRevenueFile(month, year) {
    // Get all invoice that we have in month
    const queryInvoice = QueryUtils.getInvoicesByMonth(month, year);
    const invoices = await Daos.invoice.find(queryInvoice);

    const workbook = XLSX.utils.book_new();

    await this.generateRevenueTrackerSheet(workbook, invoices, month, year);
    logger.info({
      message: 'generateRevenueTrackerSheet success',
      time: format(new Date(year, month), 'MMMM yyyy'),
    });

    await this.generateVolumeTrackerSheet(workbook, invoices, month, year);
    logger.info({
      message: 'generateVolumeTrackerSheet success',
      time: format(new Date(year, month), 'MMMM yyyy'),
    });

    await this.generateDTSheet(workbook, invoices, month, year);
    logger.info({
      message: 'generateDTSheet success',
      time: format(new Date(year, month), 'MMMM yyyy'),
    });

    await this.generateSurchargesAndTaxesSheet(workbook, invoices, month, year);
    logger.info({
      message: 'generateSurchargesAndTaxesSheet success',
      time: format(new Date(year, month), 'MMMM yyyy'),
    });

    return XLSX.write(workbook, { type: 'buffer' });
  },
};
