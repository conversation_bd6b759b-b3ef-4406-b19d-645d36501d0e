import { SqlQuerySpec } from '@azure/cosmos';

import axios from 'axios';
import filter from 'lodash/filter.js';
import find from 'lodash/find.js';
import groupBy from 'lodash/groupBy.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { AppLogger } from '~/utilities/logUtils.js';
import { roundNumberBaseOnCurrency } from '~/utilities/numberUtils.js';
import { AzRedisClient } from '~/utilities/redis-client.js';

import { Daos } from '~/daos/index.js';

import CountryISOService from './countryISO-service.js';
import { UserServices } from './user.services.js';

const logger = new AppLogger({ name: 'TaxService' });

export const TaxService = {
  async getAllTaxCodes() {
    let taxCodes = await AzRedisClient.getCache(ENUM.cacheName.TAX_CODE);

    if (!taxCodes || taxCodes.length === 0) {
      logger.error({ message: 'cannot get data from Redis' });

      const response = await axios.get(`${process.env.FINANCE_URL}/taxCode/getAllTaxCode`);

      if (response.data.isSuccess) {
        taxCodes = response.data.message;
      } else {
        logger.error({ message: 'cannot get data from FIN', response: response.data });
      }
    }

    return taxCodes;
  },

  getTaxCodeItem(
    taxCodes,
    country: string,
    isDomestic: boolean,
    invoiceType: string,
    merchantType: string,
    chargeType: string,
    chargeSubType: string,
  ) {
    return taxCodes?.find(
      (item) =>
        CountryISOService.getCountryCode2(item.taxCountry) ===
          CountryISOService.getCountryCode2(country) &&
        item.isDomestic === isDomestic &&
        item.invoiceType?.toLowerCase() === invoiceType?.toLowerCase() &&
        item.merchantType?.toLowerCase() === merchantType?.toLowerCase() &&
        item.chargeType?.toLowerCase() === chargeType?.toLowerCase() &&
        item.chargeSubType?.toLowerCase() === (chargeSubType?.toLowerCase() || 'n/a'),
    );
  },

  filterMerchantTaxCodes(allTaxCodes: any, taxCountry: string, merchantType: string = '') {
    return filter(allTaxCodes, (taxCode) => {
      return (
        CountryISOService.getCountryCode2(taxCode.taxCountry) ===
          CountryISOService.getCountryCode2(taxCountry) &&
        taxCode.merchantType.toLowerCase() === merchantType.toLowerCase()
      );
    });
  },

  getTaxDisplay(taxCountry: string, taxCountryManagementInfo: any[]) {
    const taxDisplay = taxCountryManagementInfo.find((item) => item.taxCountry === taxCountry);

    return taxDisplay;
  },

  getTaxCode(
    merchantTaxCodes: any,
    isDomestic: boolean,
    chargeType: string = '',
    chargeSubType: string = '',
    invoiceType: string = ENUM.invoiceType.PD,
  ) {
    return (
      find(merchantTaxCodes, (taxCode) => {
        return (
          taxCode.isDomestic === isDomestic &&
          taxCode.chargeType.toLowerCase() === chargeType.toLowerCase() &&
          taxCode.invoiceType.toLowerCase() === invoiceType.toLowerCase() &&
          taxCode.chargeSubType.toLowerCase() === chargeSubType.toLowerCase()
        );
      }) || { taxCode: '', taxRate: 0 }
    );
  },

  filterMerchantTaxRates(allTaxRates: any, taxType = '', taxCountry = '') {
    return filter(allTaxRates, (taxRate) => {
      return (
        taxRate.tax_type.toLowerCase() === taxType.toLowerCase() &&
        taxRate.country.toLowerCase() === taxCountry.toLowerCase()
      );
    });
  },

  calculateTax(
    merchant: any,
    isDomestic: boolean,
    chargeType: any,
    chargeAmount: string,
    invoiceRateCurrency: string,
    chargeSubtype: any,
  ) {
    const taxCodeData = this.getTaxCode(merchant.taxCodes, isDomestic, chargeType, chargeSubtype);
    const { taxCode, taxRate } = taxCodeData;

    const rate = roundNumberBaseOnCurrency(chargeAmount, invoiceRateCurrency);

    return {
      chargeType,
      rate,
      taxCode,
      taxRate: Number(taxRate) || 0,
      tax: Number(taxRate) === 0 ? 0 : (+taxRate * rate) / 100,
    };
  },

  groupByTaxCode(taxItems) {
    const itemsByTaxCode = groupBy(taxItems, 'taxCode');

    return Object.values(itemsByTaxCode).map((items) => {
      const itemHaveTaxRate = items?.find((item) => item.taxRate);
      const result: any = {
        taxCode: items[0].taxCode,
        taxRate: itemHaveTaxRate?.taxRate || 0,
        rate: items.reduce((sum, item) => sum + (item.rate || item.totalActualTax || 0), 0),
        tax: items.reduce((sum, item) => sum + (item.tax || 0), 0),
      };

      if (items.some((item) => 'totalTaxInSGD' in item)) {
        result.taxInSGD = items.reduce((sum, item) => sum + (item.totalTaxInSGD || 0), 0);
      }

      return result;
    });
  },

  groupByRouteAndTaxCode(items) {
    const itemsByRoute = groupBy(items, 'route');

    return Object.values(itemsByRoute).flatMap((items) => {
      return this.groupByTaxCode(items).map((item) => ({
        route: items[0]?.route,
        ...item,
      }));
    });
  },

  async saveDutyAndTax(dutyAndTax: any) {
    await Daos.tax_rate.addItem(dutyAndTax);
  },

  async editDutyAndTax(taxId: string, editData: any, _partitionKey: string | undefined) {
    await Daos.tax_rate.updateItemResolveConflict(
      {
        id: taxId,
        ...editData,
      },
      _partitionKey,
    );
  },

  async getDutyAndTax(country: string) {
    let querySpec: SqlQuerySpec = {
      query: `SELECT c.country,c.currency,c.de_minimis,c.effective_date,c.id,
      c.latest_status,c.product_specific,c.tax_percentage,c.tax_type,c.value_to_check,c.value_used_for_calculation,
      c.latest_update
      FROM c 
      WHERE c._partitionKey = @partitionKey
      AND c.country = @country`,
      parameters: [
        {
          name: '@partitionKey',
          value: ENUM.taxRate.type.duty_and_tax,
        },
        {
          name: '@country',
          value: AesUtils.CrtCounterEncrypt(country),
        },
      ],
    };
    const taxRates = await Daos.tax_rate.find(querySpec);

    if (taxRates.length === 0) return [];
    const taxRateIds = taxRates.map((t) => t.id);

    querySpec = {
      query: `SELECT c.action,c.approver_id,c.approver_name,c.id,c.status,
      c.tax_id,c.time,c.user_name,c.user_id, c.comment
      FROM c 
      WHERE c._partitionKey = @partitionKey
      AND ARRAY_CONTAINS(@taxRateIds, c.tax_id)`,
      parameters: [
        {
          name: '@partitionKey',
          value: ENUM.taxRate.type.duty_and_tax_approval_path,
        },
        {
          name: '@taxRateIds',
          value: taxRateIds,
        },
      ],
    };
    let taxRatesApprovalPath: any = await Daos.tax_rate.find(querySpec);
    taxRatesApprovalPath = groupBy(taxRatesApprovalPath, 'tax_id');

    taxRates.forEach((rate: any) => {
      rate.country = AesUtils.CrtCounterDecrypt(rate.country);
      rate.approval_path = taxRatesApprovalPath[rate.id];

      if (Array.isArray(rate.approval_path)) {
        rate.approval_path.sort((a: any, b: any) => b.time.localeCompare(a.time));
        rate.approval_path.forEach((approvalDetail: any) => {
          approvalDetail.user_name = approvalDetail.user_name
            ? AesUtils.CrtCounterDecrypt(approvalDetail.user_name)
            : approvalDetail.user_name;
          approvalDetail.approver_name = approvalDetail.approver_name
            ? AesUtils.CrtCounterDecrypt(approvalDetail.approver_name)
            : approvalDetail.approver_name;
        });
      }
    });

    return taxRates;
  },

  async getLatestDutyAndTaxApproval(taxId: string) {
    const querySpec = {
      query: `SELECT * FROM c 
      WHERE c._partitionKey = @partitionKey
      AND c.tax_id = @taxId
      ORDER BY c.time DESC OFFSET 0 LIMIT 1`,
      parameters: [
        {
          name: '@partitionKey',
          value: ENUM.taxRate.type.duty_and_tax_approval_path,
        },
        {
          name: '@taxId',
          value: taxId,
        },
      ],
    };
    const approvalPaths = await Daos.tax_rate.find(querySpec);

    return approvalPaths[0];
  },

  getTaxById(taxId: string, _partitionKey: string) {
    return Daos.tax_rate.getItem(taxId, _partitionKey);
  },

  async getDutyAndTaxMasterRecord(country: string, status: string = '') {
    const querySpec = {
      query: `SELECT * 
      FROM c 
      WHERE c._partitionKey = @partitionKey
      AND c.country = @country
      ${status ? 'AND c.latest_status = @status' : ''}
      `,
      parameters: [
        {
          name: '@partitionKey',
          value: ENUM.taxRate.type.duty_and_tax,
        },
        {
          name: '@country',
          value: country,
        },
        {
          name: '@status',
          value: status,
        },
      ],
    };

    return Daos.tax_rate.find(querySpec);
  },

  async getTariffRateById(tariffRateId: string) {
    const querySpec = {
      query: "SELECT * FROM c WHERE c._partitionKey = 'duty_and_tax' AND c.id = @id",
      parameters: [
        {
          name: '@id',
          value: tariffRateId,
        },
      ],
    };

    return Daos.tax_rate.find(querySpec);
  },

  async checkExisting(country: string, state: string, tax_type: string, type: string) {
    const querySpec = {
      query: `SELECT * FROM c WHERE c._partitionKey = @type AND c.country=@country 
      AND c.state = @state AND c.tax_type = @tax_type 
      AND c.status != 'Rejected'`,
      parameters: [
        { name: '@country', value: AesUtils.CrtCounterEncrypt(country) },
        { name: '@state', value: AesUtils.CrtCounterEncrypt(state) },
        { name: '@tax_type', value: AesUtils.CrtCounterEncrypt(tax_type) },
        { name: '@type', value: type },
      ],
    };
    const taxRates = await Daos.tax_rate.find(querySpec);

    return taxRates.length > 0;
  },

  checkValidFromDate(valid_from: any) {
    const validFromDate = new Date(valid_from);
    const current = new Date();

    return validFromDate.getTime() > current.getTime();
  },

  async validateApprover(approver: string): Promise<boolean> {
    const HODUserList = await UserServices.getUserByRole([
      ENUM.USER_ROLES_SERVER.head_of_department,
    ]);
    const HODUser = HODUserList.data.find((receivedUser: any) => receivedUser.name === approver);

    return Boolean(HODUser);
  },

  async isTaxRateExist(country: string, taxType: string, partitionKey: string) {
    const querySpec = {
      query: `SELECT * FROM c 
      WHERE c._partitionKey = @partitionKey
      AND c.country = @country
      AND c.version = 2
      AND c.tax_type = @taxType`,
      parameters: [
        { name: '@country', value: AesUtils.CrtCounterEncrypt(country) },
        { name: '@taxType', value: AesUtils.CrtCounterEncrypt(taxType) },
        { name: '@partitionKey', value: partitionKey },
      ],
    };
    const taxRates = await Daos.tax_rate.find(querySpec);

    return taxRates.length > 0;
  },
};
