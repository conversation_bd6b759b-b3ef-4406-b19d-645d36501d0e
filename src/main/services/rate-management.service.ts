import { differenceInMilliseconds, endOfDay, startOfDay } from 'date-fns';
import XLSX, { WorkSheet } from 'xlsx';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import { CommonUtils } from '~/utilities/commonUtils.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { checkCellsEmpty, getCellValue } from '~/utilities/rate-sheet.util.js';

import { Daos } from '~/daos/index.js';
import { ValueOf } from '~/types/parcel-cancel.type.js';
import {
  IMerchantRate,
  IMerchantRateV2,
  IMerchantRateItem,
  IMerchantRateItemV2,
  IRateSheetValidationResult,
  TRateSheetServiceOption,
} from '~/types/rate.type.js';

import CountryISOService from './countryISO-service.js';
import { MerchantService } from './merchant-service.js';
import { RateServices } from './rate-services.js';

const logger = new AppLogger({ name: 'RateManagementService' });

export const RateManagementService = {
  async validateRateSheetDataV1(rateSheet: WorkSheet) {
    const startDataRow = 8;
    const range = XLSX.utils.decode_range(rateSheet['!ref']);
    const rowCount = range.e.r;
    const colCount = range.e.c;

    // check rate sheet v1 has correct number of columns
    if (colCount !== 7) {
      return {
        success: false,
        message: 'The number of columns must be 8',
      };
    }

    // check if header values are valid
    const headerCheck = this.validateHeaders(rateSheet);

    if (!headerCheck.success) {
      return headerCheck;
    }

    // check if cells are not empty
    const checkRange = {
      row: {
        startIdx: startDataRow,
        endIdx: rowCount,
      },
      col: {
        startIdx: 1,
        endIdx: colCount,
      },
    };
    const tbCellCheck = checkCellsEmpty(rateSheet, checkRange);

    if (!tbCellCheck.success) {
      return tbCellCheck;
    }

    // validate sheet data and generate rate sheet array simultaneously
    const validationMessages = {
      origin: 'Origin should be a valid 2-letter country code',
      destination: 'Destination should be a valid 2-letter country code',
      serviceOption: 'Incorrect input Service Option',
      weight: 'Weight must be number',
      unit: 'Incorrect input Weight Unit',
      rateCurrency: 'Incorrect input Rate Currency',
      rate: 'Rate must be number',
    };

    try {
      const [code2List, arrRateCurrency] = await Promise.all([
        CountryISOService.getCountriesList().then(
          (countries) => new Set(countries.map((ct) => ct.codeAlpha2)),
        ),
        CountryISOService.getCurrencyList(),
      ]);
      const arrServiceOption = new Set(['standard', 'plus', 'self-collect', 'freight', 'postal']);
      const arrWeightUnit = new Set(['lb', 'kg']);
      const arrRateSheet = [];

      for (let row = 0; row <= rowCount; row++) {
        const arrCols = [];

        for (let col = 0; col <= colCount; col++) {
          const cellValue = getCellValue(rateSheet, col, row);

          if (row < startDataRow) {
            arrCols.push(cellValue);

            continue;
          }

          // check col Origin
          if (col === 0 && !code2List.has(cellValue)) {
            return {
              success: false,
              message: validationMessages.origin,
            };
          }

          // check col Destination
          if (col === 1 && !code2List.has(cellValue)) {
            return {
              success: false,
              message: validationMessages.destination,
            };
          }

          // check col Service Option
          if (col === 2 && !arrServiceOption.has(cellValue.toLowerCase())) {
            return {
              success: false,
              message: validationMessages.serviceOption,
            };
          }

          // check col Weight format
          if (col === 3 && Number.isNaN(+cellValue)) {
            return {
              success: false,
              message: validationMessages.weight,
            };
          }

          // check col Weight Unit
          if (col === 4 && !arrWeightUnit.has(cellValue)) {
            return {
              success: false,
              message: validationMessages.unit,
            };
          }

          // check col Rate Currency
          if (col === 5 && !arrRateCurrency.includes(cellValue)) {
            return {
              success: false,
              message: validationMessages.rateCurrency,
            };
          }

          // check col Rate format
          if (col === 6 && Number.isNaN(+cellValue)) {
            return {
              success: false,
              message: validationMessages.rate,
            };
          }

          arrCols.push(cellValue);
        }

        arrRateSheet.push(arrCols);
      }

      // check col LMD Zone
      const checkLmdZone = await this.validateLmdZoneCol(rateSheet);

      if (checkLmdZone && !checkLmdZone.success) {
        return checkLmdZone;
      }

      return {
        success: true,
        data: arrRateSheet,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  },

  validateDataV2(rateSheetArr: Array<any>, code2List: Set<string>) {
    const serviceOptions = new Set([
      'postal',
      'standard',
      'domestic',
      'plus',
      'self-collect',
      'b2b',
    ]);
    let tableColCount: number;
    // because the number of columns is varied, minimum number of colCount is the length of the top (metadata) section, which is 6
    const minSheetColCount = 6;
    const actualSheetColCount = rateSheetArr[0].length;
    const countryRowIdx = 10;
    const firstUndefinedCountryIdx = rateSheetArr[countryRowIdx].indexOf(undefined);
    const otherCellsInSameColumnEmpty = rateSheetArr
      .filter((_, idx) => idx > countryRowIdx)
      .every((row) => {
        return row[firstUndefinedCountryIdx] === undefined || row[firstUndefinedCountryIdx] === '';
      });

    /**
     * Number of table columns could be less than the total number of sheet columns.
     * Thus we look for empty (undefined) cell in country row (index = 10).
     * If empty, check for all other cell values in the same column.
     * If all empty, there's no more table column.
     */
    if (
      actualSheetColCount === minSheetColCount &&
      firstUndefinedCountryIdx !== -1 &&
      otherCellsInSameColumnEmpty
    ) {
      tableColCount = firstUndefinedCountryIdx;
    } else if (
      actualSheetColCount === minSheetColCount &&
      firstUndefinedCountryIdx !== -1 &&
      !otherCellsInSameColumnEmpty
    ) {
      // Else, missing country, take this column for later validations
      tableColCount = firstUndefinedCountryIdx + 1;
    } else if (actualSheetColCount > minSheetColCount) {
      tableColCount = actualSheetColCount;
    }

    for (let row = countryRowIdx; row < rateSheetArr.length; row++) {
      for (let col = 1; col < tableColCount; col++) {
        const cellValue = rateSheetArr[row][col];

        // verify country
        if (row === 10 && !code2List.has(cellValue?.trim().toLowerCase())) {
          throw new Error(`Error: Invalid country: ${cellValue}`);
        }

        // verify service options
        if (row === 11 && !serviceOptions.has(cellValue?.trim().toLowerCase())) {
          throw new Error(
            `Error: Invalid Service Option applied for ${rateSheetArr[row - 1][col]}`,
          );
        }

        // verify rates
        if (row >= 12 && Number.isNaN(+cellValue)) {
          throw new Error('Error: Invalid rates');
        }
      }
    }
  },

  async validateRateSheetDataV2(
    rateSheet: WorkSheet,
    serviceOption: ValueOf<typeof ENUM.RATE_SHEET_SERVICE_OPTION>,
    isFlatRate = false,
  ) {
    const range = XLSX.utils.decode_range(rateSheet['!ref']);
    const rowCount = range.e.r;
    const colCount = range.e.c;
    const currencies = new Set();
    const code2List = new Set<string>();
    const weightUnits = new Set(['kg', 'lb']);

    try {
      const countries = await CountryISOService.getCountriesList();

      for (const country of countries) {
        currencies.add(country.currency?.toLowerCase());
        code2List.add(country.codeAlpha2?.toLowerCase());
      }

      const rateSheetArr = [];
      let emptyRows = 0;

      for (let row = 0; row <= rowCount; row++) {
        const arrCols = [];

        for (let col = 0; col <= colCount; col++) {
          const cellValue = getCellValue(rateSheet, col, row);

          // verify currency
          if (row === 6 && col === 2 && !currencies.has(cellValue?.trim().toLowerCase())) {
            return {
              success: false,
              message: 'Error: Invalid currency',
            };
          }

          // verify weight unit
          if (row === 8 && col === 2 && !weightUnits.has(cellValue?.trim().toLowerCase())) {
            return {
              success: false,
              message: 'Error: Invalid weight unit',
            };
          }

          // verify incremental
          if (
            serviceOption === ENUM.RATE_SHEET_SERVICE_OPTION.B2C &&
            row === 12 &&
            col === 0 &&
            cellValue?.trim() !== 'Incremental'
          ) {
            return {
              success: false,
              message: 'Error: Incremental rates are not defined',
            };
          }

          // verify weight breaks
          if (
            serviceOption === ENUM.RATE_SHEET_SERVICE_OPTION.B2C &&
            row >= 13 &&
            col === 0 &&
            Number.isNaN(+cellValue)
          ) {
            emptyRows += 1;
          }

          if (
            serviceOption === ENUM.RATE_SHEET_SERVICE_OPTION.B2B &&
            col === 0 &&
            ((!isFlatRate && row >= 12) || (isFlatRate && row >= 13)) &&
            Number.isNaN(+cellValue)
          ) {
            emptyRows += 1;
          }

          arrCols.push(cellValue);
        }

        rateSheetArr.push(arrCols);
      }

      /**
       * Separate validations for countries, service options and rates.
       * Number of columns in v2 is varied hence `colCount` variable is not reliable.
       */
      this.validateDataV2(rateSheetArr, code2List);

      return {
        success: true,
        data: rateSheetArr,
        emptyRows,
      };
    } catch (error) {
      return {
        success: false,
        message: error.message,
      };
    }
  },

  async validateB2CRateSheet(
    rateSheet: WorkSheet,
    data: { merchantAccNoFromRequest: string },
  ): Promise<IRateSheetValidationResult> {
    const { merchantAccNoFromRequest } = data;
    const cellA1 = getCellValue(rateSheet, 0, 0);
    const cellA7 = getCellValue(rateSheet, 0, 6);
    const cellC3 = getCellValue(rateSheet, 2, 2);
    const cellC4 = getCellValue(rateSheet, 2, 3);
    const cellC5 = getCellValue(rateSheet, 2, 4);
    const cellC6 = getCellValue(rateSheet, 2, 5);
    const cellE5 = getCellValue(rateSheet, 4, 4);
    const cellC7 = getCellValue(rateSheet, 2, 6);
    const cellC10 = getCellValue(rateSheet, 2, 9);
    const isVersion2 = !!cellC7 && !cellC10 && cellA7.trim() === 'Currency';

    // check if metadata is fully populated
    if (!cellA1 || !cellC3 || !cellC4 || !cellC5 || !cellC6 || !cellE5) {
      return {
        success: false,
        message:
          'Error Encountered. File format has issues. Please contact your administrator for assistance',
      };
    }

    // check if chargeable weight value is valid
    const chargeableWeightArr = [
      'higher of gross/volumetric weight',
      'gross weight',
      'volumetric weight',
    ];

    if (!chargeableWeightArr.includes(cellC6.trim().toLowerCase())) {
      return {
        success: false,
        message: isVersion2 ? 'Error: Invalid chargeable component.' : 'Invalid Chargeable Weight',
      };
    }

    // check if merchant account number from sheet matches from request query
    const merchantAccountNumber = cellC4.trim();

    if (merchantAccountNumber !== merchantAccNoFromRequest) {
      return {
        success: false,
        message: `Merchant account ${merchantAccountNumber} does not match with the merchant which the file is uploaded to`,
      };
    }

    // check if merchant is in the system
    const merchantName = cellC3.trim();
    const isMerchantExisted = await MerchantService.checkMerchantExist(
      merchantName,
      merchantAccountNumber,
    );

    if (!isMerchantExisted) {
      return {
        success: false,
        message: isVersion2
          ? 'Error: Merchant Name and/or Account No. does not exist. Please check your file.'
          : `Merchant ${merchantName} with account number ${merchantAccountNumber} does not exist!`,
      };
    }

    // check if rates validity is valid
    const dateAtC5 = Date.parse(cellC5);
    const dateAtE5 = Date.parse(cellE5);
    const validFrom = startOfDay(new Date(cellC5));
    const validTo = endOfDay(new Date(cellE5));
    const isDateValid = differenceInMilliseconds(validFrom, validTo) < 0;

    if (Number.isNaN(+dateAtC5) || Number.isNaN(+dateAtE5) || !isDateValid) {
      return {
        success: false,
        message: 'Error: Invalid dates',
      };
    }

    // data validation for each version
    const dataValidationResult = isVersion2
      ? await this.validateRateSheetDataV2(rateSheet, ENUM.RATE_SHEET_SERVICE_OPTION.B2C)
      : await this.validateRateSheetDataV1(rateSheet);

    if (!dataValidationResult.success) {
      return dataValidationResult;
    }

    return {
      success: true,
      data: {
        rates: dataValidationResult['data'],
        version: isVersion2 ? 2 : 1,
        emptyRows: dataValidationResult['emptyRows'] || 0,
      },
    };
  },

  async validateB2BRateSheet(
    rateSheet: WorkSheet,
    data: { merchantAccNoFromRequest: string },
    serviceOption: TRateSheetServiceOption,
  ): Promise<IRateSheetValidationResult> {
    const { merchantAccNoFromRequest } = data;
    const title = getCellValue(rateSheet, 0, 0);
    const merchantName = getCellValue(rateSheet, 2, 2);
    const merchantAccountNumber = getCellValue(rateSheet, 2, 3);
    const startDate = getCellValue(rateSheet, 2, 4);
    const chargeableWeight = getCellValue(rateSheet, 2, 5);
    const endDate = getCellValue(rateSheet, 4, 4);
    const currency = getCellValue(rateSheet, 2, 6);
    const weightUnit = getCellValue(rateSheet, 2, 8);
    const rateChargeType = getCellValue(rateSheet, 0, 12);
    const isFlatRateCharge =
      !!rateChargeType && rateChargeType.trim() === ENUM.RATE_CHARGE_TYPE.FLAT_RATE;

    // check if metadata is fully populated
    if (
      !title ||
      !merchantName ||
      !merchantAccountNumber ||
      !startDate ||
      !chargeableWeight ||
      !currency ||
      !weightUnit ||
      !endDate
    ) {
      return {
        success: false,
        message:
          'Error Encountered. File format has issues. Please contact your administrator for assistance',
      };
    }

    // check if merchant account number from sheet matches from request query
    if (merchantAccountNumber.trim() !== merchantAccNoFromRequest) {
      return {
        success: false,
        message: `Merchant account ${merchantAccountNumber.trim()} does not match with the merchant which the file is uploaded to`,
      };
    }

    // check if merchant is in the system
    const isMerchantExisted = await MerchantService.checkMerchantExist(
      merchantName.trim(),
      merchantAccountNumber,
    );

    if (!isMerchantExisted) {
      return {
        success: false,
        message: 'Error: Merchant Name and/or Account No. does not exist. Please check your file.',
      };
    }

    // check if rates validity is valid
    const dateAtC5 = Date.parse(startDate);
    const dateAtE5 = Date.parse(endDate);
    const validFrom = startOfDay(new Date(startDate));
    const validTo = endOfDay(new Date(endDate));
    const isDateValid = differenceInMilliseconds(validFrom, validTo) < 0;

    if (Number.isNaN(+dateAtC5) || Number.isNaN(+dateAtE5) || !isDateValid) {
      return {
        success: false,
        message: 'Error: Invalid dates',
      };
    }

    // check if chargeable weight value is valid
    const chargeableWeightArr = [
      'higher of gross/volumetric weight',
      'gross weight',
      'volumetric weight',
    ];

    if (!chargeableWeightArr.includes(chargeableWeight.trim().toLowerCase())) {
      return {
        success: false,
        message: 'Error: Invalid Chargeable Weight',
      };
    }

    // Data validation for each version
    const dataValidationResult = await this.validateRateSheetDataV2(
      rateSheet,
      serviceOption,
      isFlatRateCharge,
    );

    if (!dataValidationResult.success) {
      return {
        success: false,
        message: dataValidationResult.message,
      };
    }

    return {
      success: true,
      data: {
        rates: dataValidationResult.data,
        emptyRows: dataValidationResult.emptyRows || 0,
        version: 2,
        rateChargeType: isFlatRateCharge
          ? ENUM.RATE_CHARGE_TYPE.FLAT_RATE
          : ENUM.RATE_CHARGE_TYPE.WEIGHT_BREAK,
      },
    };
  },

  async deactivateMerchantRate(rateId: string, partitionKey: string) {
    return Daos.rateMerchant.patch({
      id: rateId,
      isActive: false,
      partitionKey,
    });
  },

  async deactivateMerchantRateItems(rateId: string, encryptedMerchantAccountNumber: string) {
    try {
      const querySpec = {
        query: `SELECT * FROM rateMerchant c 
                WHERE c._partitionKey = @merchant_account_number 
                AND c.type = 'merchant_rate_item' 
                AND c.rate_table_id = @id`,
        parameters: [
          {
            name: '@id',
            value: rateId,
          },
          {
            name: '@merchant_account_number',
            value: encryptedMerchantAccountNumber,
          },
        ],
      };
      const merchantRateItems = await Daos.rateMerchant.find(querySpec);

      if (merchantRateItems.length > 0) {
        for (const items of this.getBatch(merchantRateItems, 100)) {
          await Promise.all(
            items.map((item: any) =>
              Daos.rateMerchant.patch({
                id: item.id,
                active: false,
                partitionKey: item._partitionKey,
              }),
            ),
          );

          await CommonUtils.sleep(500);
        }
      }
    } catch (error) {
      logger.error({ error });
    }
  },

  async createNewActiveMerchantRate(
    rateSheetArr: any,
    version: 1 | 2,
    serviceOptionRateSheet: TRateSheetServiceOption,
    isFlatRate: boolean,
  ): Promise<IMerchantRate | IMerchantRateV2> {
    const merchantAccountNumber = rateSheetArr[3][2].trim();
    const encryptedMerchantAccNo = AesUtils.CrtCounterEncrypt(merchantAccountNumber);
    const validityFrom = startOfDay(new Date(rateSheetArr[4][2]));
    const validityTo = endOfDay(new Date(rateSheetArr[4][4]));
    const chargeableWeight = rateSheetArr[5][2];
    const baseRate = {
      merchant_account_number: encryptedMerchantAccNo,
      validity_from: validityFrom,
      validity_to: validityTo,
      chargeable_weight: chargeableWeight,
      status: 'Created',
      isActive: true,
      _partitionKey: encryptedMerchantAccNo,
      type: 'merchant_rate',
      is_uploading: true,
      service_option: ENUM.RATE_SHEET_SERVICE_OPTION.B2C,
      version: 1,
    };
    let newRate;

    if (serviceOptionRateSheet === ENUM.RATE_SHEET_SERVICE_OPTION.B2B) {
      logger.info({
        message: `Start create new B2B merchant ratesheet`,
      });
      const minimumCharge = {};
      const countryRowIdx = 10;
      const rowLength = rateSheetArr[countryRowIdx].filter(Boolean).length;

      for (let i = 1; i <= rowLength; i++) {
        const destinationCode = rateSheetArr[10][i]?.trim();
        const minimumChargeValue = +rateSheetArr[11][i];

        if (!minimumCharge[destinationCode]) {
          minimumCharge[destinationCode] = {};
        }

        minimumCharge[destinationCode] = minimumChargeValue;
      }

      newRate = {
        ...baseRate,
        version: 2,
        currency: rateSheetArr[6][2]?.trim(),
        weight_unit: rateSheetArr[8][2]?.trim().toLowerCase(),
        service_option: ENUM.RATE_SHEET_SERVICE_OPTION.B2B,
        minimum_charge: minimumCharge,
        is_flat_rate: isFlatRate,
      };
    } else {
      logger.info({
        message: `Start create new B2C merchant ratesheet`,
      });

      if (version === 2) {
        const incremental = {};
        const countryRowIdx = 10;
        const rowLength = rateSheetArr[countryRowIdx].filter(Boolean).length;

        for (let i = 1; i < rowLength; i++) {
          const destinationCode = rateSheetArr[10][i]?.trim();
          const serviceOption = rateSheetArr[11][i]?.trim().toLowerCase();
          const incrementalValue = +rateSheetArr[12][i];

          if (!incremental[destinationCode]) {
            incremental[destinationCode] = {};
          }

          incremental[destinationCode][serviceOption] = incrementalValue;
        }

        newRate = {
          ...baseRate,
          version: 2,
          currency: rateSheetArr[6][2]?.trim(),
          incremental_weight_break: +rateSheetArr[7][2],
          weight_unit: rateSheetArr[8][2]?.trim().toLowerCase(),
          incremental,
        };
      }
    }

    logger.info({
      message: `Start add new merchant ratesheet to db: ${newRate}`,
    });

    const newMerchantRate = await Daos.rateMerchant.addItem(newRate);

    return newMerchantRate;
  },

  async updateRateAndRateItems(
    fileName: string,
    rateSheetArr: any,
    version: 1 | 2,
    serviceOption: TRateSheetServiceOption,
    isFlatRate: boolean,
  ): Promise<IMerchantRate | IMerchantRateV2> {
    const merchantAccountNumber = rateSheetArr[3][2]?.trim();
    const encryptedMerchantAccountNumber = AesUtils.CrtCounterEncrypt(merchantAccountNumber);
    const validity = {
      from: startOfDay(new Date(rateSheetArr[4][2])) as unknown as string,
      to: endOfDay(new Date(rateSheetArr[4][4])) as unknown as string,
    };
    const merchantRate = await RateServices.getRateByValidityDates(merchantAccountNumber, validity);

    if (merchantRate) {
      logger.info({
        message: `There is a merchant rate for ${fileName} already. All merchant rate items belong to this merchant rate will be updated`,
      });

      this.deactivateMerchantRateItems(merchantRate.id, encryptedMerchantAccountNumber);
      await this.deactivateMerchantRate(merchantRate.id, encryptedMerchantAccountNumber);
    } else {
      logger.info({
        message: `There is no merchant rate available for ${fileName}. New rate merchant items will be created`,
      });
    }

    return this.createNewActiveMerchantRate(rateSheetArr, version, serviceOption, isFlatRate);
  },

  async createMerchantRateItemsV2(rateSheetArray, data: { fileName: string; rateId: string }) {
    const { fileName, rateId } = data;

    const arrData = [];
    const startDataRowIndex = 13;
    const countryRowIdx = 10;
    const tableRowLength = rateSheetArray[countryRowIdx].filter(Boolean).length;

    for (let i = 1; i < tableRowLength; i++) {
      for (let j = startDataRowIndex; j < rateSheetArray.length; j++) {
        const countryCode = rateSheetArray[startDataRowIndex - 3][i]?.trim();
        const serviceOption = rateSheetArray[startDataRowIndex - 2][i]?.trim().toLowerCase();
        const rate = Number(rateSheetArray[j][i]?.trim());
        const weight = +rateSheetArray[j][0];

        arrData.push({
          weight,
          country_code: countryCode,
          service_option: serviceOption,
          rate,
          rate_table_id: rateId,
          active: true,
          merchant_account_number: rateSheetArray[3][2]?.trim(),
          type: 'merchant_rate_item',
        });
      }
    }

    // Copy new one to use before "getBatch"
    // Because while getting batch to create merchant rate items, the "arrData" will be spliced and reduced to 0
    const merchantRateItems = arrData;

    logger.info({
      message: `Start updating ${fileName} / ${rateId}.xlsx into the database.`,
      info: `${arrData.length} merchant rate items will be created`,
    });

    const now = performance.now();

    for (const batch of this.getBatch(arrData, 100)) {
      await Promise.all(
        batch.map((item: IMerchantRateItemV2) => this.createMerchantRateItem(item)),
      );

      await CommonUtils.sleep(500);
    }

    logger.info({
      message: `Finish updating ${fileName} / ${rateId}.xlsx into the database`,
      info: `${merchantRateItems.length > 0 || rateSheetArray.length - startDataRowIndex} merchant rate items were created`,
      duration: `${Math.round(performance.now() - now)}ms`,
    });

    return merchantRateItems;
  },

  async createB2BWeightBreakMerchantRateItem(
    rateSheetArray,
    data: { fileName: string; rateId: string; emptyRows: number },
  ) {
    const { fileName, rateId, emptyRows } = data;

    const arrData = [];
    const startDataRowIndex = 12;
    const countryRowIdx = 10;
    const tableRowLength = rateSheetArray[countryRowIdx].filter(Boolean).length;
    const lengthOfDataRows = rateSheetArray.length - emptyRows;

    for (let i = 1; i <= tableRowLength; i++) {
      for (let j = startDataRowIndex; j < lengthOfDataRows; j++) {
        const countryCode = rateSheetArray[startDataRowIndex - 2][i]?.trim();
        const rate = Number(rateSheetArray[j][i]?.trim());
        const weight = +rateSheetArray[j][0];

        arrData.push({
          weight,
          country_code: countryCode,
          rate,
          rate_table_id: rateId,
          active: true,
          merchant_account_number: rateSheetArray[3][2]?.trim(),
          type: 'merchant_rate_item',
        });
      }
    }

    // Copy new one to use before "getBatch"
    // Because while getting batch to create merchant rate items, the "arrData" will be spliced and reduced to 0
    const merchantRateItems = [...arrData];

    logger.info({
      message: `Start updating ${fileName} / ${rateId}.xlsx into the database.`,
      info: `${arrData.length} merchant rate items will be created`,
    });

    const now = performance.now();

    for (const batch of this.getBatch(arrData, 100)) {
      await Promise.all(
        batch.map((item: IMerchantRateItemV2) => this.createMerchantRateItem(item)),
      );

      await CommonUtils.sleep(500);
    }

    logger.info({
      message: `Finish updating ${fileName} / ${rateId}.xlsx into the database`,
      info: `${merchantRateItems.length} merchant rate items were created`,
      duration: `${Math.round(performance.now() - now)}ms`,
    });

    return merchantRateItems;
  },

  async createB2BFlatRateMerchantRateItem(
    rateSheetArray,
    data: { fileName: string; rateId: string },
  ) {
    const { fileName, rateId } = data;

    const arrData = [];
    const startDataRowIndex = 12;
    const countryRowIdx = 10;
    const tableRowLength = rateSheetArray[countryRowIdx].filter(Boolean).length;

    for (let i = 1; i <= tableRowLength; i++) {
      const countryCode = rateSheetArray[10][i]?.trim();
      const rate = Number(rateSheetArray[12][i]?.trim());

      arrData.push({
        country_code: countryCode,
        rate,
        rate_table_id: rateId,
        active: true,
        merchant_account_number: rateSheetArray[3][2]?.trim(),
        type: 'merchant_rate_item',
      });
    }

    // Copy new one to use before "getBatch"
    // Because while getting batch to create merchant rate items, the "arrData" will be spliced and reduced to 0
    const merchantRateItems = [...arrData];

    logger.info({
      message: `Start updating ${fileName} / ${rateId}.xlsx into the database.`,
      info: `${arrData.length} merchant rate items will be created`,
    });

    const now = performance.now();

    for (const batch of this.getBatch(arrData, 100)) {
      await Promise.all(
        batch.map((item: IMerchantRateItemV2) => this.createMerchantRateItem(item)),
      );

      await CommonUtils.sleep(500);
    }

    logger.info({
      message: `Finish updating ${fileName} / ${rateId}.xlsx into the database`,
      info: `${merchantRateItems.length > 0 || rateSheetArray.length - startDataRowIndex} merchant rate items were created`,
      duration: `${Math.round(performance.now() - now)}ms`,
    });

    return merchantRateItems;
  },

  async createMerchantRateItems(
    rateSheet: WorkSheet,
    rateSheetArray,
    data: { fileName: string; rateId: string },
  ) {
    const { fileName, rateId } = data;
    /* map input lmd zone to its correct lmd zone if its characters are correct 
    eg: 'Ha noi' will be mapped to 'Ha Noi' as correct lmd zone is 'Ha Noi' */
    const mappingLMDZone = await RateManagementService.mapLMDZone(rateSheet);
    /* eg: mappingLMDZone = {
      'Ha noi': 'Ha Noi',
      '1-yyR': '1-YYR'
    */
    const range = XLSX.utils.decode_range(rateSheet['!ref']);
    const arrData = [];
    const startDataRowIndex = 8;

    for (let i = 8; i <= range.e.r; i++) {
      arrData.push({
        origin: rateSheetArray[i][0],
        destination: rateSheetArray[i][1],
        service_option: rateSheetArray[i][2],
        weight: Number(rateSheetArray[i][3].trim()),
        unit: rateSheetArray[i][4],
        rate_currency: rateSheetArray[i][5],
        rate: Number(rateSheetArray[i][6].trim()),
        rate_zone: mappingLMDZone[rateSheetArray[i][7]],
        rate_table_id: rateId,
        active: true,
        merchant_account_number: rateSheetArray[3][2].trim(),
      });
    }

    // Copy new one to use before "getBatch"
    // Because while getting batch to create merchant rate items, the "arrData" will be spliced and reduced to 0
    const merchantRateItems = [...arrData];

    logger.info({
      message: `Start updating ${fileName} / ${rateId}.xlsx into the database`,
      info: `${arrData.length} merchant rate items will be created`,
    });

    const now = performance.now();

    for (const batch of this.getBatch(arrData, 100)) {
      await Promise.all(batch.map((item: IMerchantRateItem) => this.createMerchantRateItem(item)));

      await CommonUtils.sleep(500);
    }

    logger.info({
      message: `Finish updating ${fileName} / ${rateId}.xlsx into the database`,
      info: `${merchantRateItems.length > 0 || rateSheetArray.length - startDataRowIndex} merchant rate items were created`,
      duration: `${Math.round(performance.now() - now)}ms`,
    });

    return merchantRateItems;
  },

  *getBatch(records, batchSize = 10) {
    while (records.length > 0) {
      yield records.splice(0, batchSize);
    }
  },

  /**
   * Expect data structure for v2:
   *
   * [
   {
   "code": "SG",
   "data": {
   rateZones: [],
   "postCodeLength": [
   4
   ],
   "postCodeChecked": false,
   "cityChecked": false,
   "stateChecked": false,
   "countryCode": "SG",
   "postCodeType": ""
   }
   }
   * ]
   * @returns {Promise<any[]>}
   */
  async listDestinationWithZone() {
    const allRatezoneData = await RateServices.getRatezonesOfCountry('all');

    return allRatezoneData.map((item) => {
      return { code: item.countryCode, data: item };
    });
  },

  /**
   * Validate ratezone vs destination
   * @param {*} workbook
   * @param {*} mappingDestinationWithAirport
   * @param {*} listDestinationWithZone
   * @returns
   */
  async checkRateZone(workbook, mappingDestinationWithAirport, listDestinationWithZone) {
    let result = {
      success: true,
      message: '',
    };
    const first_sheet_name = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[first_sheet_name];
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    const countRows = range.e.r;

    for (let i = 8; i <= countRows; i++) {
      // Get each rate zone
      const desired_cell_check = getCellValue(worksheet, 7, i);
      const eachRateZoneValue = desired_cell_check.toLowerCase();

      // Get each Destination(Called Airport code. Exp: SIN)
      const eachAirportCodeValue = getCellValue(worksheet, 1, i);

      let des = ''; // Exp: singapore

      for (const dest in mappingDestinationWithAirport) {
        if (mappingDestinationWithAirport[dest].includes(eachAirportCodeValue)) {
          des = dest;
          break;
        }
      }

      // create ignore condition here before return
      if (!des) {
        result = {
          success: false,
          message: `could not get destination for airport code: ${eachAirportCodeValue}`,
        };

        return result;
      }

      const countryCode = CountryISOService.getCountryCode2(des);
      const countryRatezoneObj = listDestinationWithZone.find((x) => {
        return x.code === countryCode;
      });

      const zones = [];

      if (!countryRatezoneObj) {
        result = {
          success: false,
          message: `could not get rate zone for destination: ${des}`,
        };

        return result;
      }

      if (countryRatezoneObj.data) {
        for (const airport of countryRatezoneObj.data.airports) {
          for (const rateZone of airport.rateZones) {
            zones.push(rateZone.name.toLowerCase());
          }
        }
      }

      if (zones.length > 0 && !zones.includes(eachRateZoneValue)) {
        result = {
          success: false,
          message: `zone ${eachRateZoneValue} of destination ${des} does not exist`,
        };

        return result;
      }

      if (zones.length === 0 && eachRateZoneValue.toLowerCase() !== des.toLowerCase()) {
        result = {
          success: false,
          message: `zone ${eachRateZoneValue} of destination ${des} does not exist`,
        };

        return result;
      }
    }

    return result;
  },

  async createMerchantRateItem(rateItem) {
    const encryptedMerchantAccountNumber = AesUtils.CrtCounterEncrypt(
      rateItem.merchant_account_number,
    );
    const payload = {
      ...rateItem,
      merchant_account_number: encryptedMerchantAccountNumber,
      _partitionKey: encryptedMerchantAccountNumber,
      type: 'merchant_rate_item',
    };

    try {
      await Daos.rateMerchant.addItem(payload);
    } catch (error) {
      logger.error({ message: 'Failed to create merchant rate item', error });
    }
  },

  /**
   * Validate the header row
   * @param {*} worksheet
   */
  validateHeaders(worksheet) {
    const objcheck = {
      origin: 'Origin',
      destination: 'Destination',
      service_option: 'Service Option',
      weight: 'Weight',
      weight_unit: 'Weight Unit',
      rate_currency: 'Rate Currency',
      rate: 'Rate',
      lmd_zone: 'LMD Zone',
    };

    let count = 0;

    for (const propertyName in objcheck) {
      const desired_cell_check = getCellValue(worksheet, count, 7);

      if (desired_cell_check === undefined) {
        return {
          success: false,
          message: 'Title cannot be empty',
        };
      }

      const test = objcheck[propertyName].toLowerCase() === desired_cell_check.trim().toLowerCase();

      if (test === false) {
        return {
          success: false,
          message: 'Incorrect columns name',
        };
      }

      count++;
    }

    return { success: true };
  },

  async validateLmdZoneCol(worksheet) {
    try {
      const listDestinationWithZone = await this.listDestinationWithZone();
      const range = XLSX.utils.decode_range(worksheet['!ref']);
      const countRows = range.e.r;

      for (let i = 8; i <= countRows; i++) {
        // check if Destination has LMD zone
        const destDesiredCellCheck = getCellValue(worksheet, 1, i);
        const dest = listDestinationWithZone.find((item) => item.code === destDesiredCellCheck);

        if (!dest) {
          return {
            success: false,
            message: `${destDesiredCellCheck} does not have any LMD zone`,
          };
        }

        // check zone's name
        const listZoneName = dest.data.rateZones.map((item) => item.name);
        const listZoneNameUpcase = listZoneName.map((zone) => zone.toUpperCase());
        const zoneDesiredCellCheck = getCellValue(worksheet, 7, i);
        const zoneDesiredCheckUpcase = zoneDesiredCellCheck.toUpperCase();
        const checkZoneName = listZoneNameUpcase.includes(zoneDesiredCheckUpcase);

        if (!checkZoneName) {
          return {
            success: false,
            message: `${zoneDesiredCellCheck} is not a legitimate LMD Zone. The LMD Zones for the country of ${destDesiredCellCheck} is as follows: ${listZoneName.join(', ')}`,
          };
        }
      }

      return { success: true };
    } catch (error) {
      logger.error({ error });

      return {
        success: false,
        message: 'Something wrong',
      };
    }
  },

  async mapLMDZone(worksheet) {
    const mappingLMDZone = {};
    const listDestinationWithZone = await this.listDestinationWithZone();
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    const countRows = range.e.r;

    for (let i = 8; i <= countRows; i++) {
      const destDesiredCellCheck = getCellValue(worksheet, 1, i);
      const dest = listDestinationWithZone.find((item) => item.code === destDesiredCellCheck);
      const listZoneName = dest.data.rateZones.map((item) => item.name);
      // listZoneName = ['Ha Noi', 'North', 'Ho Chi Minh]
      const mapListZoneUpcase = listZoneName.reduce((result, zone) => {
        return { ...result, [zone]: zone.toUpperCase() };
      }, {});
      /* mapListZoneUpcase = {
        'Ha Noi': 'HA NOI',
        'Ho Chi Minh': 'HO CHI MINH',
        'North': 'NORTH'
      } */
      const zoneDesiredCellCheck = getCellValue(worksheet, 7, i);

      // zoneDediredCellCheck = 'ha Noi'
      for (const [key, value] of Object.entries(mapListZoneUpcase)) {
        if (value === zoneDesiredCellCheck.toUpperCase()) {
          mappingLMDZone[zoneDesiredCellCheck] = key;
          break;
        }
      }
    }

    return mappingLMDZone;
  },

  async updateMerchantRateFinishUpload(id: string, encryptedMerchantAccNo: string): Promise<any> {
    return Daos.rateMerchant.patch({
      id,
      partitionKey: encryptedMerchantAccNo,
      is_uploading: false,
    });
  },
};
