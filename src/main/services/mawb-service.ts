import axios from 'axios';
import groupBy from 'lodash/groupBy.js';
import sumBy from 'lodash/sumBy.js';
import uniq from 'lodash/uniq.js';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';

import { AesUtils } from '~/models/aesUtils.js';
import { COUNTRY_CODE_2, CURRENCIES_NAME, ENUM } from '~/models/enum.js';

import {
  ConsoleManifestUtils,
  CustomManifestPDFPayload,
  HongKongCustomManifestPDFPayload,
} from '~/utilities/consoleManifestUtils.js';
import { DateUtils } from '~/utilities/dateUtils.js';
import { ExcelUtils, IField } from '~/utilities/excelUtils.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { UnitConverterUtils } from '~/utilities/unitConverterUtils.js';
import { JSZipUtils } from '~/utilities/zipUtils.js';

import { config } from '~/configs/index.js';
import { Daos } from '~/daos/index.js';
import { mawbHelper } from '~/helpers/mawb_helper.js';
import { EXT } from '~/types/ext.type.js';
import { DefaultOdPermitRow, KROdPermitRow } from '~/types/od-permit.type.js';

import CountryISOService from './countryISO-service.js';
import { CurrencyConversionService } from './currency-conversion-service.js';
import { DestinationService } from './destination-services.js';
import { GaylordServices } from './gaylord.service.js';
import { ManifestItemService } from './manifest-item.service.js';
import { MerchantService } from './merchant-service.js';
import { OperationHubService } from './operation-hub.service.js';

const logger = new AppLogger({ name: 'MawbService' });

export const MawbService = {
  /**
   * get active MAWB tranx by mawb no
   */
  async getMawb(mawbNo: string) {
    const url = `${process.env.FINANCE_URL}/mawb/getMawb/${mawbNo}`;

    try {
      const res = await axios.get(url);

      return await Promise.resolve(res.data);
    } catch (error) {
      logger.error({ mawbNo, error });

      return [];
    }
  },

  /**
   * get MAWB tranx by mawb no
   */
  async getByMawbNo(mawbNo: string) {
    const url = `${process.env.FINANCE_URL}/mawb/getByMawbNo/${mawbNo}`;

    try {
      const res = await axios.get(url);

      return await Promise.resolve(res.data);
    } catch (error) {
      logger.error({ mawbNo, error });

      return [];
    }
  },

  /**
   * get MAWB tranx by multiple mawb nos
   * @param mawbNos MAWB numbers
   * @param fields fields to be selected
   * @returns {Object.<string, MawbItem[]>} - key is mawb no and value is array of MAWB
   * Take a look at Unit test to see examples
   */
  async getMultipleMawbs(mawbNos: string[], fields?: string[]) {
    const date = new Date();
    const ts = mawbHelper.getMawbRecycleTs(date);
    const query = mawbHelper.getMawbQuery(mawbNos, ts, fields);
    const mawbs = await Daos.mawb.find(query);
    const mawbsMap = groupBy(mawbs, '_partitionKey');

    return mawbsMap;
  },

  /**
   * get create tranx of mawb
   */
  getCreatedTx(mawbTranx: any[]) {
    const createdObjs = mawbTranx.filter((tx) => tx.tranx_type === 'created');

    return createdObjs.at(-1);
  },

  /**
   * get gaylord under mawb
   */
  getGaylords(mawbTranx: any) {
    const gaylords = [];
    const gaylordTx = mawbTranx.filter(
      (tx: any) => tx.tranx_type === 'add-gaylord' || tx.tranx_type === 'remove-gaylord',
    );

    for (const tx of gaylordTx) {
      if (tx.tranx_type === 'add-gaylord') {
        gaylords.push(tx);
      }

      if (tx.tranx_type === 'remove-gaylord') {
        const index = gaylords.findIndex((gl) => gl.gaylord_no === tx.gaylord_no);
        gaylords.splice(index, 1);
      }
    }

    return gaylords;
  },

  async patch(item: any) {
    return Daos.mawb.patch(item);
  },

  /**
   * Update BoxC mawb
   * <AUTHOR>
   * @param lmdName
   * @param mawbNo
   * @param mawbId
   * @param gaylordNos
   * @param gaylords
   * @returns {Promise<void>}
   */
  async updateBoxCMawb({
    lmdName,
    mawbNo,
    mawbId,
    gaylords,
  }: {
    lmdName: string;
    mawbNo: string;
    mawbId: string;
    gaylords: any[];
  }) {
    if (!this.isLmdBoxC(lmdName)) {
      return;
    }

    // Classify Gaylords into 2 group: with and without manifest
    const gaylordsWithManifestIds = gaylords.filter((gaylord) => !!gaylord.manifest_data);
    const uniqueExsitingManifestIds = groupBy(
      gaylordsWithManifestIds,
      (gaylord) => gaylord.manifest_data.id,
    );
    const gaylordWithoutManifestIds = gaylords.filter((gaylord) => !gaylord.manifest_data);
    const overpackWithoutManifestIds = gaylordWithoutManifestIds.map(
      (gaylord) => gaylord.overpack_id,
    );
    const mawbUpdatePayload = [];
    let gaylordUpdatePayload;

    // Generate update payload for mawb and gaylords
    for (const [manifestId, GLs] of Object.entries(uniqueExsitingManifestIds)) {
      const mawbPayload = {
        _partitionKey: mawbNo,
        ref_id: mawbId,
        manifest_id: manifestId,
        tranx_type: ENUM.mawbTransaction.boxCTriggered,
        status: ENUM.mawbStatus.boxCTriggered,
        date: GLs[0].manifest_data.date,
        gaylords: GLs.map((gaylord) => gaylord.id),
        overpacks: GLs.map((gaylord) => gaylord.overpack_id),
      };
      mawbUpdatePayload.push(mawbPayload);
    }

    if (overpackWithoutManifestIds.length > 0) {
      const manifestId = await this.createBoxCManifest(overpackWithoutManifestIds);
      const ts = new Date();
      const mawbPayload = {
        _partitionKey: mawbNo,
        ref_id: mawbId,
        manifest_id: manifestId,
        tranx_type: ENUM.mawbTransaction.boxCTriggered,
        status: ENUM.mawbStatus.boxCTriggered,
        date: ts,
        gaylords: gaylordWithoutManifestIds.map((gaylord) => gaylord.id),
        overpacks: overpackWithoutManifestIds,
      };
      mawbUpdatePayload.push(mawbPayload);
      gaylordUpdatePayload = {
        id: manifestId,
        date: ts,
      };
    }

    // Update database
    for (const gaylord of gaylordWithoutManifestIds) {
      await Daos.gaylord.updateItemResolveConflict({
        id: gaylord.id,
        manifest_data: gaylordUpdatePayload,
      });
    }

    for (const item of mawbUpdatePayload) {
      await Daos.mawb.addItem(item);
    }
  },

  /**
   * Check whether lmd name is BoxC or not
   * <AUTHOR>
   * @param lmdName
   * @returns {boolean} true if it is BoxC, false otherwise
   */
  isLmdBoxC(lmdName: string) {
    const givenName = lmdName.toLowerCase();
    const boxC = ENUM.lmdNames.BoxC.toLowerCase();

    return givenName.includes(boxC);
  },

  /**
   * Create boxc manifest
   * @param overpacks
   * @returns {Promise<*>}
   */
  async createBoxCManifest(overpacks: any) {
    let manifestId;

    try {
      const res = await axios.post(`${process.env.FINANCE_URL}/boxc/manifest`, { overpacks });
      manifestId = res.data.success ? res.data.manifestId : res.data.message;
    } catch (error) {
      logger.error({ error });
    }

    if (isNaN(manifestId)) {
      throw new TypeError(`Create boxc manifest failed! ${JSON.stringify(overpacks)}`);
    }

    return manifestId;
  },

  /**
   * Get data from mawbNo
   * @param {string} mawbNo
   * @returns {Promise<any>} return list of mawb tranx and list of gaylord ids
   */
  async getMawbTrxDataByMawbNo(mawbNo: string) {
    try {
      const mawbTx = await this.getMawb(mawbNo);
      const gaylords = this.getGaylords(mawbTx).map((x) => x.gaylord_no);

      return { mawbTx, gaylords };
    } catch (error: any) {
      logger.error({ message: 'can not get Mawb from db', error });

      throw new Error(error);
    }
  },

  async createCustomManifest(mawbTx, flightInfo) {
    try {
      const mawbCreatedTx = this.getCreatedTx(mawbTx);

      const [allOpHubs, allMerchants, gaylords] = await Promise.all([
        OperationHubService.getAll(),
        MerchantService.getAllMerchants(false),
        GaylordServices.getGLsbyMAWB(flightInfo.mawb_no),
      ]);

      const destinations = await DestinationService.getAllDestItems();
      const destinationGroup = gaylords[0].destination_group;
      const parcelClassificationData = await ManifestItemService.parcelClassification(
        gaylords,
        allMerchants,
        destinations,
      );
      const { destination, allConversionRates, allParcels } = parcelClassificationData;
      const countryCode = destination.country;

      const lmdName = gaylords[0].lmd_provider_name;

      if (countryCode === 'US' && lmdName !== ENUM.lmdNames.LMG) {
        return {
          success: true,
          data: [],
        };
      }

      const mawb = {
        tracking_status: mawbTx,
        ...mawbCreatedTx,
      };

      const customBroker = await DestinationService.getCBByDGAndPOD(
        destinationGroup,
        mawbCreatedTx.point_of_discharge,
      );
      const archiveFile = await ManifestItemService.generateCustomManifestOnly(
        mawb,
        gaylords,
        parcelClassificationData,
        allOpHubs,
        allConversionRates,
        customBroker,
        flightInfo,
      );

      if (countryCode === 'MY') {
        const pdfData = await ManifestItemService.generatePdfProformaInvoice(
          allParcels,
          allMerchants,
          allOpHubs,
        );

        if (pdfData.success) {
          archiveFile.push({
            name: 'proforma.pdf',
            buffer: Buffer.from(JSON.parse(pdfData.buffer)),
          });
        }
      } else {
        const aboveItems = (allParcels || []).filter((p) =>
          parcelClassificationData.parcelsAbove.includes(p.id),
        );

        if (aboveItems.length > 0 && lmdName !== ENUM.lmdNames.LMG) {
          const pdfData = await ManifestItemService.generatePdfProformaInvoice(
            aboveItems,
            allMerchants,
            allOpHubs,
          );

          if (pdfData.success) {
            archiveFile.push({
              name: 'proforma.pdf',
              buffer: Buffer.from(JSON.parse(pdfData.buffer)),
            });
          }
        }
      }

      if (archiveFile.length === 0) {
        return {
          success: false,
          message: `Cannot generate custom manifest for mawb ${flightInfo.mawb_no}`,
        };
      }

      for (const item of archiveFile) {
        await AzureBlobStorage.uploadFile(
          item.buffer,
          AzureBlobStorage.generateBlobNameWithTimestamp(item.name),
          config.azureStorageContainer.MAWB,
        );
      }

      return {
        success: true,
        data: archiveFile,
      };
    } catch (error: any) {
      logger.error({ mawbNo: flightInfo.mawb_no, error: error.message });

      throw new Error(error.message);
    }
  },

  /**
   * Generate OD Permit file then upload to Azure Blob Storage
   * @param {string} awbNo
   * @param {*} awbParcels
   * @returns
   */
  async generateOdPermitFile(awbNo: string, awbParcels: any[]) {
    let fileName = `${awbNo}_OD_Permit`;
    let sheetName = fileName;
    let sheetFields: IField[];
    let sheetItems: DefaultOdPermitRow[] | KROdPermitRow[];

    try {
      // check origin country of ophub
      const opHub = await OperationHubService.getOperationHubByName(awbParcels[0].operation_hub);

      if (opHub.country_iso2 === ENUM.COUNTRIES.KR) {
        const { KRTableFields, KRTableRow } = await this.ODPermitKoreaTemplateContent({
          awbNo,
          awbParcels,
        });
        sheetFields = KRTableFields;
        sheetItems = KRTableRow;
        fileName = `KR Simplified Manifest ${awbNo}`;
        sheetName = 'KR manifest';
      } else {
        const { defaultTableFields, defaultTableRow } = await this.ODPermitDefaultTemplateContent({
          awbNo,
          awbParcels,
        });
        sheetFields = defaultTableFields;
        sheetItems = defaultTableRow;
      }

      logger.info({
        message: 'Start create OD permit',
        awbNo,
        country: opHub.country_iso2,
        business: ENUM.FunctionName.AZURE_BLOB_STORAGE,
      });

      const odPermitBuffer = ExcelUtils.createBuffer(sheetName, sheetItems, sheetFields);
      await AzureBlobStorage.uploadFile(
        odPermitBuffer,
        AzureBlobStorage.generateBlobNameWithTimestamp(`${awbNo}/${fileName}.xlsx`),
        config.azureStorageContainer.MAWB,
      );

      const message = `OD Permit for AWB ${awbNo} has been generated and uploaded to blob successfully`;
      logger.info({ message, business: ENUM.FunctionName.AZURE_BLOB_STORAGE });

      return {
        success: true,
        message,
      };
    } catch (error) {
      const failedMessage = `OD Permit generation for AWB ${awbNo} has been failed`;
      logger.error({
        message: failedMessage,
        business: ENUM.FunctionName.AZURE_BLOB_STORAGE,
        error,
      });

      return {
        success: false,
        message: failedMessage,
      };
    }
  },

  async ODPermitDefaultTemplateContent({ awbNo, awbParcels }) {
    try {
      let skuHsCodeMapping: any = null;
      const sheetFields = [
        { label: 'Tracking ID', value: (row: any) => row.tracking_id },
        { label: 'Item Description', value: (row: any) => row.description },
        { label: 'Item Quantity', value: (row: any) => row.quantity },
        { label: 'SKU', value: (row: any) => row.sku },
        { label: 'Origin Country HS Code', value: (row: any) => row.hs_code },
        { label: 'Item Country of Origin', value: (row: any) => row.origin_country },
        { label: 'Item Value', value: (row: any) => row.value },
        { label: 'Currency', value: (row: any) => row.currency },
        { label: 'Merchant Name', value: (row: any) => row.merchant_name },
      ];
      const sheetItems = [];
      // Group parcels by merchant account numbers to reduce number of calls to get mapping files from blob
      const parcelsByMerchantAccNumber = groupBy(
        awbParcels,
        (parcel) => parcel.merchant_account_number,
      );

      for (const accNumber in parcelsByMerchantAccNumber) {
        const decrypted = AesUtils.CrtCounterDecrypt(accNumber);
        const latestSkuBlob = await AzureBlobStorage.getLatestFileByTimestamp(
          `${decrypted.toUpperCase()}_SKU`,
          EXT.JSON,
          config.azureStorageContainer.SKU,
        );

        if (latestSkuBlob) {
          const skuHsCodeBuffer = await AzureBlobStorage.downloadBlobBuffer(
            config.azureStorageContainer.SKU,
            latestSkuBlob.name,
          );

          const skuHsCodeArray = JSON.parse(skuHsCodeBuffer.toString()).map((item: any) => [
            item['Product SKU'],
            item['HS Code'],
          ]);

          skuHsCodeMapping = new Map(skuHsCodeArray);
        }

        for (const parcel of parcelsByMerchantAccNumber[accNumber]) {
          if (!parcel.item?.length) continue;

          const lineItems = parcel.item.map((lineItem: any) => ({
            tracking_id: parcel.tracking_id || '',
            description: lineItem.description || '',
            quantity: lineItem.quantity || '',
            sku: lineItem.SKU || '',
            hs_code: skuHsCodeMapping?.get(lineItem.SKU) || '',
            origin_country: lineItem.origin_country || '',
            value: lineItem.total_declared_value || '',
            currency: parcel.merchant_declared_currency || '',
            merchant_name: AesUtils.CrtCounterDecrypt(parcel.merchant_name),
          }));
          sheetItems.push(...lineItems);
        }
      }

      return {
        defaultTableFields: sheetFields,
        defaultTableRow: sheetItems,
      };
    } catch (error) {
      logger.error({
        message: 'Failed to generate OD Permit default template content',
        awbNo,
        error,
      });

      throw new Error(error.message || 'Failed to generate OD Permit default template content');
    }
  },

  async ODPermitKoreaTemplateContent({ awbNo, awbParcels }) {
    try {
      const sheetFields = [
        { label: 'Invoice번호', value: (row: KROdPermitRow) => row.id },
        { label: '신고자상호', default: '' },
        { label: '수출대행자상호', default: '주식회사 뉴웨이브커머스' },
        { label: '수출화주상호', default: '주식회사 뉴웨이브커머스' },
        { label: '대표자', default: '이보연, 변광윤' },
        { label: '사업자등록번호', default: '8268801938' },
        { label: '통관고유부호', default: '뉴웨이브1211013' },
        { label: '수출자우편번호', default: '6577' },
        { label: '수출자주소', default: '서울특별시 서초구 서래로 24, 4층 2호(반포동, 다솜빌딩)' },
        { label: '제조자 상호', default: '미상' },
        {
          label: '해외거래처상호(Recipient)',
          value: (row: KROdPermitRow) => row.recipient_fullname,
        },
        { label: '신고구분', default: '11' },
        { label: '거래구분', default: '21' },
        { label: '결제종류', default: 'TT' },
        {
          label: '목적국',
          value: (row: KROdPermitRow) => row.iso2_country,
        },
        { label: '물품소재지 우편번호', default: '22379' },
        { label: '물품소재지주소', default: '인천 중구 공항동로 296번길 98-114' },
        { label: '창고코드', default: '04077126' },
        { label: '환급신청인', default: '01' },
        { label: '간이환급구분', default: '02' },
        {
          label: '총중량(Gross weight)',
          value: (row: KROdPermitRow) => row.actual_weight,
        },
        { label: '인도조건', default: 'FOB' },
        { label: '결제통화단위(Payment currency unit)', default: 'USD' },
        {
          label: '입력 결제금액(Payment amount)',
          value: (row: KROdPermitRow) => row.payment_amount,
        },
        { label: '세번부호(HS CODE)', value: (row: KROdPermitRow) => row.hs_code },
        { label: '순중량(Gross weight)', value: (row: KROdPermitRow) => row.gross_weight },
        { label: '원산지', default: 'KR' },
        {
          label: '품명 및 규격(Item name and specification)',
          value: (row: KROdPermitRow) => row.description,
        },
        { label: '수량', default: '1' },
        { label: '단위', default: 'BAG' },
        { label: '단가(Payment amount)(USD)', value: (row: KROdPermitRow) => row.payment_amount },
        {
          label: '금액(Payment amount)',
          value: (row: KROdPermitRow) => row.total_merchant_declared_value,
        },
        { label: '포장갯수', default: '1' },
        { label: '포장단위', default: 'GT' },
        { label: '운송용기', default: 'ETC' },
        { label: '적재항', default: 'ICN' },
        { label: '운송수단', default: '40' },
        { label: 'ORDER NO', value: (row: KROdPermitRow) => row.merchant_order_no },
        { label: 'MASTER BL번호', default: awbNo },
        { label: '출항일시', default: '' },
        { label: '선편/항공편명', default: '' },
        { label: 'DELIVERY ROUTE', default: '엑스포라인' },
      ];

      const sheetItems: KROdPermitRow[] = [];

      for (const parcel of awbParcels) {
        if (!parcel.item?.length) continue;
        AesUtils.decryptParcel(parcel);
        const {
          id,
          recipient_first_name,
          recipient_last_name,
          item,
          country,
          weight,
          weight_unit,
          merchant_order_no,
          merchant_declared_currency,
        } = parcel;

        const recipientFullname = [recipient_first_name, recipient_last_name]
          .filter(Boolean)
          .join(' ');

        const actualWeight =
          weight_unit === ENUM.measurements.unit.kg
            ? weight
            : UnitConverterUtils.lbtokg(Number(weight));

        // get exchange rate for USD from merchant_declared_currency
        // eslint-disable-next-line no-await-in-loop
        const exchangeRateObj = await CurrencyConversionService.getExchangeRate(
          merchant_declared_currency,
          CURRENCIES_NAME.USD,
        );

        // sum total_declared_value of all items in parcel
        const sumMerchantDeclaredValue = item.reduce((acc, i) => {
          return acc + Number(i.total_declared_value);
        }, 0);

        const totalMerchantDeclaredValue = (
          Number(sumMerchantDeclaredValue) * Number(exchangeRateObj.exchangeRate)
        ).toFixed(2);

        item.map((lineItem) => {
          const quantity = Number(lineItem.quantity);
          const grossWeight =
            weight_unit === ENUM.measurements.unit.kg
              ? Number(lineItem.subtotal_weight) / quantity
              : UnitConverterUtils.lbtokg(Number(lineItem.subtotal_weight)) / quantity;
          const paymentAmount = (
            (Number(lineItem.total_declared_value) * Number(exchangeRateObj.exchangeRate)) /
            quantity
          ).toFixed(2);

          for (let i = 0; i < quantity; i++) {
            sheetItems.push({
              id: id || '',
              recipient_fullname: recipientFullname || '',
              iso2_country: CountryISOService.getCountryCode2(country) || '',
              actual_weight: actualWeight || '',
              payment_amount: paymentAmount || '',
              hs_code: lineItem.hs_code || '',
              gross_weight: grossWeight || '',
              description: lineItem.description || '',
              total_merchant_declared_value: totalMerchantDeclaredValue || '',
              merchant_order_no: merchant_order_no || '',
            });
          }
        });
      }

      return {
        KRTableFields: sheetFields,
        KRTableRow: sheetItems,
      };
    } catch (error) {
      logger.error({
        message: 'Failed to generate OD Permit Korea template content',
        awbNo,
        error,
      });

      throw new Error(error.message || 'Failed to generate OD Permit Korea template content');
    }
  },

  async createConsoleManifestAndUpload(mawbNo: string, filePath: string) {
    // TODO: optimize this function?
    const { mawbTx, gaylords } = await MawbService.getMawbTrxDataByMawbNo(mawbNo);

    const fields = [
      'id',
      'tracking_id',
      'tracking_no',
      'merchant_account_number',
      'merchant_name',
      'merchant_order_no',
      'merchant_declared_currency',
      'item',
      'length',
      'width',
      'height',
      'weight_unit',
      'weight',
      'operation_hub',
      'recipient_first_name',
      'recipient_last_name',
      'recipient_addressline1',
      'recipient_addressline2',
      'recipient_addressline3',
      'city_suburb',
      'state',
      'postcode',
      'country',
      'origin_country',
      'phone_country_code',
      'phone',
      'taiwan_import_number',
      'gaylord_no',
      'declaration',
      'shipper_name',
      'shipper_tax_id',
      'shipper_address',
      'shipper_country',
      'shipper_contact',
      'shipper_trademark',
    ];

    const allParcels = await ManifestItemService.getManifestItemsFromMultipleGaylords(
      gaylords,
      fields,
    );

    if (!allParcels?.length) {
      throw new Error('No parcel found in MAWB');
    }

    const opHubName = allParcels.find((x) => !!x.operation_hub).operation_hub;
    const opHub = await OperationHubService.getOperationHubByName(opHubName);

    const createdTx = MawbService.getCreatedTx(mawbTx);
    const pointOfDischarge = createdTx.point_of_discharge;
    const destinationInfo = pointOfDischarge || createdTx.destination;
    const finishTx = mawbTx.find(
      (x: any) => x.status?.toUpperCase() === ENUM.mawbStatus.fin.toUpperCase(),
    );

    if (!opHub) {
      throw new Error('No opHub found');
    }

    const originAirport = opHub.airport_code;
    const originCurrency = CountryISOService.getCurrencyByCountry(opHub.country);

    if (!originCurrency) {
      throw new Error('Cannot get origin currency');
    }

    const listMerchantDeclareCurrencies = uniq(allParcels.map((x) => x.merchant_declared_currency));
    const merchantNames = uniq(allParcels.map((x) => x.merchant_name));
    const merchants = await MerchantService.getMerchantsByNames(merchantNames, false);

    const isKorea = opHub.country_iso2 === ENUM.COUNTRIES.KR;

    let listConversions = [];

    if (isKorea) {
      const originCurrencyConversions = await Promise.all(
        listMerchantDeclareCurrencies.map((cur) =>
          CurrencyConversionService.getExchangeRateOldFormat(cur, originCurrency, new Date()),
        ),
      );

      const usdConversions = await Promise.all(
        listMerchantDeclareCurrencies.map((cur) =>
          CurrencyConversionService.getExchangeRateOldFormat(cur, 'USD', new Date()),
        ),
      );

      listConversions = [...originCurrencyConversions, ...usdConversions];
    } else {
      listConversions = await Promise.all(
        listMerchantDeclareCurrencies.map((cur) =>
          CurrencyConversionService.getExchangeRateOldFormat(cur, originCurrency, new Date()),
        ),
      );
    }

    let details = [];
    let totalDetail = {};
    const isHongKong = opHub.country_iso2 === ENUM.COUNTRIES.HK;
    const isFromTaiwan = opHub.country_iso2 === ENUM.COUNTRIES.TW;
    const isMalaysia = opHub.country_iso2 === ENUM.COUNTRIES.MY;
    const isUK = opHub.country_iso2 === ENUM.COUNTRIES.GB;
    const isSin = opHub.country_iso2 === ENUM.COUNTRIES.SG;
    const isAU = opHub.country_iso2 === ENUM.COUNTRIES.AU;
    const isJapan = opHub.country_iso2 === ENUM.COUNTRIES.JP;

    if (isHongKong) {
      details = ConsoleManifestUtils.getDetailsHongKong(
        allParcels,
        merchants,
        originCurrency,
        listConversions,
      );
      const totalWeight = sumBy(details, 'totalWeight').toFixed(2);
      const totalPieces = sumBy(details, 'pieces');
      const totalValue = sumBy(details, 'value').toFixed(2);
      totalDetail = { totalPieces, totalWeight, totalValue };
    } else {
      const parcelsByMerchantAccNo = groupBy(allParcels, (parcel) => {
        return parcel.merchant_account_number;
      });
      details = ConsoleManifestUtils.getDetails(
        parcelsByMerchantAccNo,
        opHub,
        merchants,
        originCurrency,
        listConversions,
        pointOfDischarge,
      );
    }

    const dateOfCreation = DateUtils.utcToStr(
      ENUM.dateTimeFormat.DDMMMYYYY,
      new Date(finishTx.console_manifest_creation_date),
    );
    const customBroker = await DestinationService.getCBByPOD(pointOfDischarge);

    let zipBuffer;

    if (isFromTaiwan) {
      const TWConsoleXLSX = new JSZipUtils();
      const declarationGroup = groupBy(allParcels, 'declaration');

      if (declarationGroup['Y']) {
        const shipperGroup = groupBy(declarationGroup['Y'], 'shipper_name');

        for (const [shipperName, parcelsByShipper] of Object.entries(shipperGroup)) {
          const consoleBuffer = ConsoleManifestUtils.exportConsoleManifestForTaiwanExport(
            parcelsByShipper,
            finishTx,
            createdTx,
            opHub.airport_code,
            originCurrency,
            listConversions,
          );
          TWConsoleXLSX.add(
            [finishTx._partitionKey, 'Declaration required', shipperName]
              .join(' - ')
              .concat('.xlsx'),
            consoleBuffer,
          );
        }
      }

      if (declarationGroup['N'] || declarationGroup['undefined'] || declarationGroup['']) {
        const nonDeclarationParcels = [declarationGroup['N']]
          .flat()
          .concat(declarationGroup['undefined'])
          .concat(declarationGroup[''])
          .filter(Boolean);
        const consoleBuffer =
          ConsoleManifestUtils.exportConsoleManifestForTaiwanExportNoDeclaration(
            merchants,
            nonDeclarationParcels,
            finishTx,
            createdTx,
            opHub.airport_code,
          );
        TWConsoleXLSX.add(
          [finishTx._partitionKey, 'Without declaration'].join(' - ').concat('.xlsx'),
          consoleBuffer,
        );
      }

      zipBuffer = await TWConsoleXLSX.toBuffer();
    } else if (isMalaysia) {
      const archive = new JSZipUtils();
      const malaysiaDetails = ConsoleManifestUtils.generateMalaysiaDetails(
        allParcels,
        opHub,
        merchants,
        originCurrency,
        listConversions,
        destinationInfo,
        customBroker,
      );

      const data = {
        mawbNo,
        totalHAWB: allParcels.length,
        origin: originAirport,
        destination: destinationInfo,
        details: malaysiaDetails,
      };

      // Call to Monorepo pdf func to generate pdf using Pdf kit
      const payload = {
        country: COUNTRY_CODE_2.MY,
        originCurrency,
        ...data,
      } as CustomManifestPDFPayload;
      const fileName = `${mawbNo}_console_manifest.pdf`;
      const pdfBuffer = await ConsoleManifestUtils.generateCustomManifestPDF(payload);
      archive.add(fileName, pdfBuffer);
      zipBuffer = await archive.toBuffer();

      // upload to blob pdf version for commercial invoice email
      await AzureBlobStorage.uploadFile(
        pdfBuffer,
        AzureBlobStorage.generateBlobNameWithTimestamp(`${mawbNo}/${fileName}`),
        config.azureStorageContainer.MAWB,
      );
    } else if (isUK) {
      zipBuffer = await ConsoleManifestUtils.generateUKExcel(
        mawbNo,
        allParcels,
        merchants,
        originCurrency,
        listConversions,
      );
    } else if (isSin) {
      const archive = new JSZipUtils();
      const finishMawbDate = DateUtils.localToStr('DD/MO/YYYY', new Date(finishTx.finish_date));
      const parcelsInMawb = allParcels.map((parcel) => AesUtils.decryptParcel(parcel));
      const data = {
        finishMawbDate,
        mawbNo: createdTx._partitionKey,
        POD: createdTx.point_of_discharge,
        origin: opHub.airport_code,
        originCurrency,
      };

      const excelBuffer = ConsoleManifestUtils.exportConsoleManifestForSingaporeExport(
        data,
        parcelsInMawb,
        merchants,
        listConversions,
      );

      archive.add(`${mawbNo}_console_manifest.xlsx`, excelBuffer);

      zipBuffer = await archive.toBuffer();
    } else if (isAU) {
      const archive = new JSZipUtils();
      const auConsoleManifestData = {
        merchants,
        parcels: allParcels.map((parcel) => AesUtils.decryptParcel(parcel)),
        customBroker,
        mawbNo,
        finishMawbDate: dateOfCreation,
        origin: opHub.airport_code,
        destination: destinationInfo,
        originCurrency,
        listConversions,
      };

      const excelBuffer = await ConsoleManifestUtils.generateAUExcel(auConsoleManifestData);

      archive.add(`${mawbNo}_console_manifest.xlsx`, excelBuffer);
      zipBuffer = await archive.toBuffer();
    } else if (isKorea) {
      const archive = new JSZipUtils();
      const destinationCurrency = CountryISOService.getCurrencyByCountry(destinationInfo);

      const convertedParcels = ConsoleManifestUtils.generateKoreaDetailsWithUSDConversion(
        allParcels,
        listConversions,
        destinationCurrency,
      );

      const excelBuffer = ConsoleManifestUtils.generateKoreaExcel(convertedParcels);

      archive.add(`${mawbNo}_console_manifest.xlsx`, excelBuffer);
      zipBuffer = await archive.toBuffer();
    } else if (isJapan) {
      const archive = new JSZipUtils();
      const excelBuffer = await ConsoleManifestUtils.generateJapanConsoleManifestTemplateJP02({
        merchants,
        parcels: allParcels.map((parcel) => AesUtils.decryptParcel(parcel)),
      });
      archive.add(`${mawbNo}_console_manifest.xlsx`, excelBuffer);
      zipBuffer = await archive.toBuffer();
    } else {
      // Call pdf-func to gen pdf using pdf kit
      const archive = new JSZipUtils();
      let payload: CustomManifestPDFPayload;

      if (isHongKong) {
        payload = {
          country: COUNTRY_CODE_2.HK,
          creationDate: dateOfCreation,
          mawb: {
            mawbNo,
            originAirport,
            pointOfDischarge,
          },
          details,
          totalDetail,
          customBroker,
          opHub,
        } as HongKongCustomManifestPDFPayload;
      } else {
        // default template uses the same template as Malaysia
        const defaultDetails = ConsoleManifestUtils.generateMalaysiaDetails(
          allParcels,
          opHub,
          merchants,
          originCurrency,
          listConversions,
          destinationInfo,
          customBroker,
        );

        const payloadData = {
          mawbNo,
          totalHAWB: allParcels.length,
          origin: originAirport,
          destination: destinationInfo,
          originCurrency,
          details: defaultDetails,
        };
        payload = { country: COUNTRY_CODE_2.MY, ...payloadData } as CustomManifestPDFPayload;
      }

      const pdfBuffer = await ConsoleManifestUtils.generateCustomManifestPDF(payload);
      archive.add(`${mawbNo}_console_manifest.pdf`, pdfBuffer);
      zipBuffer = await archive.toBuffer();
    }

    await AzureBlobStorage.uploadFile(
      zipBuffer,
      AzureBlobStorage.generateBlobNameWithTimestamp(filePath),
      config.azureStorageContainer.MAWB,
    );

    return zipBuffer;
  },
};
