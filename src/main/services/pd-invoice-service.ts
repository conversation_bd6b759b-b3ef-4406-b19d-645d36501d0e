import json2csv from 'json2csv';
import groupBy from 'lodash/groupBy.js';
import sumBy from 'lodash/sumBy.js';

import { AzureStorageQueue } from '~/common/azure/azure-storage-queue.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';

import CountryISOService from '~/services/countryISO-service.js';

import { CommonUtils } from '~/utilities/commonUtils.js';
import { ExcelUtils } from '~/utilities/excelUtils.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { separateThousands, roundNumberBaseOnCurrency } from '~/utilities/numberUtils.js';
import { UnitConverterUtils } from '~/utilities/unitConverterUtils.js';

import { CONSTANTS } from '~/constants/index.js';
import { ManifestItem } from '~/types/manifest-item.type.js';
import { IMerchant } from '~/types/merchant.type.js';

import { CurrencyConversionService } from './currency-conversion-service.js';
import { HawbService } from './hawb.service.js';
import { ManifestItemService } from './manifest-item.service.js';
import { OperationHubService } from './operation-hub.service.js';
import { RateServices } from './rate-services.js';
import StatusMappingService from './statusMappingService.js';
import { TaxService } from './tax-service.js';

function formatCurrency(val: any, currency: any) {
  return `${currency}${separateThousands(val)}`;
}

const logger = new AppLogger({ name: 'PdInvoiceService' });
const invoiceLogger = logger.extends({ business: ENUM.FunctionName.INVOICE });

export default new (class PdInvoiceService {
  exchangeRates: any[] = [];
  operationHubs: any[] = [];

  async prepareInvoiceMetadata() {
    [this.exchangeRates, this.operationHubs] = await Promise.all([
      CurrencyConversionService.getAllExchRates(),
      OperationHubService.getAll(),
    ]);
  }

  async getReadyToInvoiceParcels(merchant, year, month, date, startDayCountInvoice) {
    const currentDayCountInvoice = new Date(Date.UTC(year, month - 1, date - 1, 23, 59, 59, 999));

    return ManifestItemService.getPDReadyToInvoiceParcels(
      merchant.merchant_name,
      startDayCountInvoice,
      currentDayCountInvoice,
    );
  }

  calculateParcelWeights(parcel: any, chargeType: any, chargeWeightUnit: any) {
    parcel.chargeWeightUnit = chargeWeightUnit;
    parcel.chargeableWeight = UnitConverterUtils.getChargeableWeight(
      parcel,
      chargeType,
      chargeWeightUnit,
    );

    if (chargeWeightUnit === 'kg') {
      parcel.grossWeight = UnitConverterUtils.getGrossWeightKg(parcel);
      parcel.VolumetricWeigh = UnitConverterUtils.getVolumetricWeightKg(parcel);
    } else {
      parcel.grossWeight = UnitConverterUtils.getGrossWeightLb(parcel);
      parcel.VolumetricWeigh = UnitConverterUtils.getVolumetricWeightLb(parcel);
    }
  }

  getRateV1(rateSheet: any, parcel: any) {
    const ratesByRoute = rateSheet.rates.filter((rate: any) => {
      if (
        rate.origin.toUpperCase() !== parcel.origin_country ||
        rate.destination.toUpperCase() !== parcel.country_ISO2 ||
        rate.service_option.toLowerCase() !== parcel.service_option.toLowerCase()
      ) {
        return false;
      }

      return (
        parcel.lmd_zone === 'NO_RATEZONE_REQUIRED' ||
        rate.rate_zone.toUpperCase() === parcel.lmd_zone.toUpperCase()
      );
    });

    if (ratesByRoute.length === 0) {
      return;
    }

    this.calculateParcelWeights(parcel, rateSheet.chargeable_weight, ratesByRoute[0].unit);

    // Default to use rate item with highest chargeable weight
    let selectedRate = ratesByRoute.at(-1);

    for (const rate of ratesByRoute) {
      // Find rate item with nearest higher weight
      if (parcel.chargeableWeight <= rate.weight) {
        selectedRate = rate;
        break;
      }
    }

    return selectedRate;
  }

  getRateV2(rateSheet: any, parcel: any) {
    const ratesByRoute = rateSheet.rates.filter((rate: any) => {
      return (
        rate.country_code.toUpperCase() === parcel.country_ISO2?.toUpperCase() &&
        rate.service_option.toLowerCase() === parcel.service_option?.toLowerCase()
      );
    });

    if (ratesByRoute.length === 0) {
      return;
    }

    this.calculateParcelWeights(parcel, rateSheet.chargeable_weight, rateSheet.weight_unit);

    // parcel weight > weight of highest rate item => rate = highest rate + increment rate
    if (parcel.chargeableWeight > ratesByRoute.at(-1).weight) {
      const {
        country_code: countryCode,
        service_option: serviceOption,
        rate,
        weight,
      } = ratesByRoute.at(-1);
      const { incremental_weight_break: incrementalWeightBreak, incremental } = rateSheet;
      const incrementalRate = incremental[countryCode][serviceOption];

      return {
        country_code: countryCode,
        weight:
          weight +
          Math.ceil((parcel.chargeableWeight - weight) / incrementalWeightBreak) *
            incrementalWeightBreak,
        rate:
          rate +
          Math.ceil((parcel.chargeableWeight - weight) / incrementalWeightBreak) * incrementalRate,
      };
    }

    // Find rate item with nearest higher weight
    for (const rate of ratesByRoute) {
      if (parcel.chargeableWeight <= rate.weight) {
        return rate;
      }
    }
  }

  calculateParcelDeliveryRate(merchant: any, parcel: any, rateSheets: any, lmdZone: any) {
    // Get lmd_zone for parcel
    parcel.lmd_zone = parcel.lmd_zone || lmdZone.name;

    for (const rateSheet of rateSheets) {
      if (rateSheet.version === 2) {
        const parcelRate = this.getRateV2(rateSheet, parcel);

        if (parcelRate) {
          parcel.invoiceRateCurrency = rateSheet.currency;
          parcel.rate_zone = parcelRate.country_code;
          parcel.rate_sheet_chg_wt = parcelRate.weight;
          parcel.rate = parcelRate.rate;
          break;
        }
      } else if (parcel.lmd_zone) {
        const parcelRate = this.getRateV1(rateSheet, parcel);

        if (parcelRate) {
          parcel.invoiceRateCurrency = parcelRate.rate_currency;
          parcel.rate_zone = parcelRate.rate_zone;
          parcel.rate_sheet_chg_wt = parcelRate.weight;
          parcel.rate = parcelRate.rate;
          break;
        }
      }
    }

    if (!parcel.rate) {
      parcel.invoiceError =
        `Rate not found for route ${parcel.origin_country}-${parcel.country_ISO2}` +
        ` (lmd_zone: ${parcel.lmd_zone}) in rate sheets ${rateSheets.map((rate: any) => rate.id)}`;

      return;
    }

    // Calculate tax information for delivery rate
    const taxInfo = TaxService.calculateTax(
      merchant,
      parcel.shipment_type.toLowerCase() === ENUM.shipmentType.domestic,
      ENUM.invoiceChargeType.delivery,
      parcel.rate,
      parcel.invoiceRateCurrency,
      'N/A',
    );
    Object.assign(parcel, taxInfo);
  }

  calculateParcelFuelSurcharge(parcel: any) {
    const { chargeWeightUnit, chargeableWeight, fuel_surcharge, invoiceRateCurrency }: any = parcel;

    if (fuel_surcharge) {
      const chargeableWeightKg: any =
        chargeWeightUnit === 'kg' ? chargeableWeight : UnitConverterUtils.lbtokg(chargeableWeight);
      let fuelSurcharge: any = Number(fuel_surcharge.rates_per_kg * chargeableWeightKg);

      if (fuelSurcharge && invoiceRateCurrency !== fuel_surcharge.currency) {
        const convertedCurrency = CurrencyConversionService.convertCurrency(
          this.exchangeRates,
          fuel_surcharge.currency,
          invoiceRateCurrency,
        );

        if (convertedCurrency) {
          fuelSurcharge *= convertedCurrency.destination_currency;
        }
      }

      return roundNumberBaseOnCurrency(fuelSurcharge, invoiceRateCurrency);
    }

    return 0;
  }

  async calculateChargesByParcels(merchant: IMerchant, parcels: any) {
    const successParcels = <any>[];
    let errorParcels = 0;

    if (parcels.length === 0) {
      return [successParcels, errorParcels];
    }

    const bookingDates = new Set(parcels.map((parcel: ManifestItem) => parcel.order_date));
    const [rates, lmdZones] = await Promise.all([
      RateServices.getActiveRates(
        AesUtils.CrtCounterEncrypt(merchant.merchant_account_number),
        Array.from(bookingDates),
      ),
      RateServices.getRateZone(parcels.filter((parcel: ManifestItem) => !parcel.lmd_zone)),
    ]);

    if (rates.length === 0) {
      throw new Error('No active rate');
    }

    for (const parcel of parcels) {
      try {
        // get all approved and effected rate
        const orderDate = new Date(parcel.order_date);
        const rateSheets = rates.filter(
          (rateTable: { validity_from: Date; validity_to: Date }) =>
            rateTable.validity_from <= orderDate && orderDate <= rateTable.validity_to,
        );
        const lmdZone = lmdZones.find((item: any) => item.id === parcel.id);

        if (rateSheets) {
          this.calculateParcelDeliveryRate(merchant, parcel, rates, lmdZone);
          parcel.fuelSurcharge = this.calculateParcelFuelSurcharge(parcel);
        } else {
          parcel.invoiceError = `Rate not found for booking date ${parcel.order_date}`;
        }
      } catch (error) {
        parcel.invoiceError = error.message || error.stack;
      } finally {
        if (parcel.invoiceError) {
          errorParcels++;
          invoiceLogger.error({
            message: parcel.invoiceError,
            merchantName: merchant.merchant_name,
            parcelId: parcel.id,
          });
        } else {
          successParcels.push(parcel);
        }
      }
    }

    return [successParcels, errorParcels];
  }

  async calculateChargesByHawbs(merchant: IMerchant, hawbs: any) {
    const successHawbs = <any>[];

    if (hawbs.length === 0) {
      return [successHawbs, 0];
    }

    // Get active rates
    const rates = await RateServices.getActiveRates(
      AesUtils.CrtCounterEncrypt(merchant.merchant_account_number),
      [new Date()],
      true,
    );

    const ratesheet = rates.find(
      (rate: any) => rate.service_option.toUpperCase() === ENUM.RATE_SHEET_SERVICE_OPTION.B2B,
    );

    if (!ratesheet) {
      throw new Error('No active rate for hawb');
    }

    ratesheet.rateItems = [...ratesheet.rates];

    const { successItems, errorItems } = await HawbService.updateHawbChargeableWeightAndPdAmount(
      AesUtils.CrtCounterEncrypt(merchant.merchant_account_number),
      ratesheet,
    );

    // Calculate Tax information for delivery rate
    for (const hawb of successItems) {
      const destinationCountry = CountryISOService.getCountryCode2(
        AesUtils.CrtCounterDecrypt(hawb.item.destination_country),
      );
      // Calculate tax information
      const taxInfo = TaxService.calculateTax(
        merchant,
        merchant.country === destinationCountry,
        ENUM.invoiceChargeType.delivery,
        hawb.pd_amount,
        hawb.pd_currency,
        'N/A',
      );
      Object.assign(hawb, taxInfo);
      successHawbs.push({
        ...hawb.item,
        chargeType: hawb.chargeType,
        rate: hawb.rate,
        tax: hawb.tax,
        taxCode: hawb.taxCode,
        taxRate: hawb.taxRate,
        origin_country: merchant.country,
        destination_country: destinationCountry,
      });
    }

    return [successHawbs, errorItems.length];
  }

  async calculateChargesByRoutes(parcels: any, invoiceCurrency: string, invoiceMonth: Date) {
    const parcelsByRoute = groupBy(
      parcels,
      (parcel) => `${parcel.origin_country}-${parcel.country_ISO2}`,
    );

    const result = [];

    for (const route in parcelsByRoute) {
      if (Object.hasOwn(parcelsByRoute, route)) {
        const { taxCode, taxRate, origin_country, country_ISO2 } = parcelsByRoute[route][0];
        const routeData: any = {
          route,
          service_options: [],
          rate: 0,
          tax: 0,
          total_insurance_charge: 0,
          total_fuel_surcharge: 0,
          taxCode,
          taxRate,
          origin_country,
          destination: country_ISO2,
          numberOfParcel: parcelsByRoute[route].length,
        };

        const parcelsByService = groupBy(parcelsByRoute[route], 'service_option');

        for (const serviceOption in parcelsByService) {
          if (Object.hasOwn(parcelsByService, serviceOption)) {
            const serviceOptionData = {
              service_option: serviceOption,
              total: 0,
              tax: 0,
              total_insurance_charge: 0,
              total_fuel_surcharge: 0,
            };

            for (const parcel of parcelsByService[serviceOption]) {
              parcel.insurance_amount = roundNumberBaseOnCurrency(
                parcel.insurance_amount,
                parcel.invoiceRateCurrency,
              );
              serviceOptionData.total += parcel.rate;
              serviceOptionData.tax += parcel.tax;
              serviceOptionData.total_insurance_charge += Number(parcel.insurance_amount || 0);
              serviceOptionData.total_fuel_surcharge += parcel.fuelSurcharge;
            }

            routeData.service_options.push(serviceOptionData);
            routeData.rate += serviceOptionData.total;
            routeData.tax += serviceOptionData.tax;
            routeData.total_insurance_charge += serviceOptionData.total_insurance_charge;
            routeData.total_fuel_surcharge += serviceOptionData.total_fuel_surcharge;
          }
        }

        // eslint-disable-next-line no-await-in-loop
        const totalRateInSGD = await CurrencyConversionService.exchangeMoney(
          routeData.rate,
          invoiceCurrency,
          'SGD',
          invoiceMonth,
        );

        // eslint-disable-next-line no-await-in-loop
        const totalTaxInSGD = await CurrencyConversionService.exchangeMoney(
          routeData.tax,
          invoiceCurrency,
          'SGD',
          invoiceMonth,
        );

        result.push({
          ...routeData,
          totalRateInSGD,
          totalTaxInSGD,
        });
      }
    }

    return result;
  }

  async calculateHawbChargesByRoutes(
    hawbs: any,
    merchant: any,
    invoiceCurrency: string,
    invoiceMonth: Date,
  ) {
    const hawbsByRoute = groupBy(
      hawbs,
      (hawb) => `${merchant.country}-${hawb.destination_country}`,
    );

    const result = [];

    for (const route in hawbsByRoute) {
      if (Object.hasOwn(hawbsByRoute, route)) {
        const { taxCode, taxRate, origin_country, destination_country } = hawbsByRoute[route][0];
        const routeData: any = {
          route,
          service_options: [],
          rate: 0,
          tax: 0,
          total_insurance_charge: 0,
          total_fuel_surcharge: 0,
          taxCode,
          taxRate,
          origin_country,
          destination: destination_country,
          numberOfHawbs: hawbsByRoute[route].length,
        };

        for (const hawb of hawbsByRoute[route]) {
          routeData.rate += hawb.pd_amount;
          routeData.tax += hawb.tax;
        }

        routeData.service_options.push({
          service_option: 'B2B',
          total: routeData.rate,
          tax: routeData.tax,
        });

        // eslint-disable-next-line no-await-in-loop
        const totalRateInSGD = await CurrencyConversionService.exchangeMoney(
          routeData.rate,
          invoiceCurrency,
          'SGD',
          invoiceMonth,
        );

        // eslint-disable-next-line no-await-in-loop
        const totalTaxInSGD = await CurrencyConversionService.exchangeMoney(
          routeData.tax,
          invoiceCurrency,
          'SGD',
          invoiceMonth,
        );

        result.push({
          ...routeData,
          rate: routeData.rate,
          tax: routeData.tax,
          totalRateInSGD,
          totalTaxInSGD,
        });
      }
    }

    return result;
  }

  async updateParcelInvoiceStatus(successParcels: any, dataForPDF: any) {
    for (const parcels of CommonUtils.chunkArrayGenerator(
      successParcels,
      CONSTANTS.NUMBER_OF_PARCEL_MESSAGES_TO_QUEUE,
    )) {
      const result = await Promise.allSettled(
        parcels.map((parcel) => {
          const updatePayload = {
            id: parcel.id,
            invoice_no: dataForPDF.invoiceNumber,
            PD_invoicing_status: [
              {
                status: ENUM.invoiceStatus.PD_invoiced,
                timestamp: dataForPDF.invoice_generation_datetime,
              },
            ],
            PD_invoicing_tax: parcel.tax,
            PD_rate_currency: parcel.invoiceRateCurrency,
            PD_rate: parcel.rate,
          };

          return AzureStorageQueue.sendBase64Message(
            CONSTANTS.AZURE_STORAGE_QUEUE_NAME.PARCELS_UPDATE_AFTER_INVOICING,
            {
              updatePayload,
              invoiceType: ENUM.invoiceType.PD,
              merchantAccountNumber: dataForPDF.merchant_no,
            },
          );
        }),
      );

      for (const [idx, item] of result.entries()) {
        if (item.status === 'rejected') {
          invoiceLogger.error({
            message: 'Failed queue update parcels status after invoicing',
            parcelId: parcels[idx].id,
            invoiceNo: dataForPDF.invoiceNumber,
            error: item.reason,
          });
        }
      }
    }
  }

  calculateInvoiceStatus(parcel: any, merchantRti: any) {
    const deliveryConfigStatuses = [
      ENUM.rti_info.standardOrPlus.deliveredOrUndeliverd1st,
      ENUM.rti_info.standardOrPlus.deliveredOrUndeliverd2nd,
      ENUM.rti_info.standardOrPlus.deliveredOrUndeliverd3rd,
    ];

    const deliveryStatus = {
      [ENUM.parcelOption.standard]: merchantRti.standard.deliveryStatus,
      [ENUM.parcelOption.plus]: merchantRti.plus.deliveryStatus,
    }[parcel.service_option];

    if (deliveryConfigStatuses.includes(deliveryStatus)) {
      const status = parcel.tracking_status.find(
        (ts: any) =>
          ts.status ===
          StatusMappingService.getManifestStatus(ENUM.parcelStatus.successful_delivery),
      );

      if (status) {
        parcel.trStatusInvoice = 'Job Completed';
        parcel.trStatusInvoiceDate = status.date;
      }
    } else if (
      deliveryStatus ===
      ENUM.rti_info.standardOrPlus.receivedAtWarehouseArrivedProcessingAtSortingHub
    ) {
      const statusKey = {
        [ENUM.shipmentType.domestic]: ENUM.parcelStatus.arrived_and_processing_at_sorting_hub,
        [ENUM.shipmentType.international]: ENUM.parcelStatus.received_at_warehouse,
      }[parcel.shipment_type];

      const status = parcel.tracking_status.find(
        (ts: any) => ts.status === StatusMappingService.getManifestStatus(statusKey),
      );

      const pdReadyToInvoiceStatus = parcel.PD_invoicing_status.at(-1);

      if (status) {
        parcel.trStatusInvoice = status.status;
        parcel.trStatusInvoiceDate = pdReadyToInvoiceStatus.timestamp;
      }
    }
  }

  async generatePDInvoiceExcelBuffer(
    merchant: any,
    invoice: any,
    parcels: any,
    hawbs: any,
    exSurcharges: any,
  ) {
    const { invoiceRateCurrency, totalDue, invoiceNumber } = invoice;
    const invoiceContent = [
      ['Merchant Name:', merchant.merchant_name],
      ['Merchant Account No:', merchant.merchant_account_number],
      [],
    ];

    if (parcels.length > 0) {
      invoiceLogger.info({
        message: 'Generate delivery CSV',
        merchantName: merchant.merchant_name,
        invoiceNumber,
      });

      invoiceContent.push(
        ...this.generateParcelDelivery(invoice, parcels, merchant.invoicing_info.rti_info),
      );
    }

    const insuranceSurcharges = parcels.filter((parcel: any) => parcel.insurance_amount);

    if (insuranceSurcharges.length > 0) {
      invoiceLogger.info({
        message: 'Generate insurance CSV',
        merchantName: merchant.merchant_name,
        invoiceNumber,
      });

      invoiceContent.push(
        ...this.generateInsuranceSurcharge(
          invoice,
          insuranceSurcharges,
          merchant.invoicing_info.insurance,
        ),
      );
    }

    const fuelSurcharges = parcels.filter((parcel: any) => parcel.fuelSurcharge);

    if (fuelSurcharges.length > 0) {
      invoiceLogger.info({
        message: 'Generate fuel CSV',
        merchantName: merchant.merchant_name,
        invoiceNumber,
      });
      invoiceContent.push(...this.generateFuelSurcharge(invoice, fuelSurcharges));
    }

    const ddpSurcharges = parcels
      .filter((parcel) => parcel.ddp)
      .map((parcel) => ({ ...parcel, ...parcel.ddp }));

    if (ddpSurcharges.length > 0 || exSurcharges.length > 0) {
      invoiceLogger.info({
        message: 'Generate surcharge CSV',
        merchantName: merchant.merchant_name,
        invoiceNumber,
      });
      invoiceContent.push(...this.generateSurcharge(invoice, ddpSurcharges, exSurcharges));
    }

    // Add B2B Invoice Detail
    if (hawbs.length > 0) {
      invoiceLogger.info({
        message: 'Generate B2B CSV',
        merchantName: merchant.merchant_name,
        invoiceNumber,
      });
      const dataInvoiceDetail = [];

      for (const hawb of hawbs) {
        // eslint-disable-next-line no-await-in-loop
        const parcelsInHawb = await ManifestItemService.getParcelsInHawb(hawb);
        const statusTime = parcelsInHawb[0]?.tracking_status?.find(
          (tracking) => tracking.status === CONSTANTS.BOOKING_MONITOR_STATUS.PACKED_TO_MAWB,
        );
        hawb.statusDate = statusTime?.date || '';
        hawb.status = 'Scanned To MAWB';
        hawb.service_option = 'B2B';
        hawb.rateCurrency = invoiceRateCurrency;
        hawb.taxCurrency = invoiceRateCurrency;
        hawb.parcel_tracking_id = parcelsInHawb[0]?.tracking_id;
        hawb.merchant_order_no = parcelsInHawb[0]?.merchant_order_no;
        dataInvoiceDetail.push(hawb);

        for (let i = 1; i < parcelsInHawb.length; i++) {
          dataInvoiceDetail.push({
            parcel_tracking_id: parcelsInHawb[i].tracking_id,
            merchant_order_no: parcelsInHawb[i].merchant_order_no,
          });
        }

        dataInvoiceDetail.push({});
      }

      invoiceContent.push(...this.generateB2BInvoiceDetail(invoice, dataInvoiceDetail, hawbs));
    }

    const pdFooter = [
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      'TOTAL DUE',
      invoiceRateCurrency,
      ExcelUtils.showValueByCurrency(totalDue, invoiceRateCurrency),
    ];
    invoiceContent.push([], [], pdFooter);

    return ExcelUtils.createBufferXLSX(`${invoiceNumber} detail`, invoiceContent);
  }

  generateSubtotalSection(invoice: any, rate: any, tax: any) {
    const { invoiceRateCurrency: currency } = invoice;

    const footer = [
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      'SUBTOTAL',
      currency,
      ExcelUtils.showValueByCurrency(rate, currency),
      currency,
      ExcelUtils.showValueByCurrency(tax, currency),
    ];

    return [[], footer];
  }

  generateParcelDelivery(invoice: any, parcels: any, merchantRti: any) {
    const weightUnit = parcels[0]?.chargeWeightUnit;

    for (const parcel of parcels) this.calculateInvoiceStatus(parcel, merchantRti);

    const fields = [
      {
        label: 'PLS Shipment Booking Reference',
        value: 'id',
      },
      {
        label: 'Shipment Tracking ID',
        value: (row: any) => ({ v: row.tracking_id || '', t: 's' }),
      },
      {
        label: 'Merchant Order No',
        value: 'merchant_order_no',
      },
      {
        label: 'Shipment Booking Date/UTC',
        value: 'order_date',
      },
      {
        label: 'Rate Zone',
        value: 'rate_zone',
      },
      {
        label: 'Service Option',
        value: 'service_option',
      },
      {
        label: 'Status',
        value: 'trStatusInvoice',
      },
      {
        label: 'Status (Date and Time/UTC)',
        value: 'trStatusInvoiceDate',
      },
      {
        label: `Rate Sheet Chargeable Weight (${weightUnit})`,
        value: (row: any) =>
          row.chargeType === ENUM.invoiceChargeType.delivery
            ? separateThousands(row.rate_sheet_chg_wt)
            : '',
      },
      {
        label: `Chargeable Weight (${weightUnit})`,
        value: (row: any) => separateThousands(row.chargeableWeight),
      },
      {
        label: `Gross Weight (${weightUnit})`,
        value: (row: any) => separateThousands(row.grossWeight),
      },
      {
        label: `Volumetric Weight (${weightUnit})`,
        value: (row: any) => separateThousands(row.VolumetricWeigh),
      },
      {
        label: 'Tax code',
        value: 'taxCode',
      },
      {
        label: 'Charge Type',
        value: 'chargeType',
        default: ENUM.invoiceChargeType.delivery,
      },
      {
        label: 'Rate Currency',
        value: () => invoice.invoiceRateCurrency,
      },
      {
        label: 'Rate',
        value: (row: any) => ExcelUtils.showValueByCurrency(row.rate, invoice.invoiceRateCurrency),
      },
      {
        label: 'Tax Currency',
        value: () => invoice.invoiceRateCurrency,
      },
      {
        label: 'Tax',
        value: (row: any) => ExcelUtils.showValueByCurrency(row.tax, invoice.invoiceRateCurrency),
      },
    ];
    invoiceLogger.info({
      message: `Invoice: ${invoice.invoiceNumber} has ${parcels.length} rows of Parcel Delivery Invoice Details`,
    });

    return [['PARCEL DELIVERY INVOICE DETAILS']]
      .concat(ExcelUtils.generateSheetData(parcels, fields))
      .concat(
        this.generateSubtotalSection(
          invoice,
          sumBy(parcels, (parcel: any) => Number(parcel.rate)),
          sumBy(parcels, (parcel: any) => Number(parcel.tax)),
        ),
      );
  }

  generateFuelSurcharge(invoice: any, parcels: any) {
    const fields = [
      {
        label: 'PLS Shipment Booking Reference',
        value: 'id',
      },
      {
        label: 'Routing',
        value: (row: any) => `${row.origin_country} - ${row.country_ISO2}`,
      },
      {
        label: 'Currency',
        value: (row: any) => row.fuel_surcharge.currency,
      },
      {
        label: 'Rate per KG',
        value: (row: any) =>
          formatCurrency(row.fuel_surcharge.rates_per_kg, row.fuel_surcharge.currency),
      },
      {
        label: 'Chargeable Weight (KG)',
        value: 'chargeableWeight',
      },
      {
        label: 'Tax Code',
        value: () => 'ZR',
      },
      ...Array.from({ length: 8 })
        .fill('')
        .map(() => ({
          label: '',
          value: '',
        })),

      { label: 'Surcharge Currency', value: (row: any) => row.fuel_surcharge.currency },
      {
        label: 'Surcharge',
        value: (row: any) =>
          ExcelUtils.showValueByCurrency(
            +row.chargeableWeight * +row.fuel_surcharge.rates_per_kg,
            row.fuel_surcharge.currency,
          ),
      },
      { label: 'Tax Currency', value: (row: any) => row.fuel_surcharge.currency },
      {
        label: 'Tax',
        value: (row: any) => ExcelUtils.showValueByCurrency(0, row.fuel_surcharge.currency),
      },
    ];

    return [['FUEL SURCHARGES']]
      .concat(ExcelUtils.generateSheetData(parcels, fields))
      .concat(this.generateSubtotalSection(invoice, sumBy(parcels, 'fuelSurcharge'), 0));
  }

  generateInsuranceSurcharge(invoice: any, parcels: any, merchantInsurance: any = {}) {
    const {
      maximumValue = 'N/A',
      valueCurrency = 'N/A',
      chargedToMerchant = 'N/A',
    } = merchantInsurance;

    const fields = [
      {
        label: 'PLS Shipment Booking Reference',
        value: 'id',
      },
      {
        label: 'Shipment Value',
        value: (row: any) =>
          separateThousands(
            row.item.reduce(
              (total: any, item: any) => total + Number(item.total_declared_value),
              0,
            ),
          ),
      },
      {
        label: 'Max Coverage',
        value: () => maximumValue,
      },
      {
        label: 'Currency',
        value: () => valueCurrency,
      },
      {
        label: 'Percentage',
        value: () => chargedToMerchant,
      },
      {
        label: 'Tax Code',
        value: () => 'ZR',
      },
      ...Array.from({ length: 8 })
        .fill('')
        .map(() => ({
          label: '',
          value: '',
        })),
      {
        label: 'Surcharge Currency',
        value: () => invoice.invoiceRateCurrency,
      },
      {
        label: 'Surcharge',
        value: (row: any) =>
          ExcelUtils.showValueByCurrency(+row.insurance_amount, invoice.invoiceRateCurrency),
      },
      {
        label: 'Tax Currency',
        value: () => invoice.invoiceRateCurrency,
      },
      {
        label: 'Tax',
        value: () => ExcelUtils.showValueByCurrency(0, invoice.invoiceRateCurrency),
      },
    ];

    return [['INSURANCE CHARGES']].concat(ExcelUtils.generateSheetData(parcels, fields)).concat(
      this.generateSubtotalSection(
        invoice,
        sumBy(parcels, (parcel: any) => Number(parcel.insurance_amount)),
        0,
      ),
    );
  }

  generateB2BInvoiceDetail(invoice: any, dataInvoiceDetail: any, hawbs: any) {
    const weightUnit = hawbs[0]?.weight_unit;
    const fields = [
      {
        label: 'HAWB Number',
        value: 'id',
        default: '',
      },
      {
        label: 'Shipment Tracking ID',
        value: (row) => row.parcel_tracking_id || '',
      },
      {
        label: 'Merchant Order No',
        value: (row) => row.merchant_order_no || '',
      },
      {
        label: '',
        value: '',
        default: '',
      },
      {
        label: 'Rate Zone',
        value: 'destination_country',
        default: '',
      },
      {
        label: 'Service Option',
        value: (row) => row.service_option || '',
      },
      {
        label: 'Status',
        value: (row) => row.status || '',
      },
      {
        label: 'Status (Date and Time/UTC)',
        value: 'statusDate',
        default: '',
      },
      {
        label: 'Charge Amount',
        value: (row) => row.pd_amount || '',
      },
      {
        label: `Chargeable Weight (${weightUnit})`,
        value: (row) => (row.chargeable_weight ? separateThousands(row.chargeable_weight) : ''),
      },
      {
        label: `Gross Weight (${weightUnit})`,
        value: (row) => (row.gross_weight ? separateThousands(row.gross_weight) : ''),
      },
      {
        label: `Volumetric Weight (${weightUnit})`,
        value: (row) => (row.volumetric_weight ? separateThousands(row?.volumetric_weight) : ''),
      },
      {
        label: 'Tax code',
        value: 'taxCode',
        default: '',
      },
      {
        label: 'Charge Type',
        value: 'chargeType',
        default: '',
      },
      {
        label: 'Rate Currency',
        value: (row) => row.rateCurrency || '',
        default: '',
      },
      {
        label: 'Rate',
        value: (row) =>
          row.pd_amount === undefined
            ? ''
            : ExcelUtils.showValueByCurrency(row.pd_amount, invoice.invoiceRateCurrency),
      },
      {
        label: 'Tax Currency',
        value: (row) => row.taxCurrency || '',
        default: '',
      },
      {
        label: 'Tax',
        value: (row: any) =>
          row.tax === undefined
            ? ''
            : ExcelUtils.showValueByCurrency(row.tax, invoice.invoiceRateCurrency),
      },
    ];
    invoiceLogger.info({
      message: `Invoice: ${invoice.invoiceNumber} has ${hawbs.length} rows of B2B Invoice Details`,
    });

    return [['B2B INVOICE DETAILS']]
      .concat(ExcelUtils.generateSheetData(dataInvoiceDetail, fields))
      .concat(
        this.generateSubtotalSection(
          invoice,
          sumBy(hawbs, (hawb: any) => Number(hawb.pd_amount)),
          sumBy(hawbs, (hawb: any) => Number(hawb.tax)),
        ),
      );
  }

  generateSurcharge(invoice: any, ddpSurcharges: any, exSurcharges: any) {
    const exSurchargesData = exSurcharges.map((surcharge) => {
      if (surcharge.reference_type === 'merchant') {
        const surchargeType = surcharge.type;

        return {
          ...surcharge,
          parxl_id: 'N/A',
          tracking_id: 'N/A',
          type: `${surchargeType} Flat Fee`,
        };
      }

      return surcharge;
    });
    const fields = [
      {
        label: 'PLS Shipment Booking Reference',
        value: (row) => ({ v: row.parxl_id || row.id, t: 's' }),
      },
      {
        label: 'Shipment Tracking ID',
        value: (row) => ({ v: row.tracking_id || '', t: 's' }),
        default: '',
      },
      {
        label: 'Fee Type',
        value: 'type',
        default: '',
      },
      {
        label: 'Tax code',
        value: 'taxCode',
        default: '',
      },
      ...Array.from({ length: 10 })
        .fill('')
        .map(() => ({
          label: '',
          value: '',
        })),

      { label: 'Rate Currency', value: () => invoice.invoiceRateCurrency },
      {
        label: 'Rate',
        value: (row) => ExcelUtils.showValueByCurrency(row.rate, invoice.invoiceRateCurrency),
      },
      { label: 'Tax Currency', value: () => invoice.invoiceRateCurrency },
      {
        label: 'Tax',
        value: (row) => ExcelUtils.showValueByCurrency(row.tax, invoice.invoiceRateCurrency),
      },
    ];

    const totalRate =
      sumBy(ddpSurcharges, (item: any) => Number(item.rate)) +
      sumBy(exSurcharges, (item: any) => Number(item.rate));
    const totalTax =
      sumBy(ddpSurcharges, (item: any) => Number(item.tax)) +
      sumBy(exSurcharges, (item: any) => Number(item.tax));

    return [['SURCHARGES INVOICE DETAILS']]
      .concat(ExcelUtils.generateSheetData(ddpSurcharges.concat(exSurchargesData), fields))
      .concat(this.generateSubtotalSection(invoice, totalRate, totalTax));
  }

  generateInvoiceCheckCsv(listParcelsMissInvoice) {
    const fields = [
      {
        label: 'Parxl ID',
        value: 'id',
      },
      {
        label: 'Merchant Name',
        value: 'merchantName',
      },
      {
        label: 'Scenario Type',
        value: 'type',
      },
    ];
    const invoiceCheckCsv = json2csv.parse(listParcelsMissInvoice, { fields });

    return invoiceCheckCsv;
  }
})();
