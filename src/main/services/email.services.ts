import url from 'url';

import { format } from 'date-fns';

import { AzureBlobStorage } from '~/common/azure/azure-blob-storage.js';
import { AzureStorageQueue } from '~/common/azure/azure-storage-queue.js';

import { AesUtils } from '~/models/aesUtils.js';
import { ENUM } from '~/models/enum.js';
import { ReportUtils } from '~/models/reportUtils.js';

import { DateUtils } from '~/utilities/dateUtils.js';
import { DtV2InvoicingUtils } from '~/utilities/dt-v2-invoicing-utils.js';
import { EmailUtils } from '~/utilities/emailUtils.js';
import { InvoiceUtils } from '~/utilities/invoiceUtils.js';
import { AppLogger } from '~/utilities/logUtils.js';
import { JSZipUtils } from '~/utilities/zipUtils.js';

import { config } from '~/configs/index.js';
import { CONSTANTS } from '~/constants/index.js';
import { Daos } from '~/daos/index.js';

import { IMawbCreated } from '../types/mawb.type.js';
import { PreAlertEmailPart, preAlertEmailUtils } from '../utilities/pre-alert-email.utils.js';

import CountryISOService from './countryISO-service.js';
import { HawbService } from './hawb.service.js';
import { InvoicingServices } from './invoicing-services.js';
import { ManifestItemService } from './manifest-item.service.js';
import { MerchantService } from './merchant-service.js';
import PdInvoiceService from './pd-invoice-service.js';
import { RateServices } from './rate-services.js';
import { SurchargeService } from './surcharge-service.js';
import { UserServices } from './user.services.js';

const logger = new AppLogger({ name: 'EmailService' });
const invoiceLogger = logger.extends({ business: ENUM.FunctionName.INVOICE });
const invoiceCheckLogger = logger.extends({ business: ENUM.FunctionName.INVOICE_CHECK });

export const EmailService = {
  async sendMailRequestApproveDeminimis(objectDeminimis: any) {
    try {
      const ui_url = url.resolve(process.env.UI_URL || '', '#/distribution/deminimis');
      const decrypted = AesUtils.decryptTaxRate(objectDeminimis);
      const getRequestor: any = await UserServices.getUserByNameOrEmail(decrypted.created_by);
      const getApprover: any = await UserServices.getUserByNameOrEmail(decrypted.approver);

      if (!getRequestor.success) {
        throw getRequestor.error;
      }

      if (!getApprover.success) {
        throw getApprover.error;
      }

      const requestor = getRequestor.success ? getRequestor.data[0] : null;
      const approver = getApprover.success ? getApprover.data[0] : null;

      if (requestor && approver) {
        const from = config.sendgrid.api_email;
        const to = approver.email;
        const subject = 'PLS notification - Request for De minimis Update Approval';
        const html = `<p>Dear ${approver.name},</p>
      <p>
      ${requestor.name} has requested you to approve the De minimis Update for ${decrypted.country}. Please login into PLS (${ui_url}) to process the request.
      </p>
      <p>Regards,</p>
      <p>PLS</p>`;
        const mailOptions = {
          from,
          to,
          subject,
          html,
          attachments: [],
        };

        return await EmailUtils.sendEmailSendgrid(mailOptions);
      }
    } catch (error) {
      logger.error({ error: error.message });
    }
  },

  async sendMailApproveOrRejectDeminimis(objectDeminimis: any, isApproval: boolean) {
    try {
      const decrypted = AesUtils.decryptTaxRate(objectDeminimis);
      const getRequestor: any = await UserServices.getUserByNameOrEmail(decrypted.created_by);
      const getApprover: any = await UserServices.getUserByNameOrEmail(decrypted.approver);

      if (!getRequestor.success) {
        throw getRequestor.error;
      }

      if (!getApprover.success) {
        throw getApprover.error;
      }

      const requestor = getRequestor.success ? getRequestor.data[0] : null;
      const approver = getApprover.success ? getApprover.data[0] : null;

      if (requestor && approver) {
        const from = config.sendgrid.api_email;
        const to = requestor.email;
        const subject = `PLS notification - Request for De minimis Update ${isApproval ? 'Approval' : 'Rejected'}`;
        const html = `Dear ${requestor.name},
      
      ${approver.name} has ${isApproval ? 'approved' : 'rejected'} the De minimis Update for ${decrypted.country}. 
      
      This is a system notification, there is no further action required from you.
      
      Regards,
      
      PLS`;
        const mailOptions = {
          from,
          to,
          subject,
          html,
          attachments: [],
        };

        return await EmailUtils.sendEmailSendgrid(mailOptions);
      }
    } catch (error) {
      logger.error({ error: error.message });
    }
  },

  async notifyMerchantB2CDisabled({
    email,
    username,
    merchantName,
  }: {
    email: string;
    username: string;
    merchantName: string;
  }) {
    const subject = 'Your Merchant Portal account has been temporarily disabled';
    const html = `
    <div style="font-size: 15px;">
      Hi <b>${username}</b>, 
      <br><br> 
      Your Merchant Portal account for <b>${merchantName}</b> has been disabled due to inactivity and/or by an administrator.
      <br>
      We understand that your account is important to you. To restore it, please contact your account manager <NAME_EMAIL> - we're always available and happy to help. 
      <br><br> 
      Your Parxl team
      <br><br> 
      <i style="opacity: 0.6; font-size: 13px;">This is an auto-generated email. Please do not reply as it will not be received.</i>
    </div>`;
    const mail_op = {
      from: config.sendgrid.api_email,
      to: email,
      cc: process.env.APP_ENV === 'UAT' ? config.sendgrid.api_cc_email : '',
      subject,
      html,
    };

    try {
      const response = await EmailUtils.sendEmailSendgrid(mail_op, {
        fromFunction: 'notifyMerchantB2CDisabled',
      });

      return response && response.success;
    } catch (error) {
      logger.error({
        message: 'failed to send notify account disabled to merchant',
        merchantName,
        username,
        error,
      });

      return error;
    }
  },

  async notifyLspUserB2CDisabled({
    email,
    username,
    opHubName,
    isSuperuser = false,
  }: {
    email: string;
    username: string;
    opHubName?: string;
    isSuperuser?: boolean;
  }) {
    const subject = 'Your LSP Portal account has been temporarily disabled';
    const html = `
    <div style="font-size: 15px;">
      Hi <b>${username}</b>, 
      <br><br> 
      Your LSP Portal account ${isSuperuser ? '' : `for <b>${opHubName}</b>`} has been disabled due to inactivity and/or by an administrator.
      <br>
      We understand that your account is important to you. To restore it, please contact your account manager <NAME_EMAIL> - we're always available and happy to help.
      <br><br> 
      Your Parxl team
      <br><br> 
      <i style="opacity: 0.6; font-size: 13px;">This is an auto-generated email. Please do not reply as it will not be received.</i>
    </div>`;
    const mail_op = {
      from: config.sendgrid.ops_email,
      to: email,
      cc: process.env.APP_ENV === 'UAT' ? config.sendgrid.api_cc_email : '',
      subject,
      html,
    };

    try {
      const response = await EmailUtils.sendEmailSendgrid(mail_op, {
        fromFunction: 'notifyMerchantB2CDisabled',
      });

      return response && response.success;
    } catch (error) {
      logger.error({ message: 'failed to notify ophub user disabled account', username, error });

      return error;
    }
  },

  sendRateSheetExpiryNotifyEmail(typeOfNotify: string, merchantRatesMap: any) {
    function getEmailTemplate(merchantRateMap: any, emailType: string) {
      const tableData = [];

      for (const { merchant, rates } of merchantRateMap.values()) {
        for (const rate of rates) {
          tableData.push(`
            <tr>
              <td style="padding: 3px 10px">${merchant?.merchant_name || ''}</td>
              <td style="padding: 3px 10px">
              ${DateUtils.localToStr('DD-MM-YYYY', new Date(rate.validity_from))} to ${DateUtils.localToStr('DD-MM-YYYY', new Date(rate.validity_to))}</td>
            </tr>
          `);
        }
      }

      return `
        <body style="font-size: 16px">
          <p>Hi Commercial Team,</p>
          <p>The following rate sheet(s) is/are about to expire in ${emailType === ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.MONTH ? '30 days' : '7 days'} time. Please re-new the contracted rate sheet if
            needed.</p>
          <table border="1" cellspacing="0" style="border-collapse: collapse;
          ">
            <thead>
              <tr>
                <th style="padding: 5px 10px">Merchant Name</th>
                <th style="padding: 5px 10px">Validity Dates</th>
              </tr>
            </thead>
            <tbody>
              ${tableData.join('')}
            </tbody>
          </table>
          <p>Best,</p>
          <p>Parxl Team</p>
          <i style='opacity: 0.6; font-size: 13px;'>This is a computer-generated email. Please do not reply.</i>
        </body>
      `;
    }

    const mailContents = {
      from: config.sendgrid.api_email,
      to: process.env.VALIDITY_EXPIRE_EMAIL_TO,
      cc: process.env.VALIDITY_EXPIRE_EMAIL_CC || '',
      subject: `Reminder - Rate sheet(s) about to expire ${
        typeOfNotify === ENUM.RATE_SHEET_NOTIFY.TYPE_OF_NOTIFY.MONTH ? '30 days' : '7 days'
      }`,
      html: getEmailTemplate(merchantRatesMap, typeOfNotify),
    };

    return EmailUtils.sendEmailSendgrid(mailContents);
  },

  async notifyUpdateParcelDimensionSuccessfully(
    fromEmail: string,
    toEmail: string,
    numberOfParcels: number,
    fileName: string,
    timeUpload: string,
  ) {
    const html = `
    <div style="font-size: 15px;">
        Dear User,
        <br><br>
        The file ${fileName} uploaded to PLS Parcel Update Page at ${timeUpload}
            has been successfully processed.
        <br>
        All ${numberOfParcels} lines have been processed and updated.
    </div>
    `;
    const mailOption = {
      from: fromEmail,
      to: toEmail,
      subject: 'Successful Update of Parcel Dimensions',
      html,
    };

    try {
      await EmailUtils.sendEmailSendgrid(mailOption, {
        fromFunction: 'notifyUpdateParcelDimensionSuccessfully',
      });
    } catch (error) {
      logger.error({
        message: 'failed to send notify update parcel dimensions successfully',
        error,
      });
    }
  },

  async notifyUpdateParcelDimensionUnsuccessfully(
    fromEmail: string,
    toEmail: string,
    attachment: any,
    fileName: string,
    timeUpload: string,
  ) {
    const html = `
    <div style="font-size: 15px;">
        Dear User,
        <br><br>
        The file ${fileName} uploaded to PLS Parcel Update Page at ${timeUpload}
            has NOT been successfully processed.
        <br>
        The rows have been found to have issues have been attached in the .csv file attached.
            Please check and re-upload ONLY the rows with problems
    </div>
    `;
    const mailOption = {
      from: fromEmail,
      to: toEmail,
      subject: 'Error:Unsuccessful Update of Parcel Dimensions',
      html,
      attachments: [
        {
          filename: 'failed_updating_parcels_dimension.csv',
          content: Buffer.from(attachment).toString('base64'),
        },
      ],
    };

    try {
      await EmailUtils.sendEmailSendgrid(mailOption, {
        fromFunction: 'notifyUpdateParcelDimensionUnsuccessfully',
      });
    } catch (error) {
      logger.error({
        message: 'failed to send notify update parcel dimensions unsuccessfully',
        error,
      });
    }
  },

  getErrorListCsv(errorParcels: any[]) {
    if (errorParcels.length === 0) return '';

    const csvHeader = 'Parcel ID, Error Type';
    const csvContent = errorParcels.map((item) => `${item.id}, ${item.message}`).join('\r\n');

    return `${csvHeader}\r\n${csvContent}`;
  },

  getInvoiceReportContent(
    tableDatas: { merchant_name: string; noOfParcelsForPD: number; noOfParcelsForDT: number }[],
    time,
    errorListCsv: string,
  ) {
    const { date, month, year } = time;
    const sortedTableDatas = tableDatas
      .filter((data) => data.noOfParcelsForPD !== 0 || data.noOfParcelsForDT !== 0)
      .sort((a, b) => b.noOfParcelsForPD - a.noOfParcelsForPD);
    const mail_op: any = {
      from: config.sendgrid.tech_email,
      to:
        process.env.APP_ENV === 'UAT'
          ? config.sendgrid.parxl_customs_test_email
          : `${config.sendgrid.tech_email}; ${config.sendgrid.api_email}`,
      subject: `Invoice Summary - Expected volumes for invoicing on [${format(new Date(year, month - 1, date), 'dd-MM-yyyy')}]`,
      html: `
        <p>Hi Team, </p>
        <br />
        <p>Please see below for [${format(new Date(year, month - 1, date), 'dd-MM-yyyy')}] 
        expected parcel volume subjected for Parcel Delivery Invoice Invoice.
        This excludes the count for parcels subjected to additional surcharges.</p>
        <br />
        <table style="border-collapse: collapse" cellpadding="5" border="1" >
          <tr style="background-color: #f4f5f7">
            <th width="130"><strong>S/N</strong></th>
            <th width="130"><strong>Merchant Name</strong></th>
            <th width="130"><strong>No. of Parcels for PD</strong></th>
            <th width="130"><strong>No. of Parcels for DT</strong></th>
          </tr>
          ${sortedTableDatas
            .map(
              (item, index) => `
            <tr>
              <td>${index + 1}</td>
              <td>${item.merchant_name}</td>
              <td>${item.noOfParcelsForPD}</td>
              <td>${item.noOfParcelsForDT === 0 ? '' : item.noOfParcelsForDT}</td>
            </tr>`,
            )
            .join('')}
        </table>`,
    };

    if (errorListCsv) {
      mail_op.attachments = [
        {
          filename: 'error list.csv',
          content: Buffer.from(errorListCsv),
        },
      ];
    }

    return mail_op;
  },

  async sendMailToMerchant(
    date,
    month,
    year,
    startDayCountInvoice,
    merchant,
    bankDetails,
    taxCountryManagementInfo,
  ) {
    const {
      merchant_account_number: merchantAccountNumber,
      merchant_name: merchantName,
      invoicing_info: invoicingInfo,
      invoice_address: invoiceAddress,
      taxCodes,
    } = merchant;

    try {
      merchant.bankDetail = InvoiceUtils.getMerchantBankDetail(merchant, bankDetails);

      if (!taxCodes || taxCodes.length === 0) {
        return {
          merchant_name: merchantName,
          message: 'Merchant tax code not found',
        };
      }

      const [invoiceCurrency, invoiceNo] = await Promise.all([
        RateServices.getMerchantRateCurrency(merchantAccountNumber),
        ManifestItemService.getInvoiceNumber(merchantAccountNumber, 'PD'),
      ]);

      if (!invoiceCurrency) {
        return {
          merchant_name: merchantName,
          message: 'Invoice currency not found',
        };
      }

      let [exSurcharges, parcels, hawbs] = await Promise.all([
        SurchargeService.getUninvoivedExceptionSurcharges(merchantName),
        PdInvoiceService.getReadyToInvoiceParcels(
          merchant,
          year,
          month,
          date,
          startDayCountInvoice,
        ),
        HawbService.getEligibleHawbs(AesUtils.CrtCounterEncrypt(merchantAccountNumber)),
      ]);

      if (exSurcharges.length === 0 && parcels.length === 0 && hawbs.length === 0) {
        return {
          merchant_name: merchantName,
          message: 'Merchant does not have any parcels or hawbs for invoicing',
        };
      }

      invoiceLogger.info({
        message: `Found ${parcels.length} parcels, ${hawbs.length} hawbs, ${exSurcharges.length} exception surcharges`,
        merchantName,
        invoiceNo,
      });

      // Calculate charge for parcels
      const [successParcels, calculateErrors] = await PdInvoiceService.calculateChargesByParcels(
        merchant,
        parcels,
      );

      invoiceLogger.info({
        message: `Calculate done with ${successParcels.length} parcles success, ${calculateErrors} parcels error`,
        merchantName,
        invoiceNo,
      });
      parcels = undefined; // NOSONAR

      // calculate charge for hawbs
      const [successHawbs, errorHawbs] = await PdInvoiceService.calculateChargesByHawbs(
        merchant,
        hawbs,
      );

      invoiceLogger.info({
        message: `Calculate done with ${successHawbs.length} hawbs success, ${errorHawbs} hawbs error`,
        merchantName,
        invoiceNo,
      });
      hawbs = undefined; // NOSONAR

      // this object contain data to generate PDF invoice
      const {
        merchant_address_line1: merchantAddressLine1,
        merchant_address_line2: merchantAddressLine2,
        merchant_address_line3: merchantAddressLine3,
      } = InvoiceUtils.getInvoiceAddress(invoiceAddress, false);

      const invoiceMonth = new Date(Date.UTC(year, month - 1, date - 1));
      const dataForPDF = {
        merchant_name: merchantName,
        merchant_no: merchantAccountNumber,
        merchant_address_line1: merchantAddressLine1,
        merchant_address_line2: merchantAddressLine2,
        merchant_address_line3: merchantAddressLine3,
        invoiceNumber: invoiceNo,
        invoice_generation_datetime: new Date(),
        invoice_date:
          date === 1
            ? format(invoiceMonth, 'dd MMM yyyy')
            : format(new Date(Date.UTC(year, month, 0)), 'dd MMM yyyy'),
        invoiceMonth: DateUtils.utcToStr('MMM YYYY', invoiceMonth),
        invoiceRateCurrency: invoiceCurrency,
        routings: [],
        merchant_type: invoicingInfo?.tax?.constParty,
        taxZone: invoicingInfo?.tax?.country,
        surcharges: [],
        creditTerm: invoicingInfo?.tax?.creditTerm || 'Immediate',
      };

      // create array of promise to handle very complicated logic
      const [parcelRoutes, hawbRoutes, exSurchargesByRouteAndType, ddpSurchage] = await Promise.all(
        [
          PdInvoiceService.calculateChargesByRoutes(successParcels, invoiceCurrency, invoiceMonth),
          PdInvoiceService.calculateHawbChargesByRoutes(
            successHawbs,
            merchant,
            invoiceCurrency,
            invoiceMonth,
          ),
          InvoicingServices.groupExSurchargesByRouteAndType(
            exSurcharges,
            invoiceCurrency,
            merchant,
            invoiceMonth,
          ),
          DtV2InvoicingUtils.getMerchantInvoiceByRoutes(
            successParcels,
            merchant,
            invoiceCurrency,
            ENUM.invoiceType.PD,
            invoiceMonth,
          ),
        ],
      );

      dataForPDF.routings = [...parcelRoutes, ...hawbRoutes];

      dataForPDF.surcharges.push(...exSurchargesByRouteAndType);

      // if DDP was config calculate in PD, ddpSurchage.routes will have elements.
      if (ddpSurchage.routes?.length > 0) {
        dataForPDF.surcharges.push(...ddpSurchage.routes);
      }

      let archive = new JSZipUtils();

      invoiceLogger.info({ message: 'Generate PDF', merchantName, invoiceNo });
      let pdfBuffer = await ReportUtils.generatePdInvoice(
        dataForPDF,
        merchant,
        taxCountryManagementInfo,
      );

      archive.add(`${invoiceNo}.pdf`, pdfBuffer);
      pdfBuffer = undefined; // NOSONAR

      invoiceLogger.info({ message: 'Generate CSV', merchantName, invoiceNo });
      let pdInvoiceExcelBuffer = await PdInvoiceService.generatePDInvoiceExcelBuffer(
        merchant,
        dataForPDF,
        successParcels,
        successHawbs,
        exSurcharges,
      );

      invoiceLogger.info({ message: 'Add CSV to ZIP', merchantName, invoiceNo });
      archive.add(`${invoiceNo} detail.xlsx`, pdInvoiceExcelBuffer);
      pdInvoiceExcelBuffer = undefined; // NOSONAR

      invoiceLogger.info({ message: 'Create ZIP buffer', merchantName, invoiceNo });
      let archiveBuffer = await archive.toBuffer();
      archive = undefined; // NOSONAR

      invoiceLogger.info({ message: 'Send merchant email', merchantName, invoiceNo });
      await this.sendMerchantEmail(merchant, invoiceNo, archiveBuffer, merchantName);

      invoiceLogger.info({ message: 'Upload to blob', merchantName, invoiceNo });
      await AzureBlobStorage.uploadInvoice(archiveBuffer, invoiceNo);
      archiveBuffer = undefined; // NOSONAR

      invoiceLogger.info({ message: 'Insert invoice', merchantName, invoiceNo });
      await InvoicingServices.insertInvoice(dataForPDF);

      invoiceLogger.info({
        message: 'Update surcharges in the background',
        merchantName,
        invoiceNo,
      });
      SurchargeService.updateInvoicedSurchargeStatus(exSurcharges, invoiceNo);

      invoiceLogger.info({
        message: 'Queue parcel invoice statuses update',
        merchantName,
        invoiceNo,
      });
      PdInvoiceService.updateParcelInvoiceStatus(successParcels, dataForPDF);

      return {
        merchantName,
        invoiceNo,
        parcels: successParcels.length,
        calculateErrors,
        hawbs: successHawbs.length,
        errorHawbs,
      };
    } catch (error) {
      invoiceLogger.error({ message: 'End PD exception', merchantName, error });

      return {
        merchantName,
        message: error.message || error,
      };
    }
  },

  async sendMailCheckInvoiceToMerchant(listParcelMissInvoice) {
    try {
      if (listParcelMissInvoice.length === 0) {
        invoiceCheckLogger.info({ message: 'Start sent email check invoice' });
        await this.sentCheckMailToMerchant();
        invoiceCheckLogger.info({ message: 'Finish sent email check invoice to merchant' });
      } else {
        invoiceCheckLogger.info({ message: 'Start sent email check invoice' });
        const invoiceCheckCsv = PdInvoiceService.generateInvoiceCheckCsv(listParcelMissInvoice);

        await this.sentCheckMailToMerchant(invoiceCheckCsv, listParcelMissInvoice.length);
        invoiceCheckLogger.info({ message: 'Finish sent email check invoice' });

        const updatedParcels = await Promise.allSettled(
          listParcelMissInvoice.map((parcel) => {
            return parcel.type === '1'
              ? Daos.manifest_items.updateItemResolveConflict({
                  id: parcel.id,
                  PD_invoicing_status: [
                    {
                      status: ENUM.invoiceStatus.PD_ready_to_invoice,
                      timestamp: new Date(),
                    },
                  ],
                })
              : Daos.manifest_items.updateItemResolveConflict({
                  id: parcel.id,
                  PD_invoicing_status: [
                    {
                      status: ENUM.invoiceStatus.PD_ready_to_invoice,
                      timestamp: new Date(),
                    },
                  ],
                  DT_invoicing_status: [
                    {
                      status: ENUM.invoiceStatus.DT_ready_to_invoice,
                      timestamp: new Date(),
                    },
                  ],
                });
          }),
        );

        for (const [index, parcel] of updatedParcels.entries()) {
          if (parcel.status === 'rejected') {
            invoiceCheckLogger.error({
              message: 'Update invoice status failed',
              parcelId: listParcelMissInvoice[index].id,
            });
          } else if (parcel.status === 'fulfilled' && parcel.value.parent_id) {
            AzureStorageQueue.sendBase64Message(
              CONSTANTS.AZURE_STORAGE_QUEUE_NAME.PARCELS_UPDATE_PARENT_SPLIT_STATUS,
              { parentId: parcel.value.parent_id },
            ).catch((error) => {
              invoiceCheckLogger.error({
                message: 'Queue update parent parcel split status failed',
                parcelId: parcel.value.id,
                error,
              });
            });
          }
        }
      }
    } catch (error) {
      invoiceCheckLogger.error({ message: 'End checking invoice', error });

      return {
        message: error.message || error,
      };
    }
  },

  async enableMerchantInAzure(email: string, password: string, portal: string) {
    const { merchantPortalUrl, lspPortalUrl } = config;
    let html = '';
    let subject = '';

    if (portal === ENUM.portals.merchantPortal) {
      subject = "Welcome to Parxl's Merchant Portal!";
      html = `
      Hey there! Get ready to unbox opportunities with Parxl by clicking on this 
        <a href=${merchantPortalUrl}>Merchant Portal Link</a> for your very first login!
      <br><br>
      Here is your password: ${password}
      <br><br>
      Questions? You can reach <NAME_EMAIL> - we're always available and happy to help.
      <br><br>
      All the best,
      <br>
      Your Parxl team`;
    } else if (portal === ENUM.portals.lspPortal) {
      subject = "Welcome to Parxl's LSP Portal!";
      html = `
      Hey there! Get ready to unbox opportunities with Parxl by clicking on this 
        <a href=${lspPortalUrl}>LSP Portal Link</a> for your very first login!
      <br><br>
      Here is your password: ${password}
      <br><br>
      Questions? You can reach <NAME_EMAIL> - we're always available and happy to help.
      <br><br>
      All the best,
      <br>
      Your Parxl team`;
    }

    const mailOptions = {
      from: config.sendgrid.ops_email,
      to: email,
      subject,
      html,
    };

    try {
      const response = await EmailUtils.sendEmailSendgrid(mailOptions, {
        fromFunction: 'enableMerchantInAzure',
      });

      return response?.success;
    } catch (error) {
      logger.error({ error });

      return error;
    }
  },

  async sendMailEnableAccountStatus(userName, name, userEmail, portal, isSuperUser = false) {
    let html = '';
    let subject = '';

    if (portal === ENUM.portals.merchantPortal) {
      subject = 'Your Merchant Portal account has been reactivated';
      html = `
      <div style="font-size: 15px;">
        Hi <b>${userName}</b>, 
        <br><br> 
        Your Merchant Portal account for <b>${name}</b> has been reactivated.
        <br>
        Questions? You can reach <NAME_EMAIL> or contact your account manager - we're always available and happy to help. 
        <br><br> 
        Your Parxl team
        <br><br> 
        <i style="opacity: 0.6; font-size: 13px;">This is an auto-generated email. Please do not reply as it will not be received.</i>
      </div>`;
    } else if (portal === ENUM.portals.lspPortal) {
      const account = isSuperUser ? '' : `for <b>${name}</b>`;
      subject = 'Your LSP Portal account has been reactivated';
      html = `
      <div style="font-size: 15px;">
        Hi <b>${userName}</b>, 
        <br><br> 
        Your LSP Portal account ${account} has been reactivated.
        <br>
        Questions? You can reach <NAME_EMAIL> or contact your account manager - we're always available and happy to help. 
        <br><br> 
        Your Parxl team
        <br><br> 
        <i style="opacity: 0.6; font-size: 13px;">This is an auto-generated email. Please do not reply as it will not be received.</i>
      </div>`;
    }

    const mail_op = {
      from:
        portal === ENUM.portals.merchantPortal
          ? config.sendgrid.api_email
          : config.sendgrid.ops_email,
      to: userEmail,
      cc: config.sendgrid.api_cc_email || '',
      subject,
      html,
    };

    try {
      const response = await EmailUtils.sendEmailSendgrid(mail_op, {
        fromFunction: 'sendMailEnableAccountStatus',
      });

      return response?.success;
    } catch (error) {
      logger.error({ error });

      return error;
    }
  },

  /** *
   * Send an activation email to driver
   * <AUTHOR>
   * @param email driver email
   * @param password password generated for the driver
   * @returns {Promise<void>}
   */
  sendDriverEmail(email, password) {
    const html = `
      Welcome to Parxl's first mile delivery portal!
      <br><br>
      Please proceed to set up your account by proceeding to this portal:
      <br><a href=${config.fmdPortalUrl}>${config.fmdPortalUrl}</a>
      <br><br>
      Here is your password: ${password}
      <br><br>
      Please feel free to contact <NAME_EMAIL> if you have any queries!
      <br>
      Your Parxl team`;

    const sender = config.sendgrid.ops_email;
    const mailOptions = {
      from: sender,
      to: email,
      subject: 'Parxl First Mile Delivery Portal Account Activation',
      html,
    };
    EmailUtils.sendEmailSendgrid(mailOptions, { fromFunction: 'send FMDDriver email' });
  },

  async sendPart2Email(
    customBroker,
    mawb: IMawbCreated,
    flight_no,
    local_sta,
    local_std,
    req,
    allParcels,
    zipBuffer,
    zoneName,
    gaylords,
    CHWeight,
    stastdPieces,
    destination,
    lmd,
  ) {
    let flightInfo = '';
    const mawbNo = mawb._partitionKey;

    if (local_std && local_sta) {
      flightInfo = `<p>-STD: ${local_std}</p> <p>-STA: ${local_sta}</p>`;
    }

    const dateObj = new Date();
    const month = dateObj.getUTCMonth() + 1;
    const day = dateObj.getUTCDate();
    const year = dateObj.getUTCFullYear();
    let mailDate;
    let mailSubject;
    let mailTo = customBroker.email;
    let mailCC = config.sendgrid.kerry_cc_email;
    let htmlTemplate = `
    <p>Dear Sir/Mdm,</p>
    <p>We are SIA Cargo, our MAWB no. ${mawbNo} will arrive at your warehouse, 
    please find more details as follows: </p>
    <p>-Flight No: ${flight_no}</p>
    ${flightInfo}
    <p>- MAWB: ${mawbNo}</p>
    <p>- No. Carton Boxes: ${req.body.total_pieces}</p>
    <p>- Parcels: ${allParcels.length}</p>
    <p>- Total Weight: ${req.body.total_weight} kg</p> `;
    const route = `(${allParcels[0].origin} to ${customBroker.discharge_point})`;

    switch (customBroker.country) {
      case 'CA': {
        if (customBroker.custom_broker_code === CONSTANTS.CUSTOM_BROKER_CODES.CB_FBE_YYZ) {
          const dateStr = DateUtils.format('MMM/DD/YYYY', year, month, day);
          const latestFlight = mawb.second_sector || mawb.first_sector;
          const routing = `${allParcels[0].origin} - ${mawb.point_of_discharge}`;

          mailSubject = `PRE-ALERT, PARXL ${dateStr}, ${routing}, ${latestFlight.flight_number}, ${mawbNo}`;
          mailTo = customBroker.email;
          mailCC = config.sendgrid.ops_email;

          const flightDate = new Date(latestFlight.flight_date);
          const formattedFlightDate = DateUtils.format(
            'DD MMM YYYY',
            flightDate.getFullYear(),
            flightDate.getMonth() + 1,
            flightDate.getDate(),
          );

          htmlTemplate = `
            <p>Dear Sir/Madam,</p>
            <br />
            <p>Please find the pre-alert information for the incoming MAWB:</p>
            <br />
            <table style="border-collapse: collapse" cellpadding="5" border="1">
              <tr style="background-color: #f4f5f7">
                <th width="130"></th>
                <th width="130"><strong>Shipment Details</strong></th>
              </tr>
              <tr>
                <td>MAWB #</td>
                <td>${mawbNo}</td>
              </tr>
              <tr>
                <td>Flight # & Flight Date</td>
                <td>${latestFlight.flight_number} ${formattedFlightDate}</td>
              </tr>
              <tr>
                <td>Number of bags/skids</td>
                <td>${gaylords.length}</td>
              </tr>
              <tr>
                <td>Number of HAWBs</td>
                <td>${allParcels.length}</td>
              </tr>
              <tr>
                <td>Total Weight in KGs</td>
                <td>${req.body.total_weight} KG</td>
              </tr>
            </table>
            <br />
            <p>Please find attached documents for your reference.</p>
            <br />
            <p>Regards,</p>
            <p>Parxl Team</p>
          `;
        }

        break;
      }

      case 'HK': {
        mailDate = `${year}/${month}/${day}`;
        // eslint-disable-next-line max-len
        mailSubject = `[PART 2] Pre-alert MAWB ${mawbNo} - DF ex ${allParcels[0].origin} to HKG - [${req.body.total_weight}kg] - (${mailDate})`;
        break;
      }

      case 'GB': {
        mailDate = `${new Date().toISOString().slice(0, -5)}+00:00`;
        mailSubject = `[PART 2] SQ (Parxl) Pre-Alert for Custom Clearance ${mailDate} [${mawbNo}] ${route}`;
        break;
      }

      case 'MY': {
        mailDate = `${year}-${month}-${day}`;
        mailSubject = `[PART 2][Parxl][${allParcels[0].origin_country}-MY] Pre-Alert ${mawbNo} ${mailDate}`;
        mailTo = customBroker.email;
        mailCC = config.sendgrid.my_cc_email;

        htmlTemplate = zoneName
          ? `
        <p>Dear Sir/Mdm,</p>
        <p>We are SIA Cargo, our MAWB no. ${mawbNo} for ${zoneName} Malaysia will arrive at your warehouse, 
        please find more details as follows: </p>
        <p>- Flight No: ${flight_no}</p>
        ${flightInfo}
        <p>- MAWB: ${mawbNo}</p>
        <p>- No. of Parcels into East: ${req.body.noOfEast}</p>
        <p>- Total Weight (East): ${req.body.eastTotalWeight} kg</p>
        <p>- No. of Parcels into West: ${req.body.noOfWest}</p>
        <p>- Total Weight (West): ${req.body.westTotalWeight} kg</p> 
        <p>The parcels are also consolidated into East and West Malaysia. Please check the labels indicated.</p> `
          : `
        <p>Dear Sir/Mdm,</p>
        <p>We are SIA Cargo, our MAWB no. ${mawbNo} will arrive at your warehouse, 
        please find more details as follows: </p>
        <p>- Flight No: ${flight_no}</p>
        ${flightInfo}
        <p>- MAWB: ${mawbNo}</p>
        <p>- No. of Parcels: ${allParcels.length}</p>
        <p>- No. of bags/boxes: ${req.body.pieces}</p>
        <p>- Total Weight: ${req.body.totalWeight} kg</p>
        <p>The parcels are also consolidated into East and West Malaysia. Please check the labels indicated.</p> `;

        break;
      }

      case 'AU': {
        if (allParcels[0].lmd === ENUM.lmdNames.CouriersPlease) {
          mailTo = customBroker.email;
          mailSubject = `SQ Parxl Custom Pre-Alert ${mawbNo} (${allParcels[0].origin} - ${customBroker.discharge_point})`;
          mailCC = '';
          htmlTemplate = `
      <p>Dear Sir/Madam</p>
      <p>Please find below the details for the upcoming shipments:</p>
      <table style="border-collapse: collapse" cellpadding="5" border="1" >
      <tr style="background-color: #f4f5f7">
        <th width="130"></th>
        <th width="130"><strong>Shipment Details</strong></th>
      </tr>
      <tr>
        <td>Destination</td>
        <td> ${customBroker.discharge_point}</td>
      </tr>
      <tr>
        <td>MAWB</td>
        <td>${mawbNo}</td>
      </tr>
      <tr>
        <td>Total Pieces</td>
        <td>${gaylords.length}</td>
      </tr>
      <tr>
        <td>Total CH Weight</td>
        <td>${CHWeight || ''}</td>
      </tr>
      <tr>
        <td>Total HAWB</td>
        <td>${allParcels.length}</td>
      </tr>
      </table>
      <p>Attached are the Manifest document and MAWB copy.</p>
      <p>Regards,</p>
      <p>Parxl</p>
      `;
        }

        if (allParcels[0].lmd === ENUM.lmdNames.AusPost) {
          mailTo = customBroker.email;
          mailCC = config.sendgrid.ops_email;
          mailSubject = `[Part 2] SQ Parxl Customs Pre-Alert ${mawbNo} ${route}`;
          htmlTemplate = `
        <p>Dear Sir/Mdm,</p>
        <p>We are SIA Cargo, our MAWB no. ${mawbNo} will arrive at your warehouse,
        please find more details as follows: </p>
        <p>- Flight No: ${flight_no}</p>
        ${flightInfo}
        <p>- MAWB: ${mawbNo}</p>
        <p>- No. Carton Boxes: ${req.body.total_pieces}</p>
        <p>- Parcels: ${allParcels.length}</p>
        <p>- Total Weight: ${req.body.total_weight} kg</p> `;
        }

        break;
      }

      case 'PH':

      case 'TW': {
        const originCountry = CountryISOService.getCountryCode3(destination.name);
        const destinationZone = customBroker.discharge_point;
        mailDate = DateUtils.format('YYYY-MM-DD', year, month, day);
        mailCC = config.sendgrid.ops_email;
        mailTo = customBroker.email;
        // eslint-disable-next-line max-len
        mailSubject = `[Parxl][${originCountry}-${destinationZone}] Final Pre-Alert [${mawbNo}][${mailDate}] ${route}`;
        htmlTemplate = `
    <p>Dear Sir/Mdm,</p>

    <p>We are SIA Cargo, our MAWB no. ${mawbNo} will arrive at your warehouse, please find more details as follows:</p>
    
    <p>- Flight No: ${flight_no}</p>
    <p>- STD: ${local_std}</p>
    <p>- STA: ${local_sta}</p>
    <p>- MAWB: ${mawbNo}</p>
    <p>- No. of Parcels: ${allParcels.length}</p>
    <p>- No. of bags: ${stastdPieces}</p>
    <p>- Total Weight (kg): ${req.body.total_weight}</p>
    `;
        break;
      }

      default: {
        mailTo = customBroker.email;
        mailCC = config.sendgrid.ops_email;
        mailSubject = `[Part 2] SQ Parxl Customs Pre-Alert ${mawbNo} ${route}`;
        break;
      }
    }

    let mailOptions;

    if (lmd.lmd_provider_name === ENUM.lmdNames.LMG) {
      mailOptions = await preAlertEmailUtils.generatePreAlertMailTemplateForLMG(
        PreAlertEmailPart.PART1,
        mawb,
        customBroker,
        local_sta,
        local_std,
        req.body.total_weight,
        zipBuffer,
      );
    } else {
      const attachmentFilename =
        customBroker.custom_broker_code === CONSTANTS.CUSTOM_BROKER_CODES.CB_FBE_YYZ
          ? `CB_FBE_YYZ_PreAlert_${mawbNo}_${new Date().toISOString().slice(0, 10)}.zip`
          : `Attachment_${new Date()}.zip`;

      mailOptions = {
        from: config.sendgrid.ops_email,
        to: mailTo,
        cc: mailCC,
        subject: mailSubject,
        html: htmlTemplate,
        attachments: [
          {
            filename: attachmentFilename,
            content: zipBuffer,
          },
        ],
      };
    }

    await EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: 'send preAlertPart2Email',
      mawbNo,
    });
  },

  /**
   * Send email with archive to merchant
   * @param {object} merchant
   * @param {string} invoiceNo
   * @param {zip} archive
   * @param {string} merchantName
   */
  async sendMerchantEmail(merchant, invoiceNo, archive, merchantName) {
    const merchantEmails = merchant.invoice_emails;
    const mailOptions = {
      from: config.sendgrid.api_email,
      to: merchantEmails,
      cc: config.sendgrid.cc_email,
      subject: `Parcel Delivery Invoice No. ${invoiceNo} from Parxl`,
      html: `
        <p>Dear Sir/Mdm,</p>
        <p>Please find attached parcel delivery invoice generated for your necessary attention.</p>
        <p>Please remit payment at your earliest convenience.</p>
        <p>This is a system generated email from Parxl. No reply is required.</p>
        `,
      attachments: [
        {
          filename: `${invoiceNo}_${new Date()}.zip`,
          content: archive.toString('base64'),
        },
      ],
    };

    EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: 'send invoice to merchant',
      merchantName,
    });
  },

  async sentCheckMailToMerchant(csvFile?: any, numberOfAffectedParcel?: any) {
    if (csvFile) {
      const archive = new JSZipUtils();
      archive.add('parcels patched.csv', Buffer.from(csvFile));
      const buffer = await archive.toBuffer();
      const data = buffer.toString('base64');
      const mailOptions = {
        from: config.sendgrid.tech_email,
        to:
          config.appEnv === 'UAT'
            ? config.sendgrid.parxl_customs_test_email
            : `${config.sendgrid.tech_email}; ${config.sendgrid.api_email}`,
        subject:
          config.appEnv === 'UAT'
            ? '[UAT] List of additional parcels to be invoiced in its next run'
            : 'List of additional parcels to be invoiced in its next run',
        html: `
          <p>Hi Team,</p>
          <p>There is/are a total of ${numberOfAffectedParcel} parcels that has/have been processed and moved
          for delivery, but without invoice statuses. The system has added pd_ready_to_invoice and/or 
          dt_ready_to_invoice for next month's invoicing accordingly. Please see attached for the summary.</p>        
          <table style="border: 1px solid; border-spacing: 0; border-collapse: collapse;">
            <tr style="background-color: #deddd9;">
                <th style="text-align: center; border: 1px solid; padding: 5px;">Scenario</th>
                <th style="text-align: center; border: 1px solid; padding: 5px;">What System Checks</th>
                <th style="text-align: center; border: 1px solid; padding: 5px;">What system fix</th>
            </tr>
            <tr>
                <td style="border: 1px solid; padding: 5px;">1</td>
                <td style="border: 1px solid; padding: 5px;">Parcels closed within container, but no
                PD_READY_TO_INVOICE status</td>
                <td style="border: 1px solid; padding: 5px;">add pd_ready_to_invoice datetime</td>
            </tr>
            <tr>
                <td style="border: 1px solid; padding: 5px;">2</td>
                <td style="border: 1px solid; padding: 5px;">Parcels have statuses that are NOT "BOOKED" / "CANCELLED"
                  / "BOOKING_RECEIVED" / "EXPIRED" / "BOOKING_REJECTED" / "PICKED_UP" / "PACKED_TO_GAYLORD" / "SORTED"
                  / "RECEIVED_AT_WAREHOUSE" / "HELD_AT_WAREHOUSE" and no PD_READY_TO_INVOICE status
                </td>
                <td style="border: 1px solid; padding: 5px;">add pd_ready_to_invoice datetime<br>
                add dt_ready_to_invoice datetime</td>
            </tr>
          </table>
          <p><i style="opacity: 0.6; font-size: 13px;">This is a system-generated email. Please do not reply.</i></p> 
          `,
        attachments: [
          {
            filename: `parcels_${new Date()}.zip`,
            content: data,
          },
        ],
      };
      await EmailUtils.sendEmailSendgrid(mailOptions, { fromFunction: 'sentCheckMailToMerchant' });
    } else {
      const mailOptions = {
        from: config.sendgrid.tech_email,
        to:
          config.appEnv === 'UAT'
            ? config.sendgrid.parxl_customs_test_email
            : `${config.sendgrid.tech_email}; ${config.sendgrid.api_email}`,
        subject:
          config.appEnv === 'UAT'
            ? '[UAT] No additional parcels to be invoiced in its next run'
            : 'No additional parcels to be invoiced in its next run',
        html: `
          <p>Hi Team,</p>
          <p>There was no parcels found to be affected based on the below scenarios. 
            No system patch was done.
          </p>        
          <table style="border: 1px solid; border-spacing: 0; border-collapse: collapse;">
            <tr style="background-color: #deddd9;">
                <th style="text-align: center; border: 1px solid; padding: 5px;">Scenario</th>
                <th style="text-align: center; border: 1px solid; padding: 5px;">What System Checks</th>
                <th style="text-align: center; border: 1px solid; padding: 5px;">What system fix</th>
            </tr>
            <tr>
                <td style="border: 1px solid; padding: 5px;">1</td>
                <td style="border: 1px solid; padding: 5px;">Parcels closed within container, but no
                PD_READY_TO_INVOICE status</td>
                <td style="border: 1px solid; padding: 5px;">add pd_ready_to_invoice datetime</td>
            </tr>
            <tr>
                <td style="border: 1px solid; padding: 5px;">2</td>
                <td style="border: 1px solid; padding: 5px;">Parcels have statuses that are NOT "BOOKED" / "CANCELLED"
                  / "BOOKING_RECEIVED" / "EXPIRED" / "BOOKING_REJECTED" / "PICKED_UP" / "PACKED_TO_GAYLORD" / "SORTED"
                  / "RECEIVED_AT_WAREHOUSE" / "HELD_AT_WAREHOUSE" and no PD_READY_TO_INVOICE status
                </td>
                <td style="border: 1px solid; padding: 5px;">add pd_ready_to_invoice datetime<br>
                add dt_ready_to_invoice datetime</td>
            </tr>
          </table>
          <p><i style="opacity: 0.6; font-size: 13px;">This is a system-generated email. Please do not reply.</i></p> 
          `,
      };
      await EmailUtils.sendEmailSendgrid(mailOptions, { fromFunction: 'sentCheckMailToMerchant' });
    }
  },

  async sentMailForApproverDTManualUpload(
    approverEmail: string,
    approverName: string,
    submitterName: string,
  ) {
    const mailOptions = {
      from: config.sendgrid.api_email,
      to: approverEmail,
      subject: 'PLS Notification - Manual Duty and Tax Request for Approval',
      html: `
        <p>Dear ${approverName},</p>
        <p>${submitterName} has submitted Manual Duty and Tax for your review and action.</p>
        <i>This is a system notification, please do not reply to this email.</i>

        <p>Best regards,</p>
        <p>Parxl Team</p>
      `,
    };

    await EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: 'sentMailForApproverDTManualUpload',
    });
  },

  async sentMailInformDTManualApproved(
    submitterEmail: string,
    submitterName: string,
    approverName: string,
    parcelList: any,
  ) {
    const mailOptions: any = {
      from: config.sendgrid.api_email,
      to: submitterEmail,
      subject: 'PLS Notification - Manual Duty and Tax Approved',
      html: `
        <p>Dear ${submitterName},</p>
        <p>${approverName} has approved your Manual Duty and Tax Submission. ${parcelList ? 'Please see list of parcels that cannot be updated as it has been invoiced already' : ''}.</p>
        <i>This is a system notification, please do not reply to this email.</i>

        <p>Best regards,</p>
        <p>Parxl Team</p>
      `,
    };

    if (parcelList) {
      mailOptions.attachments = [
        {
          filename: 'Invoiced Parcels.csv',
          content: Buffer.from(parcelList).toString('base64'),
        },
      ];
    }

    await EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: 'sentMailInformDTManualApproved',
    });
  },

  async sentMailInformDTManualRejected(
    submitterEmail: string,
    submitterName: string,
    approverName: string,
  ) {
    const mailOptions = {
      from: config.sendgrid.api_email,
      to: submitterEmail,
      subject: 'PLS Notification - Manual Duty and Tax Rejected',
      html: `
        <p>Dear ${submitterName},</p>
        <p>${approverName} has rejected your Manual Duty and Tax Submission.</p>
        <i>This is a system notification, please do not reply to this email.</i>

        <p>Best regards,</p>
        <p>Parxl Team</p>
      `,
    };

    await EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: 'sentMailInformDTManualRejected',
    });
  },

  async sentMailForApproverSurchargeUpload(
    approverEmail: string,
    approverName: string,
    submitterName: string,
  ) {
    const mailOptions = {
      from: config.sendgrid.api_email,
      to: approverEmail,
      subject: 'PLS Notification - Request for Approval for Surcharges',
      html: `
        <p>Dear ${approverName},</p>
        <p>${submitterName} has submitted surcharges for your review and action.</p>
        <i>This is a system notification, please do not reply to this email.</i>

        <p>Best regards,</p>
        <p>Parxl Team</p>
      `,
    };

    await EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: 'sentMailForApproverSurchargeUpload',
    });
  },

  // use for lsp-portal dims weight upload
  async sendParcelsDimsWeightUpdateResult(userEmail: string, userName: string, errorFile?: Buffer) {
    const opsEmail = config.sendgrid.ops_email;
    const mailOption = {
      from: opsEmail,
      to: userEmail,
      cc: opsEmail,
      subject: 'Parcel values bulk update report',
      html: errorFile
        ? `<p>Dear ${userName}</p></br><p>Some of your updates have errors. Please see attached file for failed bookings and try to upload them again.</p>`
        : `<p>Dear ${userName}</p></br><p>Your shipment updates have all been successfully processed.</p>`,

      attachments: errorFile && [
        {
          filename: 'failed_updating_parcels_dimension.csv',
          content: Buffer.from(errorFile).toString('base64'),
        },
      ],
    };

    try {
      await EmailUtils.sendEmailSendgrid(mailOption, {
        fromFunction: 'sendParcelsDimsWeightUpdateResult',
      });
    } catch (error) {
      logger.error({ error });
    }
  },

  async sentCommercialInvoiceEmail(
    userEmails: string[],
    item: any,
    recipientType: 'commercial_invoice' | 'ops_hub' = 'ops_hub',
  ): Promise<void> {
    try {
      const { zipFile, destination, merchantName, sequence, merchantAcc, originCountry, mawbId } =
        item;
      const contentAttachment = await zipFile.toBuffer();

      const contentResult =
        originCountry === 'MY'
          ? EmailUtils.getEmailContentForMawb(mawbId, destination, originCountry)
          : EmailUtils.getEmailContentForMerchant(merchantName, sequence, merchantAcc, destination);

      const { emailContent, filename } = contentResult;
      const attachments = [
        {
          filename,
          content: contentAttachment,
          type: 'application/zip',
          disposition: 'attachment',
        },
      ];

      let allRecipients = [...userEmails];

      if (recipientType === 'commercial_invoice') {
        try {
          const encryptedMerchantName = AesUtils.CrtCounterEncrypt(merchantName);

          const merchants = await MerchantService.getMerchantByName(encryptedMerchantName, false);

          const merchant =
            Array.isArray(merchants) && merchants.length > 0 ? merchants[0] : undefined;

          if (
            merchant &&
            'commercial_invoice_emails' in merchant &&
            Array.isArray(merchant.commercial_invoice_emails) &&
            merchant.commercial_invoice_emails.length > 0
          ) {
            allRecipients = [...allRecipients, ...merchant.commercial_invoice_emails];

            allRecipients = [...new Set(allRecipients)];

            invoiceLogger.info({
              function: 'email.service > sentCommercialInvoiceEmail',
              message: `Added ${merchant.commercial_invoice_emails.length} commercial invoice emails for merchant`,
              merchantName,
              recipientType,
            });
          }
        } catch (error) {
          invoiceLogger.error({
            function: 'email.service > sentCommercialInvoiceEmail',
            message: 'Failed to get merchant commercial invoice emails',
            merchantName,
            recipientType,
            error: error.message,
          });
        }
      }

      const mailOptions = {
        from: config.sendgrid.ops_email,
        to: allRecipients.join(';'),
        subject: emailContent.subject,
        html: emailContent.html,
        attachments,
      };

      await EmailUtils.sendEmailSendgrid(mailOptions, { fromFunction: 'sentCheckMailToMerchant' });

      invoiceLogger.info({
        function: 'email.service > sentCommercialInvoiceEmail',
        message: `sent email from ${config.sendgrid.ops_email} to ${allRecipients.join(',')}`,
        recipientType,
        merchantName,
      });
    } catch (error) {
      invoiceLogger.error({
        function: 'email.service > sentCommercialInvoiceEmail',
        message: 'Send Commercial Invoice Email error',
        recipientType,
        error: error.message,
      });
    }
  },

  async sendSAPMail(
    bufferData: any,
    arZipFileName: string,
    errString: string,
    ctlZipFileBuffer: any,
    ctlZipFileName: string,
  ) {
    const dateStringForSubject = DateUtils.utcToStr('DD-MMM-YYYY');
    const subject = `AR File Transfer Success ${dateStringForSubject}`;
    const errStrforMail = errString
      ? `<p>The following error occurred when generating the AR file:</p> ${errString}`
      : '';
    const html =
      `<p>Dear Parxl Admin,</p>
                      <p>AR file transfer has been completed successfully. Please find AR file attached.</p> ` +
      `${errStrforMail}`;
    const mailOps = {
      from: config.sendgrid.tech_email,
      to: config.sendgrid.tech_email,
      subject,
      html,
      attachments: [
        {
          filename: arZipFileName,
          content: bufferData.toString('base64'),
        },
      ],
    };

    if (ctlZipFileName) {
      mailOps.attachments.push({
        filename: ctlZipFileName,
        content: ctlZipFileBuffer.toString('base64'),
      });
    }

    await EmailUtils.sendEmailSendgrid(mailOps);
  },

  async sendMailInformSurchargeRejected(
    submitterEmail: string,
    submitterName: string,
    approverName: string,
  ) {
    const mailOptions = {
      from: config.sendgrid.api_email,
      to: AesUtils.CrtCounterDecrypt(submitterEmail),
      subject: 'PLS Notification - Rejected submission for Surcharges',
      html: `
        <p>Dear ${AesUtils.CrtCounterDecrypt(submitterName)},</p>
        <p>${AesUtils.CrtCounterDecrypt(approverName)} has rejected your submission for surcharges under 'Raise Surcharge' page.</p>
        <i>This is a system notification, please do not reply to this email.</i>

        <p>Best regards,</p>
        <p>Parxl Team</p>
      `,
    };

    await EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: this.sendMailInformSurchargeRejected.name,
    });
  },

  async sendMailInformSurchargeApproved(
    submitterEmail: string,
    submitterName: string,
    approverName: string,
  ) {
    const mailOptions = {
      from: config.sendgrid.api_email,
      to: AesUtils.CrtCounterDecrypt(submitterEmail),
      subject: 'PLS Notification - Approved submission for Surcharges',
      html: `
        <p>Dear ${AesUtils.CrtCounterDecrypt(submitterName)},</p>
        <p>${AesUtils.CrtCounterDecrypt(approverName)} has approved your submission.</p>
        <i>This is a system notification, please do not reply to this email.</i>

        <p>Best regards,</p>
        <p>Parxl Team</p>
      `,
    };

    await EmailUtils.sendEmailSendgrid(mailOptions, {
      fromFunction: this.sendMailInformSurchargeApproved.name,
    });
  },
};
