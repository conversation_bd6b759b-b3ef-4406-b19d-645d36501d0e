const MerchantControllerTest = require('../../../merchant/controller/merchant_controller');
const RateMerchantServices = require("../../../merchant/services/rate-merchant-services");
const BookingService = require('../../../merchant/services/booking/booking-service');
const ReportUtils = require('../../../merchant/utilities/reportUtils');
const formidable = require('formidable');
const TrackingNoService = require('../../../merchant/services/tracking-no-services');
const AzureBlobStorage = require('../../../merchant/utilities/azure-blob-storage');
const aesUtils = require('../../../merchant/models/aesUtils');
const manifestItemService = require('../../../merchant/services/manifest-item-services');
const NotificationService = require('../../../merchant/services/notification-service');
const CollectionPointService = require('../../../merchant/services/collection_point_service');
const downloadLabelService = require('../../../merchant/services/download-labels-service');

jest.mock("../../../merchant/utilities/logUtils", () => ({
  error: jest.fn(),
  info: jest.fn()
}));

formidable.IncomingForm = jest.fn().mockImplementation(() => {
  return {parse: jest.fn().mockImplementation((req, cb) => {
    cb(null, {}, {file: {path: ""}})
  })}
});

TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue(1);

beforeEach(() => {
  jest.clearAllMocks();
});

afterEach(() => {
  jest.clearAllMocks();
});

console.log = jest.fn();
console.error = jest.fn();
const mockDate = new Date('2024-12-17T02:17:19.229Z');
global.Date = jest.fn(() => mockDate);

const merchant_controller_test = new MerchantControllerTest();
describe("Test MerchantControllerTest", () => {
  test('validateMerchantName', async () => {
    try {
    const merchant = [{
      tracking_no: "SQGB00119K000D69",
      merchant_name: 'merchantName',
      merchant_account_number: '63ff0e5c7c',
    }];
    RateMerchantServices.validateRateMerchantByTime = jest.fn(() => Promise.resolve());

    merchant_controller_test.merchantDao = {
      find: jest.fn((option, cb) => {
        cb(null, merchant)
      })
    };
    await merchant_controller_test.validateMerchantName('merchantName');
    expect(RateMerchantServices.validateRateMerchantByTime.mock.calls[0][0]).toBe('63ff0e5c7c');
    expect(RateMerchantServices.validateRateMerchantByTime).toHaveBeenCalled();
    } catch (err) {

    }
  });
});

const res = {
  json: jest.fn(),
  status: jest.fn()
};

describe("Test login", () => {

  const request = {
    decoded: {
    emails: jest.fn()
    }
  };
  res.status.mockReturnValue({
    send: jest.fn()
  });
  const query_mock = [
    {
    merchant_name: 'merchantName',
    name: 'account name'
    }
  ];
  test("Case: login successful", async () => {
    //Arrange
    merchant_controller_test.merchantDao.find = jest.fn().mockReturnValue(query_mock);
    //Act
    await merchant_controller_test.login(request, res);
    //Assert
    expect(res.status.send).toEqual(undefined)
  });
  test("Case: login error", async () => {
    //Arrange
    merchant_controller_test.merchantDao.find = jest.fn().mockRejectedValue('error');
    //Act
    await merchant_controller_test.login(request, res);
    //Assert
    expect(res.status.send).toEqual(undefined)
  })
});

describe('test makeMultiBooking function', () => {
  const request = {
    merchant: {
      merchant_name: 'testMerchant'
    },
    query: {
    validateOnly: 'true'
    },
    decoded: {
    emails: ['<EMAIL>']
    }
  };
  const requestOfShopify = {
    query: {
      validateOnly: 'true'
      },
    body: {
      booking_platform: 'SHOPIFY'
    }
  }
  const response = {json: jest.fn(), setTimeout: jest.fn()};
  const parcels = [
    {id: "1", PLS_batch_no: 'batch1'},
    {id: "2", PLS_batch_no: 'batch2'}
  ];

  test('should return valid parcels', async () => {
    let expResult = {
    validatedParcels: parcels,
    rejectedParcels: [],
    success: true,
    };
    merchant_controller_test.getParcels = jest.fn().mockResolvedValue(parcels);
    BookingService.validateMultiParcels = jest.fn().mockResolvedValue([[], parcels]);

    await merchant_controller_test.makeMultiBooking(request, response);

    expect(response.json).toHaveBeenCalledWith(expResult);
  });

  test('should return valid parcels (shopify booking)', async () => {
    let expResult = {
    validatedParcels: parcels,
    rejectedParcels: [],
    success: true,
    };
    merchant_controller_test.getParcels = jest.fn().mockResolvedValue(parcels);
    BookingService.validateMultiParcels = jest.fn().mockResolvedValue([[], parcels]);

    await merchant_controller_test.makeMultiBooking(requestOfShopify, response);

    expect(response.json).toHaveBeenCalledWith(expResult);
  });

  test('should return invalid parcels', async () => {
    const expResult = {
    validatedParcels: [],
    rejectedParcels: parcels,
    success: true,
    };
    merchant_controller_test.getParcels = jest.fn().mockResolvedValue(parcels);
    BookingService.validateMultiParcels = jest.fn().mockResolvedValue([parcels, []]);
    BookingService.createRejectedShipments = jest.fn();

    await merchant_controller_test.makeMultiBooking(request, response);

    expect(response.json).toHaveBeenCalledWith(expResult);
  });

  test('should return booking result', async () => {
    let expResult = {
    success: true,
    bookedParcels: parcels,
    rejectedParcels: []
    };
    merchant_controller_test.getParcels = jest.fn().mockResolvedValue(parcels);
    ReportUtils.exportBookingResultToCSV = jest.fn().mockReturnValue({});
    BookingService.validateMultiParcels = jest.fn().mockResolvedValue([[], parcels]);
    TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue(1);
    BookingService.orderMultiParcels = jest.fn().mockResolvedValue([[], parcels]);

    await merchant_controller_test.makeMultiBooking({...request, query: {}}, response);

    expect(response.json).toHaveBeenCalledWith(expResult);
  });


  test('test makeSingleBooking fail', async () => {
    // Arrange
    const req = 
    { 
      merchant: {
        name: 'name'
      }, 
      body: {
        reqParcel: {
        prop: 'prop'
        }
      }
    };

    const jsonFn = jest.fn();
    const res = {
    json: jsonFn,
    status: jest.fn().mockReturnValue({ json: jsonFn })
    };

    const validateResult = {
    hasError: jest.fn().mockImplementation(() => true),
    errors: ['error']
    }

    const parcel = {}
    BookingService.validateSingleParcel = jest.fn().mockResolvedValue([validateResult, parcel]);
    BookingService.createRejectedShipments = jest.fn()

    // Act
    const result = await merchant_controller_test.makeSingleBooking(req, res);

    // Assert
    expect(res.status).toHaveBeenCalledWith(422);
    expect(result).toEqual(undefined)
  })

  test('test makeSingleBooking fail', async () => {
    // Arrange
    const req = 
      { 
      merchant: {
        name: 'name'
      }, 
      body: {
        reqParcel: {
          prop: 'prop'
        }
      }
      };

      const jsonFn = jest.fn();
      const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
      };

      const validateResult = {
      hasError: jest.fn().mockImplementation(() => false),
      errors: ['error']
      }

      const parcel = {data: 'data'};

      BookingService.validateSingleParcel = jest.fn().mockResolvedValue([validateResult, parcel]);

      TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue('seqNo')
      BookingService.orderParcel = jest.fn().mockResolvedValue({
        httpStatusCode: 200,
        success: true,
        data: 'data'
      })
      // Act
      await merchant_controller_test.makeSingleBooking(req, res);

      // Assert
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        error: ['data']
      })
  })

  test('test makeSingleBooking pass', async () => {
    // Arrange
    const req = 
      { 
      merchant: {
        name: 'name'
      }, 
      body: {
        reqParcel: {
          prop: 'prop'
        }
      }
      };

      const jsonFn = jest.fn();
      const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
      };

      const validateResult = {
      hasError: jest.fn().mockImplementation(() => false),
      errors: ['error']
      }

      const parcel = {data: 'data'};

      BookingService.validateSingleParcel = jest.fn().mockResolvedValue([validateResult, parcel]);

      TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue('seqNo')
      BookingService.orderParcel = jest.fn().mockResolvedValue({
        httpStatusCode: 200,
        success: true,
        data: 'data'
      })
      // Act
      await merchant_controller_test.makeSingleBooking(req, res);

      // Assert
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        error: ['data']
      })
  })

  test('test makeSingleBooking B2B pass', async () => {
    // Arrange
    const req = 
    {
      merchant: {
        name: 'name'
      },
      body: {
        service_option: 'b2b'
      }
    };

      const jsonFn = jest.fn();
      const res = {
        json: jsonFn,
      };

      const validateResult = {
      hasError: jest.fn().mockImplementation(() => false),
      errors: ['error']
      }

      const parcel = {data: 'data'};

      BookingService.validateSingleParcel = jest.fn().mockResolvedValue([validateResult, parcel]);
      downloadLabelService.generateGenericLabel = jest.fn();
      TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue('seqNo')
      BookingService.orderParcel = jest.fn().mockResolvedValue('data')
      // Act
      await merchant_controller_test.makeSingleBooking(req, res);

      // Assert
      expect(downloadLabelService.generateGenericLabel).toHaveBeenCalled()
      expect(res.json).toHaveBeenCalledWith({
        data: 'data'
      })
  })

  test('test makeSingleBooking fail 1', async () => {
    // Arrange
    const req = 
      { 
      merchant: {
        name: 'name'
      }, 
      body: {
        reqParcel: {
          prop: 'prop'
        }
      }
      };

      const jsonFn = jest.fn();
      const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
      };

      const validateResult = {
      hasError: jest.fn().mockImplementation(() => false),
      errors: ['error']
      }

      const parcel = {data: 'data'};

      BookingService.validateSingleParcel = jest.fn().mockResolvedValue([validateResult, parcel]);

      TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue('seqNo')
      BookingService.orderParcel = jest.fn().mockResolvedValue({
        httpStatusCode: 200,
        success: false,
      })
      // Act
      const result = await merchant_controller_test.makeSingleBooking(req, res);

      // Assert
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: undefined
      })
      expect(result).toEqual(undefined);
  })

  test('test makeSingleBooking fail 1', async () => {
    // Arrange
    const req = 
      { 
      merchant: {
        name: 'name'
      }, 
      body: {
        reqParcel: {
          prop: 'prop'
        }
      }
      };

      const jsonFn = jest.fn();
      const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
      };

      const validateResult = {
      hasError: jest.fn().mockImplementation(() => false),
      errors: ['error']
      }

      const parcel = {data: 'data'};

      BookingService.validateSingleParcel = jest.fn().mockResolvedValue([validateResult, parcel]);

      TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue('seqNo')
      BookingService.orderParcel = jest.fn().mockResolvedValue({
        success: false,
      })
      // Act
      await merchant_controller_test.makeSingleBooking(req, res);

      // Assert
      expect(res.json).toHaveBeenCalledWith({
        data: {
          success: false
        }
      })
  })

  test('test makeSingleBooking fail 1', async () => {
    // Arrange
    const req = 
      { 
      merchant: {
        name: 'name'
      }, 
      body: {
        reqParcel: {
          prop: 'prop'
        }
      }
      };

      const jsonFn = jest.fn();
      const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
      };

      const validateResult = {
      hasError: jest.fn().mockImplementation(() => false),
      errors: ['error']
      }

      const parcel = {data: 'data'};

      BookingService.validateSingleParcel = jest.fn().mockResolvedValue([validateResult, parcel]);

      TrackingNoService.getSequenceValue = jest.fn().mockResolvedValue('seqNo')
      BookingService.orderParcel = jest.fn().mockRejectedValue({message: 'Error'})
      // Act
      await merchant_controller_test.makeSingleBooking(req, res);

      // Assert
      expect(res.status).toHaveBeenCalledWith(500)
      expect(res.json).toHaveBeenCalledWith({
        message: 'Error',
        success: false
      })
  })
});

describe('test downloadParcelsWithWeightChanges', () => {
  test('should throw error when find no file', async () => {
    // Arrange
    const req = {
      query: {
        dayOfWeightChanges: 20241010
      }
    }
    const jsonFn = jest.fn();
    const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
    }

    AzureBlobStorage.getLatestFileByTimestamp = jest.fn().mockResolvedValueOnce(undefined)

    // Act
    await merchant_controller_test.downloadParcelsWithWeightChanges(req, res)

    // Assert
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Something went wrong when download file',
      errorMessage: 'Cannot find file Presco_20241010 in blob storage!'
    })
  })

  test('should download file successfully', async () => {
    // Arrange
    const req = {
      query: {
        dayOfWeightChanges: 20241010
      }
    }
    const jsonFn = jest.fn();
    const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
    }

    AzureBlobStorage.getLatestFileByTimestamp = jest.fn().mockResolvedValue(
      ['Presco_20241010'], { type: 'application/zip' }
    );

    // Act
    await merchant_controller_test.downloadParcelsWithWeightChanges(req, res)

    // Assert
    expect(res.statusCode).toEqual(200)
  })
});

describe('test bookSplitedParcels function', () => {
  test('should book splited parcels successfully', async () => {
    // Arrange
    let req = {
      merchant: {
        merchant_account_number: 'encrypted_account_number',
        merchant_name: 'merchantName'
      },
      body: [
        { 
          tracking_id: 'trackingId1',
          createdBy: 'createdBy1',
          merchant_account_number: 'account_number',
          parent_id: 'parentId1',
          tracking_status:[
            {
              date: 'date'
            }
          ]
        },
        {
          tracking_id: 'trackingId2',
          createdBy: 'createdBy2',
          merchant_account_number: 'account_number',
          parent_id: 'parentId1',
          tracking_status:[
            {
              date: 'date'
            }
          ]
        }
      ],
      decoded: {
        emails: ['<EMAIL>']
      }
    };

    const payload = [
      { 
        createdBy: 'createdBy1',
        merchant_account_number: 'account_number',
        parent_id: 'parentId1',
        tracking_status:[
          {
            date: 'date'
          }
        ]
      },
      {
        createdBy: 'createdBy2',
        merchant_account_number: 'account_number',
        parent_id: 'parentId1',
        tracking_status:[
          {
            date: 'date'
          }
        ]
      }
    ]

    req.decoded['signInNames.emailAddress'] = '<EMAIL>';

    const jsonFn = jest.fn();
    const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
    };

    aesUtils.CrtCounterDecrypt = jest.fn().mockImplementation((value) => value.replace('encrypted_', ''));
    manifestItemService.patch = jest.fn().mockResolvedValue({});
    BookingService.orderMultiParcels = jest.fn().mockResolvedValue([[], req.body]);
    NotificationService.createNotification = jest.fn().mockResolvedValue({});

    // Act
    await merchant_controller_test.bookSplitedParcels(req, res);

    // Assert
    expect(manifestItemService.patch).toHaveBeenCalled();
    expect(BookingService.orderMultiParcels).toHaveBeenCalledWith(payload, req.merchant, 'splittedParcel', mockDate);
    expect(NotificationService.createNotification).toHaveBeenCalledWith(
      req.merchant,
      '<EMAIL>',
      {
        type: 'multi_piece_process_completed',
        title: 'MULTI-PIECE PROCESS COMPLETED',
        content: 'Parcel trackingId1 has been split.',
        batchNo: undefined,
      }
    );
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      success: true,
      bookedParcels: req.body,
      rejectedParcels: []
    });
  });

  test('should handle error when booking splited parcels', async () => {
    // Arrange
    const req = {
      merchant: {
        merchant_account_number: 'encrypted_account_number',
        merchant_name: 'merchantName'
      },
      body: [
        {
          tracking_id: 'trackingId1',
          createdBy: 'encrypted_createdBy1',
          parent_id: 'parentId1'
        }
      ],
      decoded: {
        emails: ['<EMAIL>']
      }
    };

    const jsonFn = jest.fn();
    const res = {
      json: jsonFn,
      status: jest.fn().mockReturnValue({ json: jsonFn })
    };

    aesUtils.CrtCounterDecrypt = jest.fn().mockImplementation((value) => value.replace('encrypted_', ''));
    manifestItemService.patch = jest.fn().mockResolvedValue({});
    BookingService.orderMultiParcels = jest.fn().mockRejectedValue(new Error('Booking error'));
    NotificationService.createNotification = jest.fn().mockResolvedValue({});

    // Act
    await merchant_controller_test.bookSplitedParcels(req, res);

    // Assert
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      message: 'Booking error',
    });
  });
});

describe('test getPudoPoints', () => {
  const req = { 
    merchant: {
      name: 'merchant'
    }, 
    params: {
      country: 'singapore',
      postalCode: '123456'
    }
  };
  const jsonFn = jest.fn();
  const res = {
    json: jsonFn,
    status: jest.fn().mockReturnValue({ json: jsonFn })
  };
  
  it('should get PUDO points as expected', async () => {
    // Arrange
    const expected = {
      success: true,
      statusCode: 200,
      points: []
    };
    CollectionPointService.getPudoPoints = jest.fn().mockResolvedValueOnce(expected);

    // Act
    await merchant_controller_test.getPudoPoints(req, res);

    // Assert
    expect(res.status).toHaveBeenCalledWith(expected.statusCode);
    expect(res.json).toHaveBeenCalledWith({
      success: true,
      data: expected.points
    });
  });

  it('should return false when failed getting PUDO points', async () => {
    // Arrange
    const expected = {
      success: false,
      statusCode: 400,
      error: [
        {
          message: 'Client error'
        }
      ]
    };
    CollectionPointService.getPudoPoints = jest.fn().mockResolvedValueOnce(expected);

    // Act
    await merchant_controller_test.getPudoPoints(req, res);

    // Assert
    expect(res.status).toHaveBeenCalledWith(expected.statusCode);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      error: [
        {
          message: expected.error[0].message
        }
      ]
    });
  });

  it('should handle unexpected error', async () => {
    // Arrange
    CollectionPointService.getPudoPoints = jest.fn().mockImplementation(() => {
      throw new Error('Unexpected error!');
    });

    // Act
    await merchant_controller_test.getPudoPoints(req, res);

    // Assert
    expect(res.status).toHaveBeenCalledWith(500);
    expect(res.json).toHaveBeenCalledWith({
      success: false,
      error: [{
        message: 'Something went wrong',
        type: 'Internal Error' 
      }]
    });
  });
});
