const NotificationService = require('../services/notification-service');
const AzureBlobStorage = require('../utilities/azure-blob-storage');
const {azureStorageContainer} = require('../config');
const { getParcelsBuffer } = require('../services/booking/booking-service');

class NotificationController {
  async getNotifications(req, res) {
    try {
      const merchantName = req.merchant.merchant_name;
      const {offset, limit} = req.query;
      const result = await NotificationService.getNotificationsPaging(offset, limit, merchantName);
      return res.status(200).json({success: true, data: result});
    } catch (err) {
      console.log('NotificationController.getNotifications error', err);
      return res.status(500).json(err);
    }
  }

  async updateNotification(req, res) {
    try {
      const merchantName = req.merchant.merchant_name;
      const { ids, ...payload } = req.body;
      for (const id of ids) {
        await NotificationService.updateNotification(merchantName, id, payload);
      }
      return res.status(200).json({success: true});
    } catch (err) {
      console.log('NotificationController.updateReadNotifications error', err);
      return res.status(500).json(err);
    }
  }

  async addNewNotification(req, res) {
    try {
      const { merchant } = req;
      const userEmail = req.decoded['signInNames.emailAddress'];
      const newItem = req.body;
      const { rejectedCSVBuffer } = getParcelsBuffer([], newItem.fileData);
      delete newItem.fileData;

      const result = await NotificationService.createNotification(merchant, userEmail, newItem, rejectedCSVBuffer);
      return res.status(200).json({success: true, data: result});
    } catch (err) {
      console.log('NotificationController.addNewNotifications error', err);
      return res.status(500).json(err);
    }
  }

  async getNotificationBlob(req, res) {
    const id = req.params.id;

    try {
      const latestFile = await AzureBlobStorage.getLatestFileByTimestamp(id, '', azureStorageContainer.NOTIFICATION);

      if (!latestFile) {
        res.json({success: false, message: `Blob for notification ${id} not found.`});
      } else {
        const blob = await AzureBlobStorage.downloadFile(latestFile.name, azureStorageContainer.NOTIFICATION);
        blob.pipe(res);
      }
    } catch (err) {
      console.log('NotificationController.getNotificationBlob error', id, err);
      res.json({success: false, message: err.message});
    }
  }
}

module.exports = NotificationController;
