const aesUtils = require('../models/aesUtils');
const csvtojson = require('csvtojson');
const ReportUtils = require('../utilities/reportUtils');
const TrackingNoService = require('../services/tracking-no-services');
const { groupBy } = require('lodash');
const { 
  bookingPlatform, 
  notificationTypes, 
  functionName,
  parcelStatus,
  serviceOptions,
  EXT
} = require('../const/enum');
const nameUtils = require('../utilities/merchantNameUtils');
const validationUtils = require('../utilities/validationUtils');
const bookingService = require('../services/booking-service');
const BookingServiceNew = require('../services/booking/booking-service');
const NotificationService = require('../services/notification-service');
const {formatBatchNo} = require('../report/report.utils');
const XLSX = require('xlsx');
const { error, info } = require('../utilities/logUtils');
const userSessionService = require('../services/user-session-service');
const AzureBlobStorage = require('../utilities/azure-blob-storage');
const { azureStorageContainer } = require('../config');
const manifestItemService = require('../services/manifest-item-services');
const StatusMappingService = require('../services/statusMappingService');
const { chunkArrayGenerator } = require('../utilities/asyncUtils');
const {batchBookingSize} = require('../config');
const CollectionPointService = require('../services/collection_point_service');
const rateMerchantServices = require('../services/rate-merchant-services');
const rateMerchantValidation = require('../utilities/rateMerchantValidation');
const downloadLabelService = require('../services/download-labels-service');
function MerchantController(merchantDao) {
  this.merchantDao = merchantDao;
}

MerchantController.prototype = {
  /**
   * Get parcels from request body or uploaded CSV then normalize them
   */
  getParcels: async function (request, isShopifyBooking = false) {
    const formidable = (await import('formidable')).default;
    const form = formidable({});
    const getUploadFile = () => {
      return new Promise((resolve, reject) => {
        form.parse(request, async function (err, fields, files) {
          if (err) {
            reject(err);
          } else if (!files.file) {
            reject(new Error('Please upload CSV file.'));
          } else {
            resolve(files.file[0]);
          }
        });
      });
    };

    let parcels;
    if (Array.isArray(request.body)) {
      parcels = request.body;
    } else if (isShopifyBooking) {
      parcels = request.body.orders;
    } else {
      const fileContent = await getUploadFile();
      const {originalFilename, filepath} = fileContent;
      if (originalFilename.toLowerCase().endsWith('.csv')) {
        parcels = await csvtojson().fromFile(filepath);
      }
      if (originalFilename.toLowerCase().endsWith('.xlsx')) {
        const workbook = XLSX.readFile(filepath);
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const csvData = XLSX.utils.sheet_to_csv(worksheet);
        parcels = await csvtojson().fromString(csvData);
      }

      // Remove items with all fields empty
      parcels = parcels.filter(parcel => !validationUtils.isEmptyValuesObj(parcel));
      parcels.forEach((parcel) => parcel.csvFileName = originalFilename);
    }

    return parcels;
  },

  login: async function (req, res) {
    const self = this;
    const email = req.decoded['signInNames.emailAddress'];
    const querySpec = {
      query: `SELECT c.merchant_name, c.merchant_type, d.name FROM c
            JOIN d IN c.list_merchant_account 
            WHERE d.email = @email AND IS_DEFINED(d.id) AND (NOT IS_DEFINED(d.isDeleted) OR d.isDeleted = @isDeleted) AND d.accountEnabled = @accountEnabled`,
      parameters: [
        {
          name: '@email',
          value: aesUtils.CrtCounterEncrypt(email)
        },
        {
          name: '@isDeleted',
          value: false
        },
        {
          name: '@accountEnabled',
          value: true
        }
      ]
    };
    try {
      const merchantInfo = await self.merchantDao.find(querySpec);
      let accountNameDecrypted = 'N/A';
      let merchantNameDecrypted = 'N/A';
      if (!merchantInfo?.length) {
        res.status(401).send({ error: 'User not found!' });
        return;
      }
      merchantNameDecrypted = aesUtils.CrtCounterDecrypt(merchantInfo[0].merchant_name);
      accountNameDecrypted = aesUtils.CrtCounterDecrypt(merchantInfo[0].name);

      /* After the user is authenticated, Set user session manually */
      req.session.userId = req.decoded.sub;
      req.session.session_id = req.sessionID;
      await userSessionService.set({
        user_id: req.decoded.sub,
        session_id: req.sessionID
      });
      
      res.status(200).send({
        message: 'Login successfully',
        merchantName: merchantNameDecrypted,
        merchantType: merchantInfo[0].merchant_type,
        accountName: nameUtils.capitalizeName(accountNameDecrypted),
        initials: accountNameDecrypted === 'N/A' ? 'N/A' : nameUtils.get2FirstInitial(accountNameDecrypted)
      });
    } catch (err) {
      console.log('<merchant_controller> - <login>', err);
      res.status(401).send({ error: 'Error when retrieving login info - ' + err });
    }
  },

  async makeSingleBooking(req, res) {
    const { merchant, body: reqParcel } = req;
    req.parcel = reqParcel;

    try {
      const [validateResult, parcel] = await BookingServiceNew.validateSingleParcel(
        reqParcel, 
        merchant, 
        reqParcel.booking_platform || bookingPlatform.api
      );
      req.parcel = parcel;
      if (validateResult.hasError()) {
        res.error = validateResult.errors;
        info(functionName.BOOKING, 'makeSingleBooking validation error', validateResult.errors, parcel);
        BookingServiceNew.createRejectedShipments([{ ...parcel, errors: validateResult.errors }]);
        res.status(422).json({
          success: false,
          error: validateResult.errors
        });
        return;
      }

      const seqNo = await TrackingNoService.getSequenceValue(merchant.merchant_account_number, 1);
      if (reqParcel.service_option === serviceOptions.B2B) {
        const result = await BookingServiceNew.orderParcel(parcel, seqNo, merchant);
        await downloadLabelService.generateGenericLabel(parcel, merchant);

        info(functionName.BOOKING, 'makeSingleBooking success', parcel);
        res.json({
          data: result
        });
        return;
      }
      //B2C booking
      const response = await BookingServiceNew.orderParcel(parcel, seqNo, merchant);
      req.parcel = parcel;
      if (response.httpStatusCode) {
        if (!response.success) {
          res.error = response.data || response.message;
          error(functionName.BOOKING, 'makeSingleBooking order error', response.message, parcel);
        }
        res.status(response.httpStatusCode).json({
          success: response.success,
          error: response.data ? [response.data] : response.message
        });
      } else {
        info(functionName.BOOKING, 'makeSingleBooking success', parcel);
        res.json({
          data: response
        });
      }
    } catch (err) {
      res.error = err;
      error(functionName.BOOKING, 'makeSingleBooking exception', err, reqParcel);
      res.status(500).json({
        success: false,
        message: err.message
      });
    }
  },

  /**
   * Validate and booking for multiple parcels, support both CSV and JSON payload
   * @param req
   * query.validateOnly: true - only validate, false - validate then book
   * query.timeout = 15s: if processing time exceed timeout setting, will send Processing response to UI,
   *   continue process then push notification at the end
   * @param res
   */
  makeMultiBooking: async function (req, res) {
    const isShopifyBooking = req.body?.booking_platform === bookingPlatform.shopify;
    const selectedBookingPlatform = isShopifyBooking ? bookingPlatform.shopify : bookingPlatform.newMerchantPortal;
    const userEmail = isShopifyBooking ? '' : req.decoded['signInNames.emailAddress'];
    const {merchant} = req;
    const {validateOnly, timeout = 15_000} = req.query;

    // Notify UI that process with take time so that UI doesn't need to wait
    res.setTimeout(timeout, () => res.json({
      success: true,
      message: {
        title: 'BOOKING IN PROGRESS',
        content: 'Your bookings are being processed. We will notify you once the upload is completed.'
      }
    }));

    try {
      const parcels = await this.getParcels(req, isShopifyBooking);
      const csvFileName = parcels[0].csvFileName;

      const [rejectedParcels, validatedParcels] = await BookingServiceNew.validateMultiParcels(
        parcels,
        merchant,
        selectedBookingPlatform,
        userEmail,
        req
      );
      if (rejectedParcels.length) {
        info(functionName.BOOKING, 'UI makeMultiBooking validation error', rejectedParcels);
        BookingServiceNew.createRejectedShipments(rejectedParcels);
      }
      if (res.headersSent) {
        if (rejectedParcels.length > 0) {
          // If UI timeout and has validation error, then push notification with validation result
          await NotificationService.createNotification(merchant, userEmail, {
            type: notificationTypes.VALIDATION_ERROR,
            title: `${csvFileName} ERROR`,
            content: 'Please amend your bookings.',
          }, JSON.stringify({rejectedParcels, validatedParcels})
          );
          return;
        }
      } else if (validateOnly === 'true' || rejectedParcels.length > 0) {
        // If UI not timeout and has error or UI request to validate only, then send API response
        res.json({ success: true, validatedParcels, rejectedParcels });
        return;
      }

      let errorParcels = [], bookedParcels = [];
      const bookingTime = new Date();
      for (const chunkOfParcels of chunkArrayGenerator(validatedParcels, batchBookingSize)) {
        const [errorParcelsChunk, bookedParcelsChunk] =
          await BookingServiceNew.orderMultiParcels(
            chunkOfParcels,
            merchant,
            'multiBookings',
            bookingTime
          );
        errorParcels = errorParcels.concat(errorParcelsChunk);
        bookedParcels = bookedParcels.concat(bookedParcelsChunk);
        info(functionName.BOOKING, 
          `Proceed batch, merchant=${merchant.merchant_name}, total=${validatedParcels.length}, success=${bookedParcels.length}, error=${errorParcels.length}, duration=${new Date().getTime() - bookingTime.getTime()}, uploadFile=${csvFileName}`);
      }
      
      if (!res.headersSent) {
        // If UI not timeout, then send API response
        res.json({
          success: true,
          bookedParcels: bookedParcels.map(item => ({ 
            id: item.id, 
            PLS_batch_no: item.PLS_batch_no, 
            origin_country: item.origin_country, 
            country: item.country,
            tracking_id: item.tracking_id,
          })),
          rejectedParcels: errorParcels
        });
      } else {
        // If UI timeout, then push notifications with batch info
        const batches = groupBy(bookedParcels, 'PLS_batch_no');
        for (const [batchNo, batchItems] of Object.entries(batches)) {
          await NotificationService.createNotification(merchant, userEmail, {
            type: notificationTypes.BOOKED,
            title: `${formatBatchNo(batchNo)} ${batchItems.length}/${parcels.length} BOOKED`,
            content: 'Your bookings have been completed. Proceed to Booking Detail page to print your labels.',
            batchNo,
            batchItems: batchItems.map(item => item.id)
          }
          );
        }
      }

      const rejectedErrors = errorParcels.map(item => ({
        order_number: item.order_number || `Row ${item.index}`,
        merchant_batch_order_no: item.errors.map(err => `${err.param}_${err.msg}`).join('; ')
      }));
      const result = ReportUtils.exportBookingResultToCSV(bookedParcels, errorParcels, rejectedErrors);
      if (process.env.IS_NOTIFY_BOOKING_RESULT_MP_2 === 'true') {
        bookingService.notifyBookingResult(result.confirmedCSVBuffer, result.rejectedCSVBuffer, userEmail);
      }

      if (errorParcels.length) {
        error(functionName.BOOKING, 'UI makeMultiBooking order error', errorParcels);
        // If has booking error, then push notification with rejected CSV
        await NotificationService.createNotification(merchant, userEmail, {
          type: notificationTypes.BOOKING_ERROR,
          title: `${formatBatchNo(errorParcels[0].PLS_batch_no) || 'BOOKING'} ERROR`,
          content: 'Please amend your bookings.',
        },
        result.rejectedCSVBuffer
        );
      }

      if (bookedParcels.length > 0) {
        info(functionName.BOOKING, 'UI makeMultiBooking order success', bookedParcels);
        // Create notification for CSV labels and report but disable, have web job to scan status and enable
        await NotificationService.createNotification(merchant, userEmail, {
          type: notificationTypes.CSV_BOOKING_REPORT,
          title: `${csvFileName} LABEL(S) & REPORT`,
          content: 'Your label(s) and report are ready for download.',
          csvFileName,
          bookingTime: bookedParcels[0].tracking_status[0].date,
          isActive: false
        });
      }
    } catch (err) {
      error(functionName.BOOKING, 'makeMultiBooking exception', err);
      if (!res.headersSent) {
        res.json({
          success: false,
          message: err.message
        });
      }
    }
  },

  bookSplitedParcels: async function (req, res) {
    try {
      const { merchant, body } = req; 
      const trackingId = body[0].tracking_id;
  
      const parcels = body.map((parcel, index) => {
        const { tracking_id, ...parcelData } = parcel;
        const payload = {
          ...parcelData,
          merchant_account_number: aesUtils.CrtCounterDecrypt(merchant.merchant_account_number),
          createdBy: aesUtils.CrtCounterDecrypt(parcel.createdBy),
        };
        info(
          functionName.BOOKING, 
          'merchant_controller bookSplitedParcels', 
          `The payload number ${index + 1} for tracking_id ${tracking_id} is: `, 
          payload
        );
        return payload;
      });
      const parentId = parcels[0].parent_id;

      // update parcel as parent and its tracking status
      const splitCreatedStt = StatusMappingService.getManifestStatus(parcelStatus.split_parcel_created);
      await manifestItemService.patch({
        id: parentId,
        parent_id: parentId,
        latest_tracking_status: splitCreatedStt,
        tracking_status: [{
          status: splitCreatedStt,
          date: new Date(),
        }],
      });

      info(functionName.BOOKING, 'merchant_controller bookSplitedParcels', parcels, merchant);

      let errorParcels = [], bookedParcels = [];
      const bookingTime = new Date();
      for (const chunkOfParcels of chunkArrayGenerator(parcels, batchBookingSize)) {
        const [errorParcelsChunk, bookedParcelsChunk] = 
          await BookingServiceNew.orderMultiParcels(chunkOfParcels, merchant, 'splittedParcel', bookingTime);
        errorParcels = errorParcels.concat(errorParcelsChunk);
        bookedParcels = bookedParcels.concat(bookedParcelsChunk);
        info(functionName.BOOKING, 
          `Proceed batch, merchant=${merchant.merchant_name}, total=${parcels.length}, success=${bookedParcels.length}, error=${errorParcels.length}, duration=${new Date().getTime() - bookingTime.getTime()}, parentId=${parentId}`);
      }
  
      await NotificationService.createNotification(merchant, req.decoded['signInNames.emailAddress'], {
        type: notificationTypes.MULTI_PIECE_PROCESS_COMPLETED,
        title: 'MULTI-PIECE PROCESS COMPLETED',
        content: `Parcel ${trackingId} has been split.`,
        batchNo: bookedParcels[0]?.PLS_batch_no || errorParcels[0]?.PLS_batch_no,
      });

      if (bookedParcels?.length > 0) {
        await NotificationService.createNotification(merchant, req.decoded['signInNames.emailAddress'], {
          type: notificationTypes.CSV_BOOKING_REPORT,
          title: `${bookedParcels.length} Labels from multi-piece processing is ready`,
          content: 'Your label(s) and report are ready for download.',
          bookingTime: bookedParcels[0]?.tracking_status[0].date,
          parentId: parentId,
          isLabelReady: false,
        });
      }
  
      res.status(200).json({
        success: true,
        bookedParcels,
        rejectedParcels: errorParcels
      });
    } catch (err) {
      error(functionName.BOOKING, 'merchant_controller bookSplitedParcels', err);
      res.status(500).json({
        success: false,
        message: err.message,
      });
    }
  },

  downloadParcelsWithWeightChanges: async function (req, res) {
    const dayOfWeightChanges = req.query.dayOfWeightChanges;
    const fileName = `Presco_${dayOfWeightChanges}`;
    try {
      const blob = await AzureBlobStorage.getLatestFileByTimestamp(
        fileName,
        EXT.ZIP,
        azureStorageContainer.WEIGHT_CHANGES_FILES);
      if (!blob) {
        throw new Error(`Cannot find file ${fileName} in blob storage!`);
      } else {
        res.statusCode = 200;
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', 
          `attachment; filename="${fileName}"`);
        blob.pipe(res);
        info('MerchantController.downloadParcelsWithWeightChanges',
          `Successfully download latest file ${fileName} from blob storage`
        );
      }
    } catch (err) {
      error('MerchantController.downloadParcelsWithWeightChanges',
        `Cannot download file ${fileName} for merchant PRESCO`,
        `Error: ${err}`
      );
      res.status(500).json({
        success: false,
        message: 'Something went wrong when download file',
        errorMessage: `${err.message || err}`
      });
    }
  },

  getPudoPoints: async function (req, res) {
    try {
      const { country, postalCode } = req.params;
      const result = await CollectionPointService.getPudoPoints(country, postalCode);
      const { success, statusCode, error: err, points } = result;

      if (!result.success) {
        error(
          'MerchantController.getPudoPoints',
          'Error while getting PUDO points',
          err.message
        );
        
        return res.status(statusCode).json({ success, error: err });
      }

      info(
        'MerchantController.getPudoPoints',
        'Get PUDO points successfully',
        `Country: ${country}`,
        `Postal Code: ${postalCode}`
      );

      return res.status(statusCode).json({ success, data: points });
    } catch (err) {
      error(
        'MerchantController.getPudoPoints',
        'Error while getting PUDO points',
        err.message
      );
      
      return res.status(500).json({
        success: false,
        error: [{
          message: 'Something went wrong',
          type: 'Internal Error' 
        }]
      });
    }
  },

  calculatePotentialShippingFee: async function (req, res) {
    try {
      info(functionName.ESTIMATE_RATE, 'calculatePotentialShippingFee request body', req.body);

      const [ validateResults, merchant ] = await rateMerchantValidation.validatePayload(req.body);

      if (validateResults.hasError()) {
        res.error = validateResults.errors;
        return res.status(422).json({
          success: false,
          error: validateResults.errors
        });
      }

      const [ rates, updatedPayload ] = await rateMerchantServices.getEstimateRateForMerchant(merchant, req.body);

      info(functionName.ESTIMATE_RATE, 'calculatePotentialShippingFee rate info', rates, updatedPayload);

      return res.status(200).json({
        ...req.body,
        estimated_rate: rates,
        estimated_rate_currency: updatedPayload.invoiceRateCurrency
      });
    } catch(err) {
      res.error = err;
      error(functionName.ESTIMATE_RATE, 'calculatePotentialShippingFee exception', err, req.body);
      res.status(500).json({
        success: false,
        message: err.message
      });
    }
  }
};

module.exports = MerchantController;